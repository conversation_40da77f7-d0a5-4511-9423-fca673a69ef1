package com.stt.android.diary.progress

import android.content.Context
import android.os.SystemClock
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.data.toEpochMilli
import com.stt.android.diary.progress.ProgressViewEvent.FitnessHighlighted
import com.stt.android.diary.progress.ProgressViewEvent.FitnessPageUpdated
import com.stt.android.diary.progress.ProgressViewEvent.TimeRangeToggled
import com.stt.android.diary.progress.ProgressViewEvent.Vo2MaxHighlighted
import com.stt.android.diary.progress.ProgressViewEvent.Vo2MaxPageUpdated
import com.stt.android.diary.progress.ProgressViewEvent.ModuleFullyVisible
import com.stt.android.diary.progress.usecase.CreateCtlRampRateViewDataUseCase
import com.stt.android.diary.progress.usecase.CreateFitnessChartViewDataUseCase
import com.stt.android.diary.progress.usecase.CreateFitnessHighlightedViewDataUseCase
import com.stt.android.diary.progress.usecase.CreateFitnessViewDataUseCase
import com.stt.android.diary.progress.usecase.CreateVo2MaxChartViewDataUseCase
import com.stt.android.diary.progress.usecase.CreateVo2MaxHighlightedViewDataUseCase
import com.stt.android.diary.progress.usecase.CreateVo2MaxProgressViewDataUseCase
import com.stt.android.diary.progress.usecase.dateRange
import com.stt.android.diary.progress.usecase.pageCount
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.eventtracking.EventTracker
import com.stt.android.ui.utils.TextFormatter
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted.Companion.Lazily
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.drop
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject

@HiltViewModel
internal class ProgressViewModel @Inject constructor(
    @param:ApplicationContext private val context: Context,
    private val createFitnessViewDataUseCase: CreateFitnessViewDataUseCase,
    private val createFitnessChartViewDataUseCase: CreateFitnessChartViewDataUseCase,
    private val createCtlRampRateViewDataUseCase: CreateCtlRampRateViewDataUseCase,
    private val createVo2MaxProgressViewDataUseCase: CreateVo2MaxProgressViewDataUseCase,
    private val createVo2MaxChartViewDataUseCase: CreateVo2MaxChartViewDataUseCase,
    private val createFitnessHighlightedViewDataUseCase: CreateFitnessHighlightedViewDataUseCase,
    private val createVo2MaxHighlightedViewDataUseCase: CreateVo2MaxHighlightedViewDataUseCase,
    userSettingsController: UserSettingsController,
    private val dispatchers: CoroutinesDispatchers,
    private val eventTracker: EventTracker,
) : ViewModel() {

    private val today = LocalDate.now()
    private val firstDayOfTheWeek = userSettingsController.settings.firstDayOfTheWeek

    private val _viewData = MutableStateFlow<ProgressViewData>(ProgressViewData.Initial)
    val viewData = _viewData.asStateFlow()

    private val _pageIndex = MutableStateFlow(0)

    private var viewDurationAccumulatedMs: Long = 0L
    private var viewDurationStartedAtMs: Long? = null
    private val fullyVisibleModuleNames: MutableSet<String> = linkedSetOf()
    
    private val appLifecycleObserver = object : DefaultLifecycleObserver {
        override fun onStart(owner: LifecycleOwner) {
            startViewDuration()
        }

        override fun onStop(owner: LifecycleOwner) {
            stopAndReportViewDuration()
        }
    }

    private fun getFitnessViewData(index: Int): FitnessChartViewData {
        return (_viewData.value as? ProgressViewData.Loaded)?.fitnessChartViewDataMap?.get(index)?.value
            ?: FitnessChartViewData.None
    }

    private fun getVo2MaxViewData(index: Int): Vo2MaxChartViewData {
        return (_viewData.value as? ProgressViewData.Loaded)?.vo2MaxChartViewDataMap?.get(index)?.value
            ?: Vo2MaxChartViewData.None
    }

    init {
        ProcessLifecycleOwner.get().lifecycle.addObserver(appLifecycleObserver)
        if (ProcessLifecycleOwner.get().lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED)) {
            startViewDuration()
        }
        
        loadData()
        _pageIndex
            .drop(1)
            .onEach { onFitnessPageUpdated(it) }
            .launchIn(viewModelScope)
    }

    private fun loadData(
        timeRange: GraphTimeRange = GraphTimeRange.SIX_WEEKS,
    ) = viewModelScope.launch(dispatchers.io) {
        val ctlRampRateViewData = createCtlRampRateViewDataUseCase(timeRange)
        val pageCount = timeRange.pageCount()
        val (currStart, currEnd) = timeRange.dateRange(0)
        ProgressViewData.Loaded(
            timeRange = timeRange,
            // Make sure fitness is loaded first to ensure it is positioned at the start.
            fitnessViewData = createFitnessViewDataUseCase(timeRange, currStart, currEnd).first()
                .run { MutableStateFlow(this) },
            fitnessChartViewDataMap = buildViewDataPageMap(pageCount - 1, pageCount) { page ->
                createViewData(
                    useCase = createFitnessChartViewDataUseCase::invoke,
                    timeRange = timeRange,
                    page = page,
                    pageCount = pageCount,
                    initialValue = FitnessChartViewData.None,
                )
            },
            fitnessChartPageIndex = pageCount - 1,
            fitnessChartPageCount = pageCount,
            fitnessChartPageStartDate = currStart.formatDate(),
            fitnessChartPageEndDate = currEnd.formatDate(),
            ctlRampRateViewData = ctlRampRateViewData,
            vo2MaxProgressViewData = createVo2MaxProgressViewDataUseCase(),
            vo2MaxChartViewDataMap = buildViewDataPageMap(pageCount - 1, pageCount) { page ->
                createViewData(
                    useCase = createVo2MaxChartViewDataUseCase::invoke,
                    timeRange = timeRange,
                    page = page,
                    pageCount = pageCount,
                    initialValue = Vo2MaxChartViewData.None,
                )
            },
            vo2MaxChartPageIndex = pageCount - 1,
            vo2MaxChartPageCount = pageCount,
            vo2MaxChartPageStartDate = currStart.formatDate(),
            vo2MaxChartPageEndDate = currEnd.formatDate(),
        ).let { _viewData.tryEmit(it) }
    }

    fun onEvent(event: ProgressViewEvent) {
        when (event) {
            is TimeRangeToggled -> {
                onTimeRangeToggled(event.timeRange)
            }

            is FitnessPageUpdated -> {
                // reduce duplicated page updates
                _pageIndex.tryEmit(event.page)
            }

            is FitnessHighlighted -> {
                onFitnessHighlighted(event.page, event.index)
            }

            is Vo2MaxPageUpdated -> {
                onVo2MaxPageUpdated(event.page)
            }

            is Vo2MaxHighlighted -> {
                onVo2MaxHighlighted(event.page, event.index)
            }

            is ModuleFullyVisible -> {
                onModuleFullyVisible(event.moduleName)
            }
        }
    }

    private fun onTimeRangeToggled(
        timeRange: GraphTimeRange,
    ) = viewModelScope.launch(dispatchers.io) {
        stopAndReportViewDuration()
        val pageCount = timeRange.pageCount()
        val (currStart, currEnd) = timeRange.dateRange(0)
        _viewData.update { viewData ->
            (viewData as? ProgressViewData.Loaded)?.copy(
                timeRange = timeRange,
                fitnessViewData = createFitnessViewDataUseCase(timeRange, currStart, currEnd)
                    .stateIn(viewModelScope, Lazily, viewData.fitnessViewData.value),
                fitnessChartViewDataMap = buildViewDataPageMap(pageCount - 1, pageCount) { page ->
                    // Use the previous fitness view data as initial value to avoid ui jumping
                    createViewData(
                        useCase = createFitnessChartViewDataUseCase::invoke,
                        timeRange = timeRange,
                        page = page,
                        pageCount = pageCount,
                        initialValue = if (page == pageCount - 1) {
                            getFitnessViewData(viewData.fitnessChartPageIndex)
                        } else {
                            FitnessChartViewData.None
                        },
                    )
                },
                fitnessChartPageIndex = pageCount - 1,
                fitnessChartPageCount = pageCount,
                fitnessChartPageStartDate = currStart.formatDate(),
                fitnessChartPageEndDate = currEnd.formatDate(),
                ctlRampRateViewData = createCtlRampRateViewDataUseCase(timeRange),
                vo2MaxChartViewDataMap = buildViewDataPageMap(pageCount - 1, pageCount) { page ->
                    // Use the previous vo2Max view data as initial value to avoid ui jumping
                    createViewData(
                        useCase = createVo2MaxChartViewDataUseCase::invoke,
                        timeRange = timeRange,
                        page = page,
                        pageCount = pageCount,
                        initialValue = if (page == pageCount - 1) {
                            getVo2MaxViewData(viewData.vo2MaxChartPageIndex)
                        } else {
                            Vo2MaxChartViewData.None
                        },
                    )
                },
                vo2MaxChartPageIndex = pageCount - 1,
                vo2MaxChartPageCount = pageCount,
                vo2MaxChartPageStartDate = currStart.formatDate(),
                vo2MaxChartPageEndDate = currEnd.formatDate(),
            ) ?: ProgressViewData.Initial
        }
        startViewDuration()
    }

    private fun onFitnessPageUpdated(page: Int) = viewModelScope.launch(dispatchers.io) {
        _viewData.update { viewData ->
            if (viewData is ProgressViewData.Loaded) {
                val timeRange = viewData.timeRange
                val pageCount = viewData.fitnessChartPageCount
                val (currStart, currEnd) = timeRange.dateRange(page, pageCount)
                viewData.copy(
                    fitnessChartViewDataMap = buildViewDataPageMap(page, pageCount) { index ->
                        viewData.fitnessChartViewDataMap[index] ?: createViewData(
                            useCase = createFitnessChartViewDataUseCase::invoke,
                            timeRange = timeRange,
                            page = index,
                            pageCount = pageCount,
                            initialValue = FitnessChartViewData.None,
                        )
                    },
                    fitnessChartPageIndex = page,
                    fitnessChartPageStartDate = currStart.formatDate(),
                    fitnessChartPageEndDate = currEnd.formatDate(),
                )
            } else ProgressViewData.Initial
        }
    }

    private fun onFitnessHighlighted(page: Int, index: Int?) = _viewData.update { viewData ->
        (viewData as? ProgressViewData.Loaded)?.copy(
            fitnessHighlightedIndex = index,
            fitnessHighlightedViewData = createFitnessHighlightedViewDataUseCase(
                viewData = viewData.fitnessChartViewDataMap[page]
                    ?: flowOf(FitnessChartViewData.None),
                index = index,
            )
        ) ?: ProgressViewData.Initial
    }

    private fun onVo2MaxPageUpdated(page: Int) = viewModelScope.launch(dispatchers.io) {
        _viewData.update { viewData ->
            if (viewData is ProgressViewData.Loaded) {
                val timeRange = viewData.timeRange
                val pageCount = viewData.vo2MaxChartPageCount
                val (currStart, currEnd) = timeRange.dateRange(page, pageCount)
                viewData.copy(
                    vo2MaxChartViewDataMap = buildViewDataPageMap(page, pageCount) { index ->
                        viewData.vo2MaxChartViewDataMap[index] ?: createViewData(
                            useCase = createVo2MaxChartViewDataUseCase::invoke,
                            timeRange = timeRange,
                            page = index,
                            pageCount = pageCount,
                            initialValue = Vo2MaxChartViewData.None,
                        )
                    },
                    vo2MaxChartPageIndex = page,
                    vo2MaxChartPageStartDate = currStart.formatDate(),
                    vo2MaxChartPageEndDate = currEnd.formatDate(),
                )
            } else ProgressViewData.Initial
        }
    }

    private fun onVo2MaxHighlighted(page: Int, index: Int?) = _viewData.update { viewData ->
        (viewData as? ProgressViewData.Loaded)?.copy(
            vo2MaxHighlightedIndex = index,
            vo2MaxHighlightedViewData = createVo2MaxHighlightedViewDataUseCase(
                viewData = viewData.vo2MaxChartViewDataMap[page]
                    ?: flowOf(Vo2MaxChartViewData.None),
                index = index,
            )
        ) ?: ProgressViewData.Initial
    }

    private fun onModuleFullyVisible(moduleName: String) {
        fullyVisibleModuleNames.add(moduleName)
    }

    private fun startViewDuration() {
        if (viewDurationStartedAtMs == null) {
            viewDurationStartedAtMs = SystemClock.elapsedRealtime()
        }
    }

    private fun stopAndReportViewDuration() {
        val startedAt = viewDurationStartedAtMs ?: return
        val elapsed = SystemClock.elapsedRealtime() - startedAt
        viewDurationAccumulatedMs += elapsed
        viewDurationStartedAtMs = null

        val totalSeconds = (viewDurationAccumulatedMs / 1000).toInt()
        if (totalSeconds <= 0) return

        viewDurationAccumulatedMs = 0L

        val module = fullyVisibleModuleNames.toList()
        val currentViewData = _viewData.value
        val timeDim = (currentViewData as? ProgressViewData.Loaded)?.timeRange?.analyticsTimeDim

        timeDim?.takeIf { module.isNotEmpty() }?.let { dim ->
            eventTracker.trackEvent(
                AnalyticsEvent.LEAVE_WIDGETS_DETAIL_PAGE,
                mapOf(
                    AnalyticsEventProperty.WIDGET_NAME to
                        AnalyticsPropertyValue.WidgetDetailPageExposureWidgetNameProperty.PROGRESS,
                    AnalyticsEventProperty.TIME_DIM to dim,
                    AnalyticsEventProperty.WIDGET_DETAIL_BROWSING_DURATION to totalSeconds,
                    AnalyticsEventProperty.MODULE_NAME to module,
                )
            )
        }
    }

    override fun onCleared() {
        ProcessLifecycleOwner.get().lifecycle.removeObserver(appLifecycleObserver)
        stopAndReportViewDuration()
        super.onCleared()
    }

    private inline fun <T> buildViewDataPageMap(
        page: Int,
        pageCount: Int,
        block: (Int) -> StateFlow<T>,
    ) = listOf(page - 1, page, page + 1).filter { it in 0 until pageCount }.associateWith(block)

    private fun <T> createViewData(
        useCase: (GraphTimeRange, LocalDate, LocalDate) -> Flow<T>,
        timeRange: GraphTimeRange,
        page: Int,
        pageCount: Int,
        initialValue: T,
    ): StateFlow<T> {
        val (start, end) = timeRange.dateRange(page, pageCount)
        return useCase(timeRange, start, end).stateIn(viewModelScope, Lazily, initialValue)
    }

    private fun GraphTimeRange.dateRange(page: Int, pageCount: Int) =
        dateRange(pageCount - 1 - page)

    private fun GraphTimeRange.dateRange(offset: Int) =
        dateRange(today, firstDayOfTheWeek, offset.toLong())

    private fun GraphTimeRange.pageCount() = pageCount(today, firstDayOfTheWeek).toInt()

    private fun LocalDate.formatDate(): String {
        val millis = this.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()
        return TextFormatter.formatDate(context, millis, true)
    }

    companion object {
        fun getModuleNameByComponentKey(componentKey: String): String? {
            return when (componentKey) {
                TSS_ANALYSIS_FITNESS_FATIGUE_CHART -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.CHART1
                TSS_ANALYSIS_FORM_CHART -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.CHART2
                TSS_ANALYSIS_PHASE_DESCRIPTIONS -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.INTRODUCTION1
                VO2MAX_CHART -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.CHART3
                CTL_RAMP_RATE -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.DATA_BAR2
                else -> null
            }
        }

        const val TSS_ANALYSIS_FITNESS_FATIGUE_CHART = "tss_analysis_fitness_fatigue_chart"
        const val TSS_ANALYSIS_FORM_CHART = "tss_analysis_form_chart"
        const val VO2MAX_CHART = "vo2max_chart"
        const val CTL_RAMP_RATE = "ctl_ramp_rate"
        const val TSS_ANALYSIS_PHASE_DESCRIPTIONS = "tss_analysis_phase_descriptions"
    }
}
