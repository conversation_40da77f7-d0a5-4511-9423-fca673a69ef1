package com.stt.android.diary.dailyhealth

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.stt.android.compose.modifiers.rememberColumnNestScrollConnection
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.BackToCurrentButton
import com.stt.android.diary.dailyhealth.composables.DailyHealthItem
import com.stt.android.diary.recovery.composables.RecoverySegmentedControl
import com.stt.android.diary.trainingv2.composables.TrainingDatePicker
import com.stt.android.home.diary.InfoBottomSheet

@Composable
fun DailyHealthScreen(
    onShowInfoSheet: (InfoBottomSheet) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: DailyHealthViewModel = hiltViewModel()
) {
    val viewData by viewModel.viewData.collectAsStateWithLifecycle()

    when (viewData) {
        is DailyHealthViewData.Initial -> {
            Box(
                modifier = modifier
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.background),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        }

        is DailyHealthViewData.Loaded -> {
            DailyHealthContent(
                viewData = viewData as DailyHealthViewData.Loaded,
                onEvent = viewModel::onEvent,
                onShowInfoSheet = onShowInfoSheet,
                modifier = modifier
            )
        }
    }
}

@Composable
private fun DailyHealthContent(
    viewData: DailyHealthViewData.Loaded,
    onEvent: (DailyHealthEvent) -> Unit,
    onShowInfoSheet: (InfoBottomSheet) -> Unit,
    modifier: Modifier = Modifier,
) {
    var containerHeightPx by remember { mutableIntStateOf(0) }
    var datePickerHeightPx by remember { mutableIntStateOf(0) }
    val density = LocalDensity.current
    val contentHeightDp by remember(containerHeightPx, datePickerHeightPx) {
        mutableStateOf(density.run { (containerHeightPx - datePickerHeightPx).toDp() })
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .onSizeChanged { size ->
                containerHeightPx = size.height
            }
    ) {
        val listState = rememberLazyListState()
        val containerListState = rememberScrollState()
        val containerNestedScrollConnection =
            rememberColumnNestScrollConnection(containerListState)

        Column(
            modifier = Modifier
                .fillMaxSize()
                .nestedScroll(containerNestedScrollConnection)
                .verticalScroll(containerListState)
        ) {
            RecoverySegmentedControl(
                currentTimeGranularity = viewData.currentTimeGranularity,
                mainTimeGranularities = viewData.mainTimeGranularities,
                extraTimeGranularities = viewData.extraTimeGranularities,
                onTimeGranularityToggled = { granularity ->
                    onEvent(DailyHealthEvent.UpdateTimeGranularity(granularity))
                },
            )

            Column(
                modifier = Modifier
                    .onSizeChanged { size ->
                        datePickerHeightPx = size.height
                    }
            ) {
                Column {
                    TrainingDatePicker(
                        trainingDateRange = viewData.datePickerData.trainingDateRange,
                        onNextClick = { onEvent(DailyHealthEvent.NavigateNext) },
                        onPreviousClick = { onEvent(DailyHealthEvent.NavigatePrevious) },
                        isNextEnabled = viewData.datePickerData.canNavigateForward,
                        isPreviousEnabled = viewData.datePickerData.canNavigateBack,
                        isLoading = viewData.isLoading,
                    )
                    if (listState.canScrollBackward) {
                        HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
                    }
                }
            }

            LazyColumn(
                state = listState,
                modifier = Modifier
                    .height(contentHeightDp)
            ) {
                items(viewData.healthItems) { healthItem ->
                    DailyHealthItem(
                        healthItem = healthItem,
                        chartHighlight = healthItem.chartHighlight,
                        infoClick = if (healthItem.hasInfoButton) {
                            { onShowInfoSheet(it) }
                        } else {
                            null
                        },
                        onEntrySelected = {
                            onEvent(DailyHealthEvent.ShowHighlight(healthItem.chartType, it))
                        },
                        onNoEntrySelected = {
                            onEvent(DailyHealthEvent.HideHighlight)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = MaterialTheme.spacing.medium)
                    )
                }

                if (viewData.datePickerData.canNavigateForward) {
                    item(key = "bottom_spacer") {
                        Spacer(
                            modifier = Modifier.height(
                                56.dp + MaterialTheme.spacing.medium
                            )
                        )
                    }
                }
            }
        }

        AnimatedVisibility(
            visible = viewData.datePickerData.canNavigateForward,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = MaterialTheme.spacing.medium),
            enter = slideInVertically(initialOffsetY = { it * 2 }),
            exit = slideOutVertically(targetOffsetY = { it * 2 }),
        ) {
            M3AppTheme {
                BackToCurrentButton(
                    onClick = { onEvent(DailyHealthEvent.BackToCurrent) },
                )
            }
        }
    }
}
