package com.stt.android.diary.dailyhealth

import com.stt.android.chart.api.model.ChartGranularity

sealed interface DailyHealthEvent {
    data class UpdateTimeGranularity(val timeGranularity: ChartGranularity) : DailyHealthEvent
    data object ShowExtraTimeGranularitySelection : DailyHealthEvent
    data object HideExtraTimeGranularitySelection : DailyHealthEvent
    data class ShowHighlight(val chartType: ChartType, val entryX: Long) : DailyHealthEvent
    data object HideHighlight : DailyHealthEvent
    data object NavigateNext : DailyHealthEvent
    data object NavigatePrevious : DailyHealthEvent
    data object BackToCurrent : DailyHealthEvent
} 
