package com.stt.android.diary.trainingv2.composables

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.layout.positionOnScreen
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.IntOffset
import com.soy.algorithms.impact.WorkoutImpactType
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.screen.components.rememberVisibilityTracker
import com.stt.android.chart.impl.screen.components.trackVisibility
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.modifiers.rememberColumnNestScrollConnection
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyMegaBold
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.BackToCurrentButton
import com.stt.android.core.domain.workouts.CoreActivityGrouping
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.diary.insights.coach.TrainingCoachUiState
import com.stt.android.diary.insights.impact.ImpactUiState
import com.stt.android.diary.insights.intensity.IntensityType
import com.stt.android.diary.insights.intensity.IntensityUiState
import com.stt.android.diary.insights.volume.TrainingHighlighted
import com.stt.android.diary.insights.volume.TrainingVolumeUiState
import com.stt.android.diary.insights.volume.VolumeWorkoutSummaryType
import com.stt.android.diary.summary.composables.OverlappingRow
import com.stt.android.diary.trainingv2.TrainingDateRange
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.home.diary.R
import kotlinx.collections.immutable.toImmutableList
import kotlin.math.roundToInt

@Composable
internal fun TrainingScreen(
    isLoading: Boolean,
    chartGranularity: ChartGranularity,
    graphTimeRange: GraphTimeRange,
    onGraphTimeRangeToggled: (GraphTimeRange) -> Unit,
    hasNext: Boolean,
    hasPrevious: Boolean,
    trainingDateRange: TrainingDateRange,
    onNextClick: () -> Unit,
    onPreviousClick: () -> Unit,
    selectedActivityGroupings: List<CoreActivityGrouping>,
    allDoneActivityTypes: List<CoreActivityType>,
    onSelectSportsClick: () -> Unit,
    trainingCoachUiState: TrainingCoachUiState,
    trainingVolumeUiState: TrainingVolumeUiState,
    onVolumeWorkoutSummaryClick: (VolumeWorkoutSummaryType) -> Unit,
    onVolumeInfoClick: () -> Unit,
    trainingChartHighlighted: TrainingHighlighted?,
    trainingVolumeChartHighlightedEvent: (Long?) -> Unit,
    intensityUiState: IntensityUiState,
    onIntensityInfoClick: () -> Unit,
    onIntensityTypeClick: (IntensityType) -> Unit,
    impactUiState: ImpactUiState,
    onImpactInfoClick: (WorkoutImpactType) -> Unit,
    onWorkoutImpactTypeClick: (WorkoutImpactType) -> Unit,
    onTrainingModelInfoClick: () -> Unit,
    onBackToCurrentClick: () -> Unit,
    onModuleFullyVisible: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    var trainingHighlightedViewHeight by remember { mutableIntStateOf(0) }
    var trainingChartPositionInRootY by remember { mutableFloatStateOf(0f) }
    var containerPositionInRootY by remember { mutableFloatStateOf(0f) }
    val columnListState = rememberScrollState()
    var containerHeightPx by remember { mutableIntStateOf(0) }
    var datePickerHeightPx by remember { mutableIntStateOf(0) }
    val density = LocalDensity.current
    val contentHeightDp by remember(containerHeightPx, datePickerHeightPx) {
        mutableStateOf(density.run { (containerHeightPx - datePickerHeightPx).toDp() })
    }
    val containerListState = rememberScrollState()
    val containerNestedScrollConnection = rememberColumnNestScrollConnection(containerListState)
    var backToCurrentButtonHeightPx by remember { mutableIntStateOf(0) }

    // Visibility tracking system
    val visibilityTracker = rememberVisibilityTracker { visibleComponents ->
        visibleComponents.forEach { moduleName ->
            onModuleFullyVisible(moduleName)
        }
    }
    var columnRect by remember { mutableStateOf<Rect?>(null) }
    var contentAreaRect by remember { mutableStateOf<Rect?>(null) }
    
    // Track visibility when column position or content area changes
    LaunchedEffect(columnRect, contentAreaRect) {
        columnRect?.let { colRect ->
            contentAreaRect?.let { contentRect ->
                val baselineBottomY = kotlin.math.min(contentRect.bottom, colRect.bottom)
                visibilityTracker.checkVisibilityDebounced(colRect.bottom, baselineBottomY)
            }
        }
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .narrowContentWithBgColors(
                outerBackgroundColor = MaterialTheme.colors.background,
                backgroundColor = MaterialTheme.colors.surface,
            )
            .onGloballyPositioned { coordinates ->
                containerPositionInRootY = coordinates.boundsInRoot().top
                containerHeightPx = coordinates.size.height
                
                val positionInScreen = coordinates.positionOnScreen()
                contentAreaRect = Rect(
                    left = positionInScreen.x,
                    top = positionInScreen.y,
                    right = positionInScreen.x + coordinates.size.width,
                    bottom = positionInScreen.y + coordinates.size.height
                )
            }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .nestedScroll(containerNestedScrollConnection)
                .verticalScroll(containerListState)
        ) {
            M3AppTheme {
                TrainingSegmentedControl(
                    timeRange = graphTimeRange,
                    onTimeRangeToggled = onGraphTimeRangeToggled,
                )
            }

            Column(
                modifier = Modifier
                    .onSizeChanged { size ->
                        datePickerHeightPx = size.height
                    }
            ) {
                TrainingDatePicker(
                    trainingDateRange = trainingDateRange,
                    onNextClick = onNextClick,
                    onPreviousClick = onPreviousClick,
                    isNextEnabled = hasNext,
                    isPreviousEnabled = hasPrevious,
                    isLoading = isLoading
                )
                if (columnListState.canScrollBackward) {
                    Divider(color = MaterialTheme.colors.lightGrey)
                }
            }

            Column(
                modifier = Modifier
                    .height(contentHeightDp)
                    .verticalScroll(columnListState)
                    .onGloballyPositioned { coordinates ->
                        val positionInScreen = coordinates.positionOnScreen()
                        columnRect = Rect(
                            left = positionInScreen.x,
                            top = positionInScreen.y,
                            right = positionInScreen.x + coordinates.size.width,
                            bottom = positionInScreen.y + coordinates.size.height
                        )
                    }
            ) {
                TrainingSportsFilter(
                    selectedActivityGroupings = selectedActivityGroupings,
                    allDoneActivityTypes = allDoneActivityTypes,
                    onSelectSportsClick = onSelectSportsClick,
                )

                TrainingVolume(
                    trainingVolumeUiState = trainingVolumeUiState,
                    onShowInfoClick = onVolumeInfoClick,
                    onWorkoutSummaryClick = onVolumeWorkoutSummaryClick,
                    onChartPositionInRootYChanged = { trainingChartPositionInRootY = it },
                    chartHighlightedEvent = trainingVolumeChartHighlightedEvent,
                    coachPhrasesIds = trainingCoachUiState.trainingPhrasesIds,
                    modifier = Modifier.trackVisibility(
                        id = AnalyticsPropertyValue.LeaveTrainingZoneTabModuleNameProperty.TRAINING_VOLUME,
                        visibilityTracker = visibilityTracker
                    )
                )

                TrainingIntensityZone(
                    intensityUiState = intensityUiState,
                    onIntensityTypeClick = onIntensityTypeClick,
                    onShowInfoClicked = onIntensityInfoClick,
                    intensityCoachPhrasesIds = trainingCoachUiState.intensityPhrasesIds,
                    modifier = Modifier
                        .trackVisibility(
                            id = AnalyticsPropertyValue.LeaveTrainingZoneTabModuleNameProperty.INTENSITY_ZONE,
                            visibilityTracker = visibilityTracker
                        )
                        .padding(top = MaterialTheme.spacing.large)
                )

                TrainingImpact(
                    impactUiState = impactUiState,
                    onWorkoutImpactTypeClick = onWorkoutImpactTypeClick,
                    onShowInfoClicked = onImpactInfoClick,
                    onTrainingModelInfoClicked = onTrainingModelInfoClick,
                    impactCoachPhraseIds = trainingCoachUiState.impactPhrasesIds,
                    modifier = Modifier
                        .trackVisibility(
                            id = AnalyticsPropertyValue.LeaveTrainingZoneTabModuleNameProperty.IMPACT,
                            visibilityTracker = visibilityTracker
                        )
                        .padding(top = MaterialTheme.spacing.large)
                )

                Spacer(
                    modifier = Modifier
                        .height(if (hasNext) MaterialTheme.spacing.large + density.run { backToCurrentButtonHeightPx.toDp() } else MaterialTheme.spacing.medium)
                )
            }
        }

        TrainingHighlightedView(
            highlighted = trainingChartHighlighted,
            modifier = Modifier
                .offset {
                    IntOffset(
                        x = 0,
                        y = (trainingChartPositionInRootY - containerPositionInRootY - trainingHighlightedViewHeight)
                            .roundToInt().coerceAtLeast(0)
                    )
                }
                .onSizeChanged { trainingHighlightedViewHeight = it.height },
        )

        AnimatedVisibility(
            visible = hasNext,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = MaterialTheme.spacing.small),
            enter = slideInVertically(initialOffsetY = { it * 2 }),
            exit = slideOutVertically(targetOffsetY = { it * 2 }),
        ) {
            M3AppTheme {
                BackToCurrentButton(
                    onClick = onBackToCurrentClick,
                    modifier = Modifier
                        .onSizeChanged { size ->
                            backToCurrentButtonHeightPx = size.height
                        },
                )
            }
        }
    }
}

@Composable
private fun TrainingSportsFilter(
    selectedActivityGroupings: List<CoreActivityGrouping>,
    allDoneActivityTypes: List<CoreActivityType>,
    onSelectSportsClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val isAllSelected = remember(selectedActivityGroupings) { selectedActivityGroupings.isEmpty() }
    val title = if (isAllSelected) {
        stringResource(R.string.training_v2_training_all_sports)
    } else if (selectedActivityGroupings.size == 1) {
        stringResource(selectedActivityGroupings.first().nameRes)
    } else {
        stringResource(R.string.training_v2_training_multiple_sports)
    }
    val selectedActivityTypes = remember(selectedActivityGroupings, allDoneActivityTypes) {
        selectedActivityGroupings.flatMap { it.activityTypes }
            .intersect(allDoneActivityTypes).toImmutableList()
    }
    TextButton(
        onClick = onSelectSportsClick,
        modifier = modifier
            .background(MaterialTheme.colors.surface),
        shape = RectangleShape,
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.small),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMegaBold,
                    color = MaterialTheme.colors.onSurface
                )
                Icon(
                    painter = painterResource(R.drawable.ic_trend_down),
                    contentDescription = null,
                    tint = MaterialTheme.colors.onSurface,
                    modifier = Modifier
                        .size(MaterialTheme.iconSizes.small)
                )
            }

            ActivityTypeRow(
                selectedActivityTypes = if (isAllSelected) allDoneActivityTypes else selectedActivityTypes,
            )
        }
    }
}

@Composable
private fun ActivityTypeRow(
    selectedActivityTypes: List<CoreActivityType>,
    modifier: Modifier = Modifier,
    maxIconCount: Int = 5
) {
    if (selectedActivityTypes.isEmpty()) return
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        verticalAlignment = Alignment.CenterVertically
    ) {
        OverlappingRow(
            overlapFactor = 5f / 6
        ) {
            selectedActivityTypes.take(maxIconCount).forEach { type ->
                SuuntoActivityIcon(
                    coreActivityType = type,
                    iconSize = MaterialTheme.iconSizes.small
                )
            }
        }
        Text(
            text = if (selectedActivityTypes.size <= maxIconCount) {
                ""
            } else {
                "+${selectedActivityTypes.size - maxIconCount}"
            },
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colors.onSurface
        )
    }
}
