package com.stt.android.diary.trainingv2

import android.os.SystemClock
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.soy.algorithms.impact.WorkoutImpact
import com.soy.algorithms.impact.WorkoutImpactType
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.toAnalyticsTimeDim
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.core.domain.workouts.CoreActivityGroup
import com.stt.android.core.domain.workouts.CoreActivityGrouping
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.core.domain.workouts.MINIMUM_COUNT_TO_SHOW_GROUP
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.diary.insights.TrainingHubFormatter
import com.stt.android.diary.insights.coach.TrainingCoachUiState
import com.stt.android.diary.insights.common.TrainingHubDateRange
import com.stt.android.diary.insights.impact.ImpactUiState
import com.stt.android.diary.insights.impact.WorkoutImpactCount
import com.stt.android.diary.insights.impact.analyticsValue
import com.stt.android.diary.insights.intensity.IntensityType
import com.stt.android.diary.insights.intensity.IntensityUiState
import com.stt.android.diary.insights.intensity.IntensityZoneInfoUiState
import com.stt.android.diary.insights.intensity.analyticsValue
import com.stt.android.diary.insights.volume.TrainingHighlighted
import com.stt.android.diary.insights.volume.TrainingVolumeUiState
import com.stt.android.diary.insights.volume.VolumeWorkoutSummaryType
import com.stt.android.diary.insights.volume.analyticsValue
import com.stt.android.diary.summary.TrainingZoneAnalytics
import com.stt.android.domain.diary.insights.TrainingHubPeriodAnalysisUseCase
import com.stt.android.domain.diary.insights.TrainingHubSuuntoCoachUseCase
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.diary.models.isWeekSegmented
import com.stt.android.domain.diary.training.TrainingPeriodAnalysisUseCase
import com.stt.android.domain.diary.training.TrainingPeriodsCalculationResult
import com.stt.android.domain.diary.training.TrainingPeriodsCalculationUseCase
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.domain.workouts.tss.WorkoutTSSSummary
import com.stt.android.domain.workouts.tss.WorkoutTSSSummaryRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.collections.immutable.toImmutableMap
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.LocalDate
import javax.inject.Inject
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@HiltViewModel
class TrainingV2ViewModel @Inject constructor(
    userSettingsController: UserSettingsController,
    workoutDataSource: WorkoutDataSource,
    private val dispatchers: CoroutinesDispatchers,
    private val currentUserController: CurrentUserController,
    private val trainingHubPeriodAnalysisUseCase: TrainingHubPeriodAnalysisUseCase,
    private val trainingHubSuuntoCoachUseCase: TrainingHubSuuntoCoachUseCase,
    private val workoutTSSSummaryRepository: WorkoutTSSSummaryRepository,
    private val trainingHubFormatter: TrainingHubFormatter,
    private val trainingPeriodAnalysisUseCase: TrainingPeriodAnalysisUseCase,
    private val trainingPeriodsCalculationUseCase: TrainingPeriodsCalculationUseCase,
    private val trainingUiStateMapper: TrainingUiStateMapper,
    private val trainingZoneAnalytics: TrainingZoneAnalytics,
) : ViewModel() {

    private var browsingDurationSinceMills: Long? = null
    private val fullyVisibleModuleNames: MutableSet<String> = linkedSetOf()

    private val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek

    // Current page number is 0
    private val _pageNumber = MutableStateFlow(0)

    private val _graphTimeRange = MutableStateFlow(GraphTimeRange.CURRENT_WEEK)
    val graphTimeRange: StateFlow<GraphTimeRange> = _graphTimeRange.asStateFlow()

    val chartGranularity = _graphTimeRange.map {
        it.toChartGranularity()
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = _graphTimeRange.value.toChartGranularity()
    )

    private val timeRangeAnalyticsValue: String
        get() = chartGranularity.value.toAnalyticsTimeDim()

    private val _isLoading = MutableStateFlow(true)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val tssSummaries: MutableStateFlow<List<WorkoutTSSSummary>?> =
        MutableStateFlow(null)

    val hasNext = MutableStateFlow(false)
    val hasPrevious = MutableStateFlow(true)
    val trainingDateRange = MutableStateFlow<TrainingDateRange>(TrainingDateRange.CurrentWeek)

    private val trainingChartHighlightedEntryX = MutableStateFlow<Long?>(null)

    private val _allDoneActivityTypes = MutableStateFlow<List<CoreActivityType>>(emptyList())
    val allDoneActivityTypes = _allDoneActivityTypes.asStateFlow()

    private val _selectedActivityGroupings =
        MutableStateFlow<List<CoreActivityGrouping>>(emptyList())
    val selectedActivityGroupings = _selectedActivityGroupings.asStateFlow()

    private val _selectedActivityTypes = MutableStateFlow<List<CoreActivityType>>(emptyList())
    val selectedActivityTypes = _selectedActivityTypes.asStateFlow()

    private fun GraphTimeRange.toChartGranularity(): ChartGranularity = when (this) {
        GraphTimeRange.CURRENT_WEEK -> ChartGranularity.WEEKLY
        GraphTimeRange.CURRENT_MONTH -> ChartGranularity.MONTHLY
        GraphTimeRange.SEVEN_DAYS -> ChartGranularity.SEVEN_DAYS
        GraphTimeRange.THIRTY_DAYS -> ChartGranularity.THIRTY_DAYS
        GraphTimeRange.SIX_WEEKS -> ChartGranularity.SIX_WEEKS
        GraphTimeRange.SIX_MONTHS -> ChartGranularity.SIX_MONTHS
        GraphTimeRange.CURRENT_YEAR -> ChartGranularity.YEARLY
        GraphTimeRange.EIGHT_YEARS -> ChartGranularity.EIGHT_YEARS

        GraphTimeRange.EIGHT_WEEKS,
        GraphTimeRange.EIGHT_MONTHS,
        GraphTimeRange.THIRTEEN_MONTHS,
        GraphTimeRange.ONE_YEAR -> throw IllegalArgumentException("Unsupported time range: $this")
    }

    private fun getPeriodsByPageNumber(
        pageNumber: Int,
        timeRange: GraphTimeRange
    ): TrainingPeriodsCalculationResult {
        val periods = trainingPeriodsCalculationUseCase(
            pageNumber = pageNumber,
            firstDayOfWeek = firstDayOfWeek,
            timeRange = timeRange,
        )
        return periods
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private val trainingAnalysisUiState = combine(
        _pageNumber,
        _selectedActivityTypes
            .map {
                // If selectedActivityTypes is empty, it indicates that load all
                it.ifEmpty { CoreActivityType.entries }
            },
        _graphTimeRange,
        ::Triple
    ).mapLatest { (pageNumber, activityTypes, timeRange) ->
        _isLoading.value = true

        val (firstPeriod, secondPeriod) = getPeriodsByPageNumber(pageNumber, timeRange)

        val hasNext = !firstPeriod.contains(LocalDate.now())
        this.hasNext.value = hasNext
        this.hasPrevious.value =
            firstPeriod.start.minusDays(7).year > 2022 // 1st January 2023 is the first date where we have intensity data for downloaded with ur workouts

        val dateRange = trainingUiStateMapper.toTrainingDateRange(firstPeriod, hasNext, timeRange)
        this.trainingDateRange.value = dateRange

        val firstAnalysisAsync = viewModelScope.async {
            trainingPeriodAnalysisUseCase.getAnalysisForPeriod(
                username = currentUserController.username,
                startDate = firstPeriod.start,
                endDateInclusive = firstPeriod.endInclusive,
                avgFactor = 1f,
                activityTypes = activityTypes
            )
        }
        val secondAnalysisAsync = if (timeRange.isWeekSegmented()) {
            viewModelScope.async {
                trainingPeriodAnalysisUseCase.getAnalysisForPeriod(
                    username = currentUserController.username,
                    startDate = secondPeriod.start,
                    endDateInclusive = secondPeriod.endInclusive,
                    avgFactor = 6f,
                    activityTypes = activityTypes
                )
            }
        } else null

        trainingUiStateMapper.toTrainingUiState(
            firstPeriod = firstPeriod,
            firstAnalysis = firstAnalysisAsync.await(),
            secondPeriod = secondPeriod,
            secondAnalysis = secondAnalysisAsync?.await(),
            graphTimeRange = timeRange,
            dateRange = dateRange,
            selectedActivityTypes = activityTypes,
        )
    }.catch {
        Timber.w(it, "Error loading new training comparison")
        _isLoading.value = false
    }.onEach {
        _isLoading.value = false
    }.flowOn(dispatchers.io).stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = TrainingUiState()
    )

    private val initialTrainingCoachUiState =
        TrainingCoachUiState(persistentListOf(), persistentListOf(), persistentListOf())
    private val _trainingCoachUiState = MutableStateFlow(initialTrainingCoachUiState)

    @OptIn(ExperimentalCoroutinesApi::class)
    val trainingCoachUiState = combine(_pageNumber, _graphTimeRange, ::Pair)
        .mapLatest { (pageNumber, timeRange) ->
            // coach is displayed only in the weekly
            if (timeRange.isWeekSegmented()) {
                val (firstPeriod, secondPeriod) = getPeriodsByPageNumber(pageNumber, timeRange)
                val hasNext = !firstPeriod.contains(LocalDate.now())

                // first group
                val firstInsightsAsync = viewModelScope.async {
                    trainingHubPeriodAnalysisUseCase.getAnalysisForPeriod(
                        username = currentUserController.username,
                        startDate = firstPeriod.start,
                        endDateInclusive = firstPeriod.endInclusive,
                        preCalculatedTSSSummaries = tssSummaries.value
                    )
                }
                // second group
                val secondInsightsAsync = viewModelScope.async {
                    trainingHubPeriodAnalysisUseCase.getAnalysisForPeriod(
                        username = currentUserController.username,
                        startDate = secondPeriod.start,
                        endDateInclusive = secondPeriod.endInclusive,
                        preCalculatedTSSSummaries = tssSummaries.value
                    )
                }

                val firstInsights = firstInsightsAsync.await()
                val secondInsights = secondInsightsAsync.await()

                trainingHubSuuntoCoachUseCase.getCoachFeedback(
                    currentPeriodAnalysis = firstInsights,
                    comparisonPeriodAnalysis = secondInsights,
                    firstDayOfWeek = firstDayOfWeek,
                    isCurrentWeek = !hasNext,
                    sleepHrv = null, // We just get the coach of TRAINING
                )
            } else {
                emptyMap()
            }
        }
        .map { coachFeedback ->
            trainingUiStateMapper.toTrainingCoachUiState(coachFeedback.toImmutableMap())
        }.flowOn(dispatchers.io).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = _trainingCoachUiState.value
        )

    private val _trainingVolumeUiState = MutableStateFlow(
        TrainingVolumeUiState.createDefault(
            firstDayOfWeek,
            userSettingsController.settings.measurementUnit
        )
    )
    val trainingVolumeUiState: StateFlow<TrainingVolumeUiState> =
        combine(
            _trainingVolumeUiState,
            trainingAnalysisUiState,
            ::Pair
        ).mapNotNull { (trainingVolume, analysis) ->
            trainingUiStateMapper.toTrainingVolumeUiStateOrNull(
                trainingVolume,
                analysis.firstAnalysis,
                analysis.secondAnalysis,
                analysis.graphTimeRange.toChartGranularity(),
                analysis.selectedActivityTypes,
            )?.also {
                onVolumeWorkoutSummaryClick(it.selectedSummaryType)
            }
        }.flowOn(dispatchers.io).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = _trainingVolumeUiState.value
        )

    private val initialIntensityUiState = IntensityUiState(
        selectedType = IntensityType.HEART_RATE,
        heartRateZones = (5 downTo 1).map {
            IntensityZoneInfoUiState.empty(
                number = it,
                valueFormatted = trainingHubFormatter.formatDuration(0.0).first,
            )
        }.toImmutableList(),
        paceZones = persistentListOf(),
        runningPowerZones = persistentListOf(),
        cyclingPowerZones = persistentListOf(),
        supportComparison = true,
    )
    private val _intensityUiState = MutableStateFlow(initialIntensityUiState)
    val intensityUiState = combine(
        _intensityUiState,
        trainingAnalysisUiState,
        ::Pair
    ).mapNotNull { (intensity, analysis) ->
        trainingUiStateMapper.toIntensityUiStateOrNull(intensity, analysis)?.also {
            onIntensityTypeClick(it.selectedType)
        }
    }.flowOn(dispatchers.io).stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = _intensityUiState.value
    )

    private val initialImpactUiState = ImpactUiState(
        _workoutsImpactCount = WorkoutImpact.entries
            .map {
                WorkoutImpactCount(
                    workoutImpact = it,
                    currentCount = 0f,
                    comparisonCount = 0f,
                    formattedCurrentCount = "0",
                    formattedComparisonCount = "0"
                )
            }.toImmutableList(),
        isExpanded = true, // In the new Training, it is always expanded
        trainingHubDateRange = TrainingHubDateRange.CurrentWeek
    )
    private val _impactUiState = MutableStateFlow(initialImpactUiState)
    val impactUiState = combine(
        _impactUiState,
        trainingAnalysisUiState,
        ::Pair
    ).mapNotNull { (impact, analysis) ->
        trainingUiStateMapper.toImpactUiStateOrNull(impact, analysis)
    }.flowOn(dispatchers.io).filterNotNull().stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = _impactUiState.value
    )

    @OptIn(ExperimentalCoroutinesApi::class)
    val trainingChartHighlighted: StateFlow<TrainingHighlighted?> = combine(
        trainingChartHighlightedEntryX,
        trainingVolumeUiState,
        ::Pair
    ).mapLatest { (entryX, trainingVolume) ->
        trainingUiStateMapper.toTrainingChartHighlightedOrNull(
            entryX,
            trainingVolume.selectedSummaryType,
            trainingVolume.chartData
        )
    }.flowOn(dispatchers.io).stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = null
    )

    init {
        viewModelScope.launch(dispatchers.io) {
            tssSummaries.value = runSuspendCatching {
                workoutTSSSummaryRepository.loadWorkoutTSSSummaries(
                    currentUserController.username
                )
            }.getOrElse { e ->
                Timber.w(e, "Error running loadWorkoutTSSSummaries")
                null
            }
        }

        viewModelScope.launch(dispatchers.io) {
            workoutDataSource
                .loadAllDoneActivityTypes(currentUserController.username)
                .mapNotNull { id ->
                    runCatching { CoreActivityType.valueOf(id) }.getOrNull()
                }
                .distinct()
                .let { types ->
                    _allDoneActivityTypes.value = types
                }
        }

        trackPageExposure()
    }

    fun onGraphTimeRangeToggled(timeRange: GraphTimeRange) {
        // After changed the TimeRange, record the event and restart
        stopViewDurationAndTrackEvent()
        fullyVisibleModuleNames.clear()
        startViewDuration()

        _graphTimeRange.update { timeRange }
        _pageNumber.update { 0 }
    }

    fun onNextClick() {
        _pageNumber.update { (it + 1).coerceAtMost(0) } // Make sure we don't pass the current week

        trackButtonClick(AnalyticsPropertyValue.TrainingZoneButtonClickProperty.NEXT)
    }

    fun onPreviousClick() {
        _pageNumber.update { it - 1 }

        trackButtonClick(AnalyticsPropertyValue.TrainingZoneButtonClickProperty.LAST)
    }

    fun onBackToCurrentClick() = _pageNumber.update { 0 }

    fun updateTrainingVolumeSelectedActivityTypes(activityTypes: List<CoreActivityType>) {
        val groups = CoreActivityGroup.entries.filter {
            val typesInGroup = activityTypes.intersect(it.activityTypes)
            typesInGroup.size >= MINIMUM_COUNT_TO_SHOW_GROUP &&
                typesInGroup == _allDoneActivityTypes.value.intersect(it.activityTypes)
        }
        _selectedActivityGroupings.value =
            groups + (activityTypes - groups.flatMap { it.activityTypes })
        _selectedActivityTypes.value = activityTypes
    }

    fun onVolumeWorkoutSummaryClick(volumeWorkoutSummaryType: VolumeWorkoutSummaryType) {
        if (_trainingVolumeUiState.value.selectedSummaryType != volumeWorkoutSummaryType) {
            _trainingVolumeUiState.update {
                it.copy(selectedSummaryType = volumeWorkoutSummaryType)
            }

            trackButtonClick(volumeWorkoutSummaryType.analyticsValue)
        }
    }

    fun onIntensityTypeClick(type: IntensityType) {
        if (_intensityUiState.value.selectedType != type) {
            _intensityUiState.update { it.copy(selectedType = type) }

            trackButtonClick(type.analyticsValue)
        }
    }

    fun onWorkoutImpactTypeClick(type: WorkoutImpactType) {
        if (_impactUiState.value.selectedImpactType != type) {
            _impactUiState.update { it.copy(selectedImpactType = type) }

            trackButtonClick(type.analyticsValue.orEmpty())
        }
    }

    fun onTrainingVolumeChartHighlightedEvent(entryX: Long?) {
        trainingChartHighlightedEntryX.value = entryX
    }

    private fun trackPageExposure() {
        viewModelScope.launch(dispatchers.io) {
            delay(3000L) // This is the PM requirement

            trainingZoneAnalytics.trackPageExposure(
                pageName = AnalyticsPropertyValue.TrainingZonePageNameProperty.TRAINING,
                timeDim = timeRangeAnalyticsValue
            )
        }
    }

    fun onModuleFullyVisible(moduleName: String) {
        fullyVisibleModuleNames.add(moduleName)
    }

    fun startViewDuration() {
        browsingDurationSinceMills = SystemClock.elapsedRealtime()
    }

    fun stopViewDurationAndTrackEvent() {
        browsingDurationSinceMills?.let {
            val browsingDuration =
                (SystemClock.elapsedRealtime() - it).toDuration(DurationUnit.MILLISECONDS).inWholeSeconds
            if (browsingDuration > 0) {
                trackBrowsingDuration(browsingDuration)
            }
        }
    }

    private fun trackBrowsingDuration(duration: Long) {
        trainingZoneAnalytics.trackBrowsingDuration(
            browedPages = AnalyticsPropertyValue.TrainingZonePageNameProperty.TRAINING,
            duration = duration,
            timeDim = timeRangeAnalyticsValue,
            moduleNames = fullyVisibleModuleNames.toList()
        )
    }

    fun trackButtonClick(buttonName: String) {
        trainingZoneAnalytics.trackButtonClick(
            pageName = AnalyticsPropertyValue.TrainingZonePageNameProperty.TRAINING,
            timeDim = timeRangeAnalyticsValue,
            buttonName = buttonName
        )
    }
}
