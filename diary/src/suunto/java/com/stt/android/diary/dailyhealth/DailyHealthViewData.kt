package com.stt.android.diary.dailyhealth

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.ui.text.AnnotatedString
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.screen.ChartHighlightViewData
import com.stt.android.diary.recovery.v2.DatePickerData
import kotlinx.collections.immutable.ImmutableList

sealed interface DailyHealthViewData {

    data class Loaded(
        val mainTimeGranularities: ImmutableList<ChartGranularity>,
        val extraTimeGranularities: ImmutableList<ChartGranularity>,
        val currentTimeGranularity: ChartGranularity,
        val showExtraTimeGranularitySelection: Boolean = false,
        val healthItems: ImmutableList<HealthItem>,
        val datePickerData: DatePickerData,
        val isLoading: Boolean = false,
    ) : DailyHealthViewData

    data object Initial : DailyHealthViewData
}

data class HealthItem(
    val chartType: ChartType,
    @DrawableRes val iconRes: Int? = null,
    @StringRes val titleRes: Int? = null,
    val valueInfo: ValueInfo? = null,
    val chartData: ChartData? = null,
    val hasInfoButton: Boolean = true,
    val chartHighlight: ChartHighlightViewData = ChartHighlightViewData.None,
)

data class ValueInfo(
    val label: String? = null,
    val value: AnnotatedString? = null,
    val timeInfo: String? = null,
)

enum class ChartType {
    HEART_RATE,
    STEPS,
    CALORIES,
}
