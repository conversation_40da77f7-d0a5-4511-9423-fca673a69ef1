package com.stt.android.diary.dailyhealth

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import com.stt.android.chart.api.ChartNavigator
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.home.diary.InfoBottomSheet
import com.stt.android.home.diary.TrainingHubInfoSheetFragmentCreator
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class DailyHealthFragment : Fragment() {
    @Inject
    lateinit var chartNavigator: ChartNavigator

    @Inject
    lateinit var trainingHubInfoSheetFragmentCreator: TrainingHubInfoSheetFragmentCreator

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContentWithM3Theme {
            DailyHealthScreen(
                onShowInfoSheet = ::showInfoSheet,
            )
        }
    }

    private fun showInfoSheet(bottomSheet: InfoBottomSheet) {
        val dialog = trainingHubInfoSheetFragmentCreator.createTrainingHubInfoSheetFragment(
            bottomSheet
        )
        dialog.show(parentFragmentManager, bottomSheet.name)
    }
}
