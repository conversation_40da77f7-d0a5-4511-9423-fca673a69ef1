package com.stt.android.diary.trainingv2

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.soy.algorithms.impact.WorkoutImpactType
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.core.domain.workouts.CoreActivityGrouping
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.diary.trainingv2.composables.TrainingScreen
import com.stt.android.home.diary.InfoBottomSheet
import com.stt.android.home.diary.analyticsClickName
import com.stt.android.home.diaryv2.DiaryViewModel
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class TrainingV2Fragment : Fragment() {

    private val viewModel: TrainingV2ViewModel by viewModels()
    private val parentViewModel: DiaryViewModel by viewModels({ requireParentFragment() })

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        childFragmentManager.setFragmentResultListener(
            TrainingActivitySheetPickerFragment.REQUEST_KEY_SELECTED_ACTIVITIES,
            this
        ) { _, bundle ->
            val ids =
                bundle.getIntegerArrayList(TrainingActivitySheetPickerFragment.BUNDLE_SELECTED_ACTIVITIES)
                    ?: emptyList<Int>()
            CoreActivityType.entries.filter { it.id in ids }.let {
                viewModel.updateTrainingVolumeSelectedActivityTypes(it)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContentWithTheme {
            val isLoading by viewModel.isLoading.collectAsState()
            val graphTimeRange by viewModel.graphTimeRange.collectAsState()
            val hasNext by viewModel.hasNext.collectAsState()
            val hasPrevious by viewModel.hasPrevious.collectAsState()
            val selectedActivityGroupings by viewModel.selectedActivityGroupings.collectAsState()
            val selectedActivityTypes by viewModel.selectedActivityTypes.collectAsState()
            val allDoneActivityTypes by viewModel.allDoneActivityTypes.collectAsState()
            val trainingVolumeUiState by viewModel.trainingVolumeUiState.collectAsState()
            val trainingChartHighlighted by viewModel.trainingChartHighlighted.collectAsState()
            val trainingDateRange by viewModel.trainingDateRange.collectAsState()
            val trainingCoachUiState by viewModel.trainingCoachUiState.collectAsState()
            val intensityUiState by viewModel.intensityUiState.collectAsState()
            val impactUiState by viewModel.impactUiState.collectAsState()
            val chartGranularity by viewModel.chartGranularity.collectAsState()

            TrainingScreen(
                isLoading = isLoading,
                chartGranularity = chartGranularity,
                graphTimeRange = graphTimeRange,
                onGraphTimeRangeToggled = viewModel::onGraphTimeRangeToggled,
                hasNext = hasNext,
                hasPrevious = hasPrevious,
                trainingDateRange = trainingDateRange,
                onNextClick = viewModel::onNextClick,
                onPreviousClick = viewModel::onPreviousClick,
                trainingCoachUiState = trainingCoachUiState,
                selectedActivityGroupings = selectedActivityGroupings,
                allDoneActivityTypes = allDoneActivityTypes,
                onSelectSportsClick = {
                    openSportsPicker(selectedActivityGroupings, selectedActivityTypes)
                },
                trainingVolumeUiState = trainingVolumeUiState,
                onVolumeWorkoutSummaryClick = { viewModel.onVolumeWorkoutSummaryClick(it) },
                onVolumeInfoClick = ::openVolumeInfoSheet,
                trainingChartHighlighted = trainingChartHighlighted,
                trainingVolumeChartHighlightedEvent = viewModel::onTrainingVolumeChartHighlightedEvent,
                intensityUiState = intensityUiState,
                onIntensityInfoClick = ::openIntensityInfoSheet,
                onIntensityTypeClick = viewModel::onIntensityTypeClick,
                impactUiState = impactUiState,
                onImpactInfoClick = ::openImpactInfoSheet,
                onWorkoutImpactTypeClick = viewModel::onWorkoutImpactTypeClick,
                onTrainingModelInfoClick = ::openTrainingModelInfoSheet,
                onBackToCurrentClick = viewModel::onBackToCurrentClick,
                onModuleFullyVisible = viewModel::onModuleFullyVisible,
            )
        }
    }

    private fun openSportsPicker(
        selectedActivityGroupings: List<CoreActivityGrouping>,
        selectedActivityTypes: List<CoreActivityType>
    ) {
        val activityTypes = selectedActivityGroupings.flatMap { it.activityTypes }
            .filter { it in selectedActivityTypes }
        TrainingActivitySheetPickerFragment.newInstance(activityTypes)
            .show(childFragmentManager, "TrainingActivityPickerSheet")
    }

    private fun openVolumeInfoSheet() {
        parentViewModel.openInfoSheet(InfoBottomSheet.TRAINING_HUB_VOLUME)
        viewModel.trackButtonClick(InfoBottomSheet.TRAINING_HUB_VOLUME.analyticsClickName)
    }

    private fun openIntensityInfoSheet() {
        parentViewModel.openInfoSheet(InfoBottomSheet.TRAINING_HUB_INTENSITY)
        viewModel.trackButtonClick(InfoBottomSheet.TRAINING_HUB_INTENSITY.analyticsClickName)
    }

    private fun openImpactInfoSheet(workoutImpactType: WorkoutImpactType) {
        when (workoutImpactType) {
            WorkoutImpactType.CARDIO -> openCardioImpactInfoSheet()
            WorkoutImpactType.MUSCULAR -> openMuscularImpactInfoSheet()
            WorkoutImpactType.NONE -> {
                // This is not supposed to happen
                Timber.d("Called openImpactInfoSheet with NONE")
            }
        }
    }

    private fun openCardioImpactInfoSheet() {
        parentViewModel.openInfoSheet(InfoBottomSheet.TRAINING_HUB_CARDIO_IMPACT)
        viewModel.trackButtonClick(InfoBottomSheet.TRAINING_HUB_CARDIO_IMPACT.analyticsClickName)
    }

    private fun openMuscularImpactInfoSheet() {
        parentViewModel.openInfoSheet(InfoBottomSheet.TRAINING_HUB_MUSCULAR_IMPACT)
        viewModel.trackButtonClick(InfoBottomSheet.TRAINING_HUB_MUSCULAR_IMPACT.analyticsClickName)
    }

    private fun openTrainingModelInfoSheet() {
        parentViewModel.openInfoSheet(InfoBottomSheet.TRAINING_HUB_TRAINING_MODEL)
        viewModel.trackButtonClick(InfoBottomSheet.TRAINING_HUB_TRAINING_MODEL.analyticsClickName)
    }

    override fun onResume() {
        super.onResume()
        viewModel.startViewDuration()
    }

    override fun onPause() {
        viewModel.stopViewDurationAndTrackEvent()
        super.onPause()
    }

    companion object {

        fun newInstance() = TrainingV2Fragment()
    }
}
