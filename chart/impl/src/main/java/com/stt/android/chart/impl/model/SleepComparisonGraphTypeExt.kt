package com.stt.android.chart.impl.model

import android.content.Context
import com.stt.android.chart.impl.R
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.sumByFloat
import java.time.Duration
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.TimeUnit
import kotlin.math.abs
import kotlin.math.roundToInt
import kotlin.math.roundToLong
import kotlin.time.Duration.Companion.seconds
import com.stt.android.R as BR
import com.stt.android.core.R as CR

internal val SleepComparisonGraphType.nameRes: Int
    get() = when (this) {
        SleepComparisonGraphType.SLEEP_DURATION -> R.string.sleep_comparison_graph_type_sleep_duration
        SleepComparisonGraphType.SLEEP_REGULARITY -> R.string.sleep_comparison_graph_type_sleep_regularity
        SleepComparisonGraphType.SLEEP_NAP -> R.string.sleep_comparison_graph_type_nap_duration
        SleepComparisonGraphType.SLEEP_TOTAL -> R.string.sleep_comparison_graph_type_total_duration
        SleepComparisonGraphType.BLOOD_OXYGEN -> R.string.sleep_comparison_graph_type_blood_oxygen
        SleepComparisonGraphType.TRAINING -> R.string.sleep_comparison_graph_type_training
        SleepComparisonGraphType.MIN_HR_DURING_SLEEP -> R.string.sleep_comparison_graph_type_min_hr_during_sleep
        SleepComparisonGraphType.AVG_HR_DURING_SLEEP -> R.string.sleep_comparison_graph_type_avg_hr_during_sleep
        SleepComparisonGraphType.MORNING_RESOURCES -> R.string.sleep_comparison_graph_type_morning_resources
        SleepComparisonGraphType.NONE -> BR.string.none
    }

internal val SleepComparisonGraphType.isLeft: Boolean
    get() = when (this) {
        SleepComparisonGraphType.SLEEP_DURATION,
        SleepComparisonGraphType.SLEEP_REGULARITY,
        SleepComparisonGraphType.SLEEP_NAP,
        SleepComparisonGraphType.SLEEP_TOTAL -> true

        SleepComparisonGraphType.BLOOD_OXYGEN,
        SleepComparisonGraphType.TRAINING,
        SleepComparisonGraphType.MIN_HR_DURING_SLEEP,
        SleepComparisonGraphType.AVG_HR_DURING_SLEEP,
        SleepComparisonGraphType.MORNING_RESOURCES,
        SleepComparisonGraphType.NONE -> false
    }

internal fun Float.formatByGraphType(
    context: Context,
    graphType: SleepComparisonGraphType,
): String {
    val value = when (graphType) {
        SleepComparisonGraphType.SLEEP_DURATION,
        SleepComparisonGraphType.SLEEP_NAP,
        SleepComparisonGraphType.SLEEP_TOTAL,
        SleepComparisonGraphType.TRAINING -> (this * 3600f).roundToLong().formatDuration(context)

        SleepComparisonGraphType.BLOOD_OXYGEN,
        SleepComparisonGraphType.MORNING_RESOURCES -> this.formatPercent()

        SleepComparisonGraphType.MIN_HR_DURING_SLEEP,
        SleepComparisonGraphType.AVG_HR_DURING_SLEEP -> this.formatBpm(context)

        SleepComparisonGraphType.SLEEP_REGULARITY,
        SleepComparisonGraphType.NONE -> return ""
    }
    return "${context.getString(BR.string.avg)} $value"
}

internal fun formatByGraphType(
    context: Context,
    graphType: SleepComparisonGraphType,
    entries: List<SleepComparisonChartData.Entry>,
) = when (graphType) {
    SleepComparisonGraphType.SLEEP_DURATION,
    SleepComparisonGraphType.SLEEP_NAP,
    SleepComparisonGraphType.SLEEP_TOTAL,
    SleepComparisonGraphType.TRAINING -> entries.sumByFloat { it.high * 3600f }.roundToLong()
        .formatDuration(context)

    SleepComparisonGraphType.BLOOD_OXYGEN,
    SleepComparisonGraphType.MORNING_RESOURCES -> entries.sumByFloat { it.high }
        .takeIf { it > 0f }?.formatPercent()

    SleepComparisonGraphType.MIN_HR_DURING_SLEEP,
    SleepComparisonGraphType.AVG_HR_DURING_SLEEP -> entries.sumByFloat { it.high }
        .takeIf { it > 0f }?.formatBpm(context)

    SleepComparisonGraphType.SLEEP_REGULARITY -> {
        val entry = entries.getOrNull(0)
        if (entry != null) {
            "${entry.low.toLong().formatTime()}-${entry.high.toLong().formatTime()}"
        } else null
    }

    SleepComparisonGraphType.NONE -> null
}

internal fun Float.formatValueByGraphType(
    graphType: SleepComparisonGraphType,
): String = when (graphType) {
    SleepComparisonGraphType.SLEEP_DURATION,
    SleepComparisonGraphType.SLEEP_NAP,
    SleepComparisonGraphType.SLEEP_TOTAL,
    SleepComparisonGraphType.TRAINING -> {
        val seconds = (this * 3600f).roundToLong() + 30L
        val hours = (seconds / 3600).toInt()
        val minutes = ((seconds % 3600) / 60).toInt()
        when {
            hours == 0 && minutes == 0 -> "0"
            minutes == 0 -> hours.toString()
            else -> "$hours:${minutes.toString().padStart(2, '0')}"
        }
    }

    SleepComparisonGraphType.BLOOD_OXYGEN,
    SleepComparisonGraphType.MORNING_RESOURCES,
    SleepComparisonGraphType.MIN_HR_DURING_SLEEP,
    SleepComparisonGraphType.AVG_HR_DURING_SLEEP -> roundToInt().toString()

    SleepComparisonGraphType.SLEEP_REGULARITY,
    SleepComparisonGraphType.NONE -> ""
}

internal fun Long.formatDuration(context: Context): String {
    val (hours, minutes) = abs(this).secondsToHourMinute()
    val hourUnit = context.getString(CR.string.hour)
    val minuteUnit = context.getString(CR.string.minute)
    val text = if (hours == 0L) {
        "$minutes$minuteUnit"
    } else if (minutes == 0L) {
        "$hours$hourUnit"
    } else {
        "$hours$hourUnit $minutes$minuteUnit"
    }
    return if (this >= 0L) text else "-$text"
}

internal fun Long.secondsToHourMinute(): Pair<Long, Long> {
    var hours = TimeUnit.SECONDS.toHours(this)
    val secondsWithRounding = this + TimeUnit.MINUTES.toSeconds(1) / 2
    var minutes =
        TimeUnit.SECONDS.toMinutes(secondsWithRounding) - TimeUnit.HOURS.toMinutes(hours)
    if (minutes == TimeUnit.HOURS.toMinutes(1)) {
        minutes = 0
        hours += 1
    }
    return Pair(hours, minutes)
}

internal fun Float.formatPercent(optimizeZero: Boolean = false): String {
    return if (optimizeZero && this < 1f) {
        "<1%"
    } else {
        "${TextFormatter.formatPercentage(this.toDouble())}%"
    }
}

private fun Float.formatBpm(context: Context): String {
    return "${this.toInt()} ${context.getString(BR.string.heart_unit)}"
}

internal fun Long.formatTime() = (this % Duration.ofHours(24).toSeconds()).let {
    LocalTime.ofSecondOfDay(it).format(DateTimeFormatter.ofPattern("HH:mm"))
}
