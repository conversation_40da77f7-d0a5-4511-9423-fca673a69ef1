package com.stt.android.data.source.local.routes.popular

import android.database.Cursor
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.routes.LocalRoute
import kotlinx.coroutines.flow.Flow
import timber.log.Timber

@Dao
abstract class PopularRouteDao {

    @Query("SELECT * FROM popular_routes WHERE `key` = :key AND `activityType` = :activityType")
    internal abstract fun fetchByKey(key: String, activityType: Int): LocalPopularRoute?

    @Query("SELECT * FROM popular_routes WHERE routeId = :routeId AND `activityType` = :activityType")
    abstract suspend fun fetchById(routeId: String, activityType: Int): LocalPopularRoute?

    @Query("SELECT * FROM popular_routes WHERE locallyChanged = 1 AND isInProgress = 0")
    abstract suspend fun fetchUnsynced(): List<LocalPopularRoute>

    @Query("SELECT * FROM popular_routes WHERE isInProgress = 0 ORDER BY created DESC")
    abstract fun fetchAll(): Flow<List<LocalPopularRoute>>

    @Query("SELECT * FROM popular_routes WHERE _id = :routeId")
    abstract suspend fun fetchByLocalId(routeId: String): LocalPopularRoute?

    @Query("SELECT * FROM popular_routes WHERE _id = :routeId")
    abstract fun fetchByLocalIdCursor(routeId: String): Cursor

    @Query(
        """
        SELECT ${LocalPopularRoute.WATCH_ROUTE_ID}
        FROM popular_routes
        WHERE
            ${LocalPopularRoute.IS_IN_PROGRESS} = 0 AND
            ${LocalPopularRoute.DELETED} = 0 AND
            ${LocalPopularRoute.WATCH_ENABLED} != 0
    """
    )
    abstract fun fetchWatchEnabledRouteIdsCursor(): Cursor

    @Query("SELECT MAX(${LocalPopularRoute.WATCH_ROUTE_ID}) FROM popular_routes")
    abstract fun fetchLatestWatchRouteIdCursor(): Cursor

    @Query(
        """
        SELECT *
        FROM popular_routes
        WHERE
            ${LocalPopularRoute.IS_IN_PROGRESS} = 0 AND
            ${LocalPopularRoute.DELETED} = 0 AND
            ${LocalPopularRoute.WATCH_SYNC_STATE} IN (:states)
    """
    )
    abstract fun fetchRoutesWithStatesCursor(states: Array<String>): Cursor

    @Query(
        """
        SELECT COUNT(_id)
        FROM popular_routes
        WHERE deleted = 0 AND isInProgress = 0
    """
    )
    abstract suspend fun getRouteCount(): Int

    @Query(
        """
        SELECT COUNT(_id)
        FROM popular_routes
        WHERE deleted = 0 AND watchEnabled = 1 AND isInProgress = 0
    """
    )
    abstract suspend fun getWatchEnabledRouteCount(): Int

    @Insert
    internal abstract fun insert(route: LocalPopularRoute)

    @Update
    internal abstract fun update(route: LocalPopularRoute): Int

    @Query(
        """
        SELECT
            ${LocalPopularRoute.ID},
            ${LocalPopularRoute.KEY},
            ${LocalPopularRoute.ROUTE_ID},
            ${LocalPopularRoute.NAME},
            ${LocalPopularRoute.VISIBILITY},
            ${LocalPopularRoute.ACTIVITY_TYPE},
            ${LocalPopularRoute.AVERAGE_SPEED},
            ${LocalPopularRoute.TOTAL_DISTANCE},
            ${LocalPopularRoute.ASCENT},
            ${LocalPopularRoute.DESCENT},
            ${LocalPopularRoute.START_POINT},
            ${LocalPopularRoute.CENTER_POINT},
            ${LocalPopularRoute.STOP_POINT},
            ${LocalPopularRoute.LOCALLY_CHANGED},
            ${LocalPopularRoute.DELETED},
            ${LocalPopularRoute.CREATED_DATE},
            ${LocalPopularRoute.MODIFIED_DATE},
            ${LocalPopularRoute.SEGMENTS_MODIFIED_DATE},
            ${LocalPopularRoute.WATCH_SYNC_STATE},
            ${LocalPopularRoute.WATCH_SYNC_RESPONSE_CODE},
            ${LocalPopularRoute.WATCH_ROUTE_ID},
            ${LocalPopularRoute.WATCH_ENABLED},
            ${LocalPopularRoute.IS_IN_PROGRESS},
            ${LocalPopularRoute.TURN_WAYPOINTS_ENABLED},
            ${LocalPopularRoute.META_DATA},
             x'' AS ${LocalPopularRoute.SEGMENTS}
        FROM popular_routes
        WHERE isInProgress = 0
        ORDER BY created DESC
        """
    )
    abstract fun fetchAllWithoutSegments(): Flow<List<LocalPopularRoute>>

    @Query(
        """
        UPDATE popular_routes
        SET watchEnabled = 0, locallyChanged = 1, modifiedDate = :modifiedDate, watchSyncState = 'IGNORED'
        WHERE watchRouteId = :watchRouteId
    """
    )
    abstract fun disableWatchSync(watchRouteId: Int, modifiedDate: Long)

    open fun disableWatchSync(routeIds: List<Int>) {
        routeIds.forEach { disableWatchSync(it, System.currentTimeMillis()) }
    }

    @Query(
        """
        DELETE
        FROM popular_routes
    """
    )
    abstract fun deleteAll()

    @Query("DELETE FROM popular_routes WHERE _id IN (:routeIds)")
    abstract fun delete(routeIds: List<String>)

    open suspend fun deleteLocalPopularRoutes(routes: List<LocalPopularRoute>) {
        val routeIds = routes.mapNotNull { route ->
            if (route.fromServer) {
                fetchByKey(route.key, route.activityType)?.id
            } else {
                route.id
            }
        }
        if (routeIds.isNotEmpty()) {
            delete(routeIds)
        }
    }

    @Query(
        """
    UPDATE popular_routes
    SET deleted = 1, locallyChanged = 1, modifiedDate = :modifiedDate
    WHERE _id IN (:routeIds)
    """
    )
    abstract suspend fun markDeleted(routeIds: List<String>, modifiedDate: Long)

    open suspend fun markDeleted(routes: List<LocalPopularRoute>) {
        val routeIds = routes.mapNotNull { route ->
            if (route.fromServer) {
                fetchByKey(route.key, route.activityType)?.id
            } else {
                route.id
            }
        }
        if (routeIds.isNotEmpty()) {
            markDeleted(routeIds, System.currentTimeMillis())
        }
    }

    /**
     * Update existing [route] or insert if it doesn't exist.
     * The operation is executed within a transaction.
     */
    @Transaction
    open suspend fun upsert(route: LocalPopularRoute) {
        doUpsert(route)
    }

    /**
     * Update existing [routes] or insert if don't exist.
     * The operation is executed within a transaction.
     */
    @Transaction
    open suspend fun upsert(routes: List<LocalPopularRoute>) {
        routes.forEach { route ->
            doUpsert(route)
        }
    }

    private suspend fun doUpsert(route: LocalPopularRoute) {
        // All routes created locally have unique ID, but routes that come from server
        // do not have local IDs
        val existingRoute = if (route.fromServer) {
            fetchByKey(route.key, route.activityType)
        } else {
            runSuspendCatching {
                fetchByLocalId(route.id)
            }.getOrElse { e ->
                Timber.w(e, "PopularRoute with ID = %s not found, proceed with insert", route.id)
                null
            }
        }

        if (existingRoute != null) {
            Timber.d("Updating existing popularRoute: id=${existingRoute.id} watchRouteId=${existingRoute.watchRouteId} key=${existingRoute.key} segments.size=${existingRoute.segments.size}")
            // Refuse to clear the remote key, if the existing route already has it
            val key = route.key.takeUnless { it.isEmpty() } ?: existingRoute.key
            val hasModifications = hasModifications(route, existingRoute)
            // segmentsModifiedDate is stored only locally, LocalRoutes not originating from local DB
            // will most of the time have it same as modifiedDate. So we'll never want to use
            // the one from the updated route, keep existing or update to current timestamp
            val segmentsModifiedDate = if (route.segments != existingRoute.segments) {
                System.currentTimeMillis()
            } else {
                existingRoute.segmentsModifiedDate
            }

            val modifiedDate = if (hasModifications) {
                System.currentTimeMillis()
            } else if (route.fromServer) {
                existingRoute.modifiedDate
            } else {
                route.modifiedDate
            }

            update(
                route.copy(
                    id = existingRoute.id,
                    key = key,
                    modifiedDate = modifiedDate,
                    segmentsModifiedDate = segmentsModifiedDate
                )
            )
        } else {
            val newId = route.id.ifEmpty { LocalRoute.generateId() }
            // Re-sync routes not previously in app DB with watch
            val newWatchSyncState = if (route.watchEnabled) "PENDING" else route.watchSyncState
            val newWatchSyncResponseCode =
                if (newWatchSyncState == "PENDING") 0 else route.watchSyncResponseCode

            val newRoute = route.copy(
                id = newId,
                watchSyncState = newWatchSyncState,
                watchSyncResponseCode = newWatchSyncResponseCode,
                modifiedDate = System.currentTimeMillis(),
            )
            Timber.d("Inserting a new route: id=${newRoute.id} watchRouteId=${newRoute.watchRouteId} key=${newRoute.key} segments.size=${newRoute.segments.size}")
            insert(newRoute)
        }
    }

    /**
     * Returns true if route is modified so that we should update the [LocalRoute.modifiedDate] timestamp
     */
    private fun hasModifications(newRoute: LocalPopularRoute, existingRoute: LocalPopularRoute): Boolean =
        newRoute.watchRouteId != existingRoute.watchRouteId ||
            newRoute.name != existingRoute.name ||
            newRoute.visibility != existingRoute.visibility ||
            newRoute.activityType != existingRoute.activityType ||
            newRoute.averageSpeed != existingRoute.averageSpeed ||
            newRoute.totalDistance != existingRoute.totalDistance ||
            newRoute.startPoint != existingRoute.startPoint ||
            newRoute.centerPoint != existingRoute.centerPoint ||
            newRoute.stopPoint != existingRoute.stopPoint ||
            newRoute.deleted != existingRoute.deleted ||
            newRoute.createdDate != existingRoute.createdDate ||
            newRoute.segments != existingRoute.segments ||
            newRoute.turnWaypointsEnabled != existingRoute.turnWaypointsEnabled ||
            newRoute.metaData != existingRoute.metaData
}
