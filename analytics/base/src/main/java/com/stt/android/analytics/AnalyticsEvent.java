package com.stt.android.analytics;

import androidx.annotation.StringDef;
import com.google.firebase.analytics.FirebaseAnalytics;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * Constants for analytics event names.
 * <p>
 * When adding new events, update also the {@link EventName} StringDef.
 */
public abstract class AnalyticsEvent {

    /**
     * Common events
     */

    // Action events
    public static final String APP_OPEN = "AppOpen";
    public static final String FIRST_OPEN = "FirstOpen";
    public static final String SIGN_UP = "SignUp";
    public static final String SIGN_UP_ERROR = "SignUpError";
    public static final String LOG_IN = "LogIn";
    public static final String LOGIN_ERROR = "LogInError";
    public static final String FORGOT_PASSWORD = "ForgotPassword";
    public static final String WORKOUT_START = "WorkoutStart";
    public static final String WORKOUT_PAUSED = "WorkoutPaused";
    public static final String WORKOUT_DISCARDED = "WorkoutDiscarded";
    public static final String WORKOUT_SAVED = "WorkoutSaved";
    public static final String FOLLOW_USER = "FollowUser";
    public static final String FOLLOW_REJECTED = "FollowRejected";
    public static final String FOLLOW_ACCEPTED = "FollowAccepted";
    public static final String FOLLOW_ALL_FB_FRIENDS = "FollowAllFbFriends";
    public static final String FOLLOWER_REMOVED = "FollowerRemoved";
    public static final String SHARE_APP_LINK_ACTION = "ShareAppLinkAction";
    public static final String LOADING_VIDEO = "LoadingVideo";
    public static final String PLAY_VIDEO = "PlayVideo";
    public static final String START_BUTTON = "StartButton";
    public static final String FEED_SCROLLED = "FeedScrolled";
    public static final String EDIT_WORKOUT_SAVED = "EditWorkoutSaved";
    public static final String LOCATION_ADD_MANUAL_LOCATION_STARTED = "LocationAddManualLocationStarted";
    public static final String LOCATION_ADD_MANUAL_LOCATION_SAVED = "LocationAddManualLocationSaved";
    public static final String LOCATION_ADDED_LOCATION_DELETED = "LocationAddedLocationDeleted";
    public static final String LOCATION_ADDED_LOCATION_EDITING_STARTED = "LocationAddedLocationEditingStarted";
    public static final String LOCATION_ADDED_LOCATION_EDITING_SAVED = "LocationAddedLocationEditingSaved";
    public static final String LOCATION_CONFIRM_AUTOMATIC_LOCATION_STARTED = "LocationConfirmAutomaticLocationStarted";
    public static final String LOCATION_CONFIRM_AUTOMATIC_LOCATION_COMPLETED = "LocationConfirmAutomaticLocationCompleted"; // note: this event is too long for firebase
    public static final String LOCATION_AUTOMATIC_LOCATION_PERMISSION_TOGGLED = "LocationAutomaticLocationPermissionToggled"; // note: this event is too long for firebase
    public static final String SIGN_OUT = "SignOut";
    public static final String GOAL_SET_NEW = "GoalSetNew";
    public static final String MAP_CHANGE_MODE = "MapChangeMode";
    public static final String MAP_EXPLORE_ENTER_WORKOUT_DETAILS = "MapExploreEnterWorkoutDetails";
    public static final String ROUTE_PLANNING_STARTED = "RoutePlanningStarted";
    public static final String ROUTE_PLANNING_FINISHED = "RoutePlanningFinished";
    public static final String ROUTE_PLANNING_EDIT_ROUTE = "RoutePlanningEditRoute";
    public static final String ROUTE_PLANNING_SAVE_ROUTE = "RoutePlanningSaveRoute";
    public static final String ROUTE_PLANNING_CANCELLED = "RoutePlanningCancelled";
    public static final String ROUTE_PLANNING_INSTRUCTIONS_BUTTON_TAPPED = "RoutePlanningInstructionsButtonTapped";
    public static final String ROUTE_PLANNING_ACTIVITIES_CHANGED = "RoutePlanningActivitiesChanged";
    public static final String ROUTE_IMPORT_ERROR = "RouteImportRouteError";
    public static final String ROUTE_IMPORT = "RouteImportRoute";
    public static final String ROUTE_FOLLOW = "RouteFollow";
    public static final String ROUTE_DELETE = "RouteDelete";
    public static final String ROUTE_PLANNING_CLEAR_ROUTE = "RoutePlanningClearRoute";
    public static final String SATELLITE_DATA_DOWNLOAD_FAILED = "SatelliteDataDownloadFailed";
    public static final String CHECK_APP_NOTIFICATIONS = "CheckAppNotifications";
    public static final String COMMENT_WORKOUT = "CommentWorkout";
    public static final String SHARE_WORKOUT = "WorkoutShare";
    public static final String WORKOUT_SHARE_SCREEN = "WorkoutShareScreen";
    public static final String WORKOUT_SHARE_ADD_PHOTO = "WorkoutShareAddPhoto";
    public static final String BACKEND_SYNC_ERROR = "BackEndSyncError";
    public static final String INBOX_MESSAGE_LIST = "InboxMessageList";
    public static final String DIARY_GRAPH_TOGGLE = "DiaryGraphToggle";
    public static final String DIARY_GRANULARITY_CHANGED = "DiaryGranularityChanged";
    public static final String DIARY_CHANGE_SUB_GRAPH_TYPE = "DiaryChangeSubGraphType";
    public static final String DIARY_CHANGE_MAIN_GRAPH_TYPE = "DiaryChangeMainGraphType";
    public static final String RESET_PASSWORD_REQUESTED = "ChangePasswordRequested";
    public static final String RESET_PASSWORD_TRY_AGAIN = "ChangePasswordTryAgain";
    public static final String DELETE_ACCOUNT_REQUESTED = "DeleteAccountRequested";
    public static final String CONTACT_SUPPORT = "ContactSupport";
    public static final String DIARY_SUMMARY_SCREEN = "DiarySummaryScreen"; // This is the Summaries screen, now renamed "Totals." Analytics names remain unchanged to track the impact of this renaming.
    public static final String TRAINING_ZONE_SUMMARY_SCREEN = "TrainingZoneSummaryScreen";
    public static final String DIARY_CALENDAR_SCREEN = "DiaryCalendarScreen"; // Calendar in ST
    public static final String ROUTE_FROM_ACTIVITY = "RouteRouteFromActivity";
    public static final String NEW_USER_WELCOME_CARD_SEEN = "NewUserWelcomeCardSeen";
    public static final String NEW_USER_WELCOME_CARD_TAPPED = "NewUserWelcomeCardTapped";
    public static final String PROFILE_DESCRIPTION_ADD = "ProfileDescriptionAdd";
    public static final String PROFILE_DESCRIPTION_EDIT = "ProfileDescriptionEdit";
    public static final String PROFILE_PICTURE_BUTTON = "ProfilePictureButton";
    public static final String PROFILE_PICTURE_EDITED = "ProfilePictureAdded";
    public static final String MAP_SEARCH_GO_TO_LOCATION = "MapSearchGoToLocation";
    public static final String CHANGE_EMAIL_SAVE = "ChangeEmailSave";
    public static final String CHANGE_PHONE_SAVE = "ChangePhoneSave";
    public static final String WORKOUT_COMPARE_SCREEN = "WorkoutCompareScreen";
    public static final String USER_BLOCKED = "UserBlocked";
    public static final String USER_UNBLOCKED = "UserUnblocked";
    public static final String USER_REPORTED = "UserReported";
    public static final String DIARY_GRAPH_SWIPED = "DiaryGraphSwiped";
    public static final String CALENDAR_CHANGE_CALENDAR_LEVEL = "CalendarChangeCalendarLevel";
    public static final String ST_DIARY_SUMMARIES_CHANGE_TYPE = "DiarySummariesChangeSummaryType";
    public static final String WORKOUT_DETAILS_VALUE_DESCRIPTION_OPENED = "WorkoutDetailsValueDescriptionOpened";
    public static final String WORKOUT_DETAILS_GRID_VIEW_MORE = "WorkoutDetailsGridViewMore";
    public static final String CALENDAR_SCREEN = "CalendarScreen";
    public static final String CALENDAR_WORKOUT_LIST_SCREEN = "CalendarWorkoutListScreen";
    public static final String WAYPOINT_CREATION_STARTED = "WaypointCreationStarted";
    public static final String WAYPOINT_CREATED = "WaypointCreated";
    public static final String WAYPOINT_SAVE_EDITED = "WaypointSaveEdited";
    public static final String WAYPOINT_DELETE_WAYPOINT = "WaypointDeleteWaypoint";
    public static final String WAYPOINT_INSTRUCTION_BUTTON_TAPPED = "WaypointInstructionsButtonTapped";
    public static final String POPULAR_STARTING_POINT_TAPPED = "PopularStartingPointTapped";
    public static final String MAP_MODE_SLIDER_OPENED = "MapModeSliderOpened";
    public static final String PARTNER_SERVICES_LIST_FILTERED = "ConnectedServiceServicesListFiltered";
    public static final String US_STATE_CHOSEN = "USStateChosen";
    public static final String EXPORT_FILE = "ExportFile";

    // View events
    public static final String WELCOME_SCREEN = "WelcomeScreen";
    public static final String SIGN_UP_SCREEN = "SignUpScreen";
    public static final String LOGIN_SCREEN = "LogInScreen";
    public static final String FACEBOOK_FRIENDS_SCREEN = "FacebookFriendsScreen";
    public static final String FIND_PEOPLE_SCREEN = "FindPeopleScreen";
    public static final String FOLLOWING_SCREEN = "FollowingScreen";
    public static final String FOLLOWERS_SCREEN = "FollowersScreen";
    public static final String DIARY_SCREEN = "DiaryScreen";
    public static final String MAP_SCREEN = "MapScreen";
    public static final String MAP_EXPLORE_VIEW = "MapExploreView";
    public static final String SETTINGS_SCREEN = "SettingsScreen";
    public static final String HOME_SCREEN = "HomeScreen";
    public static final String ROUTE_MY_ROUTES_SCREEN = "RouteMyRoutesScreen";
    public static final String ROUTE_DETAILS_SCREEN = "RouteRouteDetailsScreen";
    public static final String SHARE_APP_LINK_SCREEN = "ShareAppLinkScreen";
    public static final String WORKOUT_SETTINGS_SCREEN = "WorkoutSettingsScreen";
    public static final String WORKOUT_SETUP_COMPLETE = "WorkoutSetupComplete";
    public static final String LIKE_WORKOUT = "LikeWorkout";
    public static final String EDIT_WORKOUT_SCREEN = "EditWorkoutScreen";
    public static final String USER_PROFILE_SCREEN = "UserProfileScreen";
    public static final String WORKOUT_DETAILS_SCREEN = "WorkoutDetailsScreen";
    public static final String WORKOUT_MULTISPORT_DETAILS_SCREEN = "WorkoutMultisportDetailsScreen";
    public static final String TERMS_UPDATED_SCREEN = "TermsUpdatedScreen";
    public static final String TERMS_AGREEMENT_SCREEN = "TermsAgreementScreen";
    public static final String TERMS_AGREED = "TermsAgreed";
    public static final String HELPSHIFT_LINK_CLICKED = "HelpshiftLinkClicked";
    public static final String PERMISSION_ENABLED_BY_BUTTON = "AppPermissionsEnabledByButton";
    public static final String RESET_PASSWORD_SCREEN = "ChangePasswordScreen";
    public static final String DELETE_ACCOUNT_SCREEN = "DeleteAccountScreen";
    public static final String DOWNLOAD_WORKOUT = "DownloadWorkout";
    public static final String DOWNLOAD_WORKOUT_ERROR = "DownloadWorkoutError";
    public static final String CHANGE_EMAIL_SCREEN = "ChangeEmailScreen";

    public static final String CHANGE_PHONE_SCREEN = "ChangePhoneScreen";
    public static final String REPORT_CONTENT = "ReportContent";
    public static final String MAP_WORKOUT_MAP_SCREEN = "MapWorkoutMapScreen";
    public static final String DIARY_CALENDAR_TAB_SCROLLED_TO_WORKOUT_MAP = "DiaryCalendarTabScrolledToWorkoutMap";
    public static final String PERMISSION_SCREEN = "PermissionScreen";
    public static final String PERMISSION_PROMPT_RESPONSE = "PermissionPromptResponse";
    public static final String PERMISSION_SKIPPED= "PermissionSkipped";
    public static final String ONBOARDING_VERIFICATION_SCREEN = "OnboardingVerificationScreen";
    public static final String ONBOARDING_VERIFICATION_EDIT_PHONE_NUMBER = "OnboardingVerificationEditPhoneNumber";
    public static final String ONBOARDING_VERIFICATION_RESEND_CODE = "OnboardingVerificationResendCode";
    public static final String ONBOARDING_VERIFICATION_ERROR = "OnboardingVerificationError";
    public static final String ONBOARDING_START_SCREEN = "OnboardingStartScreen";
    public static final String ONBOARDING_USERNAME_NOT_FOUND = "OnboardingUsernameNotFound";
    public static final String ONBOARDING_ASK_EMAIL_FOR_UNVERIFIED_USER_SCREEN = "OnboardingAskEmailForUnverifiedUserScreen"; // note: this event is too long for firebase
    public static final String ONGOING_WORKOUT_FULL_SCREEN_MAP = "OngoingWorkoutFullScreenMap";
    public static final String POPULAR_ROUTES_SCREEN = "PopularRoutesScreen";

    /**
     * Suunto specific events
     */

    // Action events
    public static final String SUUNTO_CONTINUE_TO_FB = "ContinueToFacebook";
    public static final String SUUNTO_ENABLE_BT_BUTTON = "PermissionEnableBluetoothButton";
    public static final String SUUNTO_PAIRING_WATCH_PAIRING_STARTED =
        "PairingWatchPairingStarted";
    public static final String SUUNTO_PAIRING_RESTART_PAIRING = "PairingRestartPairing";
    public static final String SUUNTO_SYNC_WATCH_UPDATE = "SyncWatchUpdate";
    public static final String SUUNTO_SYNC_WATCH_RETRY = "SyncWatchRetry";
    public static final String SUUNTO_FORGET_WATCH = "ForgetWatch";
    public static final String SUUNTO_TRAINING_PLAN_TOGGLED = "TrainingPlanToggled";
    public static final String SUUNTO_SIGN_UP_AND_OFFERS_SUBSCRIPTION = "SignUpNewsAndOffersSubscription";
    public static final String SUUNTO_SIGN_UP_AND_OFFERS_ERROR = "SignUpNewsAndOffersError";
    public static final String SUUNTO_ROUTE_TOGGLE_USE_IN_WATCH = "RouteToggleUseInWatch";
    public static final String SUUNTO_247_DATA = "247Data";
    public static final String SUUNTO_DAY_EDIT_GOALS_BUTTON = "DayDetailsEditGoalsButton";
    public static final String SUUNTO_DAY_DETAILS_SCREEN = "DayDetailsScreen";
    public static final String SUUNTO_OPEN_PHONE_SETTINGS = "OpenPhoneSettings";

    // View events
    public static final String SUUNTO_FB_VIA_PARTNER_SCREEN = "FacebookViaPartnerScreen";
    public static final String SUUNTO_ST_PARTNERING_SCREEN =
        "SuuntoAndSportsTrackerPartnerScreen";
    public static final String SUUNTO_BLUETOOTH_OFF_SCREEN = "BluetoothOffScreen";
    public static final String SUUNTO_PAIRING_WATCH_FOUND_SCREEN = "PairingWatchFoundScreen";
    public static final String SUUNTO_PAIRING_WATCH_PAIRED = "PairingWatchPaired";
    public static final String SUUNTO_WATCH_SCREEN = "WatchScreen";
    public static final String SUUNTO_WATCH_MANAGE_CONNECTION = "WatchManageConnectionScreen";
    public static final String SUUNTO_WATCH_SETTINGS_SCREEN = "WatchSettingsScreen";
    public static final String SUUNTO_WATCH_USER_GUIDE_OPENED = "WatchUserGuideOpened";
    public static final String SUUNTO_VIEW_EVENT_SYNC_WATCH_SUCCESSFUL =
        "SyncWatchSuccessfulScreen";
    public static final String SUUNTO_VIEW_EVENT_ONBOARDING_FLOW_FINISHED =
        "OnboardingFlowFinished";
    public static final String SUUNTO_VIEW_EVENT_ONBOARDING_FLOW = "OnboardingFlow";
    public static final String SUUNTO_VIEW_EVENT_ONBOARDING_FLOW_NAVIGATION =
        "OnboardingFlowNavigation";
    public static final String SUUNTO_CONNECTED_SERVICE_DETAIL_SCREEN = "ConnectedServiceDetailsScreen";
    public static final String SUUNTO_CONNECTED_SERVICE_LIST_SCREEN = "ConnectedServiceServicesListScreen";
    public static final String SUUNTO_NO_WATCH_PAIRED_DIALOG_SHOWN = "PairingAbortPairingPopupShown";
    public static final String SUUNTO_NO_WATCH_PAIRED_DIALOG_RESPONSE = "PairingAbortPairingPopupResponse";
    public static final String SUUNTO_WORKOUT_PLANNER_SCREEN = "WorkoutPlannerScreen";
    public static final String SUUNTO_WATCH_ONBOARDING_FLOW_VIDEO_COMPLETED = "WatchOnboardingFlowVideoCompleted";

    // System events
    public static final String SUUNTO_SYSTEM_EVENT_PAIRING_LOOKING_FOR_WATCHES =
        "PairingLookingForWatches";
    public static final String SUUNTO_SYSTEM_EVENT_PAIRING_ERROR = "PairingError";
    public static final String SUUNTO_PAIRING_WATCH_LIST_LINK_SHOWN = "PairingWatchListLinkShown";
    public static final String SUUNTO_PAIRING_WATCH_LIST_LINK_TAPPED = "PairingWatchListLinkTapped";
    public static final String SUUNTO_PAIRING_WATCH_LIST_SELECTION_MADE = "PairingWatchListSelectionMade";
    public static final String SUUNTO_AMBIT3_OR_TRAVERSE_FOUND = "PairingAmbit3TraverseFound";
    public static final String SUUNTO_SYSTEM_EVENT_DEBUG_DEVICE_CONNECTION_PROBLEM =
        "DebugDeviceConnectionProblem";
    public static final String SUUNTO_SYNC_WORKOUT_DATA = "WorkoutData";
    public static final String SUUNTO_SYNC_WORKOUT_DATA_DIVE = "WorkoutDataDive";
    public static final String SUUNTO_WORKOUT_DETAILS_DIVE_SHOW_EVENTS = "WorkoutDetailsDiveShowEvents";
    public static final String SUUNTO_CONNECTED_SERVICE_CONNECTION_ERROR =
        "ConnectedServiceConnectionError";
    public static final String SUUNTO_CONNECTED_SERVICE_CONNECTION_SUCCESSFUL =
        "ConnectedServiceConnectionSuccessful";
    public static final String SUUNTO_CONNECTED_SERVICE_CONNECTION_SUCCESSFUL_ON_CLIENT =
        "ConnectedServiceConnectionSuccessfulOnClient"; // note: this event is too long for firebase1
    public static final String SUUNTO_CONNECTED_SERVICE_CONNECTION_DISCONNECTED =
        "ConnectedServiceConnectionDisconnected";
    public static final String SUUNTO_DEBUG_SYSTEM_EVENT = "DebugSystemEventSending";
    public static final String SUUNTO_DEBUG_GPS_SYNC_NO_VALID_FILES = "DebugGpsSyncNoValidFiles";
    public static final String SUUNTO_DEBUG_CONNECTIVITY_SERVICE_RESTART =
        "DebugConnectivityServiceRestart";
    public static final String HELPSHIFT_ARTICLE_OPENED = "HelpshiftArticleOpened";

    // Sync analytics properties
    public static final String SUUNTO_SYNC_SYNC_METHOD = "SyncMethod";
    public static final String SUUNTO_SYNC_WATCH_RESULT = "WatchSyncResult";
    public static final String SUUNTO_SYNC_WORKOUTS = "Workouts";
    public static final String SUUNTO_SYNC_WORKOUTS_SYNC_DURATION = "WorkoutsSyncDuration";
    public static final String SUUNTO_SYNC_OVERALLSYNCRESULT = "OverallSyncResult";
    public static final String SUUNTO_SYNC_DURATION = "SyncDuration";
    public static final String SUUNTO_SYNC_GPS = "GPS";
    public static final String SUUNTO_SYNC_GPS_SYNC_DURATION = "GPSSyncDuration";
    public static final String SUUNTO_SYNC_SLEEP = "Sleep";
    public static final String SUUNTO_SYNC_SLEEP_SYNC_DURATION = "SleepSyncDuration";
    public static final String SUUNTO_SYNC_TRENDDATA = "CurrentStepAndEnergy";
    public static final String SUUNTO_SYNC_TRENDDATA_SYNC_DURATION = "CurrentStepAndEnergySyncDuration";
    public static final String SUUNTO_SYNC_RECOVERY = "StressAndRecovery";
    public static final String SUUNTO_SYNC_RECOVERY_SYNC_DURATION = "StressAndRecoverySyncDuration";
    public static final String SUUNTO_SYNC_SETTINGS = "Settings";
    public static final String SUUNTO_SYNC_SETTINGS_SYNC_DURATION = "SettingsSyncDuration";
    public static final String SUUNTO_SYNC_WORKOUTS_SYNCED = "WorkoutsSynced";
    public static final String SUUNTO_SYNC_WORKOUTS_NOT_SYNCED = "WorkoutsNotSynced";
    public static final String SUUNTO_SYNC_SYSTEM_EVENTS = "Timeline";
    public static final String SUUNTO_SYNC_SYSTEM_EVENTS_SYNC_DURATION = "TimelineSyncDuration";
    public static final String SUUNTO_SYNC_ROUTES = "Routes";
    public static final String SUUNTO_SYNC_ROUTES_SYNC_DURATION = "RoutesSyncDuration";
    public static final String SUUNTO_SYNC_POIS = "POIs";
    public static final String SUUNTO_SYNC_POIS_SYNC_DURATION = "POIsSyncDuration";
    public static final String SUUNTO_SYNC_SUUNTO_PLUS_GUIDES = "Suuntoplus";
    public static final String SUUNTO_SYNC_SUUNTO_PLUS_GUIDES_SYNC_DURATION = "SuuntoplusSyncDuration";
    public static final String SUUNTO_SYNC_WEATHER = "Weather";
    public static final String SUUNTO_SYNC_WEATHER_SYNC_DURATION = "WeatherSyncDuration";
    public static final String SUUNTO_SYNC_TRAINING_ZONE = "TrainingZone";
    public static final String SUUNTO_SYNC_TRAINING_ZONE_SYNC_DURATION = "TrainingZoneSyncDuration";
    public static final String SUUNTO_SYNC_TRAINING_ZONE_TRAINING = "TrainingZoneTraining";
    public static final String SUUNTO_SYNC_TRAINING_ZONE_PROGRESS = "TrainingZoneProgress";
    public static final String SUUNTO_SYNC_TRAINING_ZONE_RECOVERY = "TrainingZoneRecovery";
    public static final String SUUNTO_SYNC_TRAINING_ZONE_THRESHOLDS = "TrainingZoneThresholds";

    public static final String SUUNTO_SYNC_ZONE_SENSE = "ZoneSense";
    public static final String SUUNTO_SYNC_ZONE_SENSE_SYNC_DURATION = "ZoneSenseSyncDuration";
    public static final String SUUNTO_SYNC_ZONE_SENSE_BASELINE_VALUES = "BaselineValues";

    public static final String SUUNTO_SYNC_MINUTES_SINCE_LAST_SYNC = "MinutesSinceLastSync";
    public static final String SUUNTO_SYNC_ROUTE_TASKS = "RouteTasks";
    public static final String SUUNTO_SYNC_ROUTE_TASKS_FAILED = "RouteTasksFailed";
    public static final String SUUNTO_SYNC_WATCH_STATUS_AFTER_SYNC = "WatchStatusAfterSync";
    public static final String SUUNTO_SYNC_INVALID_PACKETS_DETECTED = "InvalidPacketsDetected";

    // Sync analytics property values
    public static final String SUUNTO_SYNC_MANUAL_METHOD = "Manual";
    public static final String SUUNTO_SYNC_BACKGROUND_METHOD = "Background";
    public static final String SUUNTO_SYNC_FOREGROUND_METHOD = "Foreground";
    public static final String SUUNTO_SYNC_HOURLY_METHOD = "Hourly";
    public static final String SUUNTO_SYNC_OK = "Ok";
    public static final String SUUNTO_SYNC_NONEWDATA = "NoNewData";
    public static final String SUUNTO_SYNC_SKIPPED = "Skipped";
    public static final String SUUNTO_SYNC_ERROR = "Error";
    public static final String SUUNTO_SYNC_ERROR_PREFIX = "Error: ";
    public static final String SUUNTO_SYNC_ROUTE_ERROR_TOO_MANY_ROUTES =
        "RoutePlanningTooManyRoutes";
    public static final String SUUNTO_SYNC_BUSY = "Busy";
    public static final String SUUNTO_SYNC_CONNECTED = "Connected";
    public static final String SUUNTO_SYNC_CONNECTION_LOST = "ConnectionLost";
    public static final String SUUNTO_SYNC_DISCONNECTED_BY_MIDDLEWARE = "DisconnectedByMiddleware";
    public static final String SUUNTO_SYNC_DISCONNECTED_BY_MOBILE_APP = "DisconnectedByMobileApp";
    public static final String SUUNTO_SYNC_WATCH_NOT_PAIRED = "WatchNotPaired";

    // Notification center events
    public static final String SUUNTO_NOTIFICATIONS_ON_WATCH_SCREEN = "NotificationsOnWatchScreen";
    public static final String SUUNTO_NOTIFICATIONS_ON_WATCH_APP_TOGGLED = "NotificationsOnWatchAppToggled";
    public static final String SUUNTO_NOTIFICATIONS_PROPERTY_APP_NAME = "AppName";
    public static final String SUUNTO_NOTIFICATIONS_PROPERTY_NEW_SETTING = "NewSetting";
    public static final String SUUNTO_NOTIFICATIONS_ON_WATCH_MUSIC_CONTROLS_USED =
        "NotificationsOnWatchMusicControlsUsed";

    public static final String SUUNTO_TENCENT_SDK_INIT_ERROR = "DebugTencentSDKInitError";

    // Sync analytics error messages
    public static final String SUUNTO_SYNC_RESULT_SERVICE_DISCONNECTED = "SyncResultService "
        + "disconnected";
    public static final String SUUNTO_SYNC_RESULT_SERVICE_BINDING_DIED = "SyncResultService "
        + "binding died";
    public static final String SUUNTO_SYNC_RESULT_SERVICE_BINDING_FAILED = "SyncResultService "
        + "binding failed";

    // Offline maps
    public static final String WATCH_MAPS_DOWNLOAD_MAPS_SCREEN = "WatchMapsDownloadMapsScreen";
    public static final String WATCH_MAPS_SEARCH_LOCATION = "WatchMapsSearchLocation";
    public static final String WATCH_MAPS_DOWNLOAD_MAP_STARTED = "WatchMapsDownloadMapStarted";
    public static final String WATCH_MAPS_CANCEL_DOWNLOAD = "WatchMapsCancelDownload";
    public static final String WATCH_MAPS_DELETE_REGION = "WatchMapsDeleteRegion";
    public static final String WATCH_MAPS_DOWNLOAD_MAP_BLOCKED = "WatchMapsDownloadMapBlocked";
    public static final String WATCH_MAPS_LIBRARY_SCREEN = "WatchMapsLibraryScreen";

    // fw version update
    public static final String SUUNTO_WATCH_FW_VERSION_UPDATED = "WatchFirmwareVersionUpdated";

    // Doze mode disable analytics
    public static final String DOZE_MODE_DISABLED = "DozeModeDisabled";
    public static final String DOZE_MODE_DISABLED_NOT_SUPPORTED = "NotSupported";
    public static final String DOZE_MODE = "DozeMode";

    // Sport modes customization analytics
    public static final String SPORT_MODE_CUSTOMIZATION_SCREEN = "SportModeCustomizationScreen";
    public static final String SPORT_MODE_LOADING_TIME = "SportModeLoadingTime";
    public static final String SPORT_MODE_SETTINGS_SCREEN = "SportModeSettingsScreen";
    public static final String SPORT_MODE_SETTINGS_SAVED = "SportModeSettingsSaved";
    public static final String SPORT_MODE_TOO_MANY = "SportModeTooMany";
    public static final String SPORT_MODE_ADD_NEW = "SportModeAddNew";
    public static final String SPORT_MODE_EDIT_CANCELLED = "SportModeEditCancelled";
    public static final String SPORT_MODE_SYNC_MODE = "SportModeSyncMode";
    public static final String SPORT_MODE_DELETE_CUSTOM_SPORT_MODE = "SportModeDeleteCustomSportMode";
    public static final String SPORT_MODE_SYNC_ERROR = "SportModeSyncError";
    public static final String SPORT_MODE_SYNC_SUCCESSFUL = "SportModeSyncSuccessful";
    public static final String SPORT_MODE_DISPLAY_NAVIGATION = "SportModeDisplayNavigation";
    public static final String SPORT_MODE_ADD_DISPLAY = "SportModeAddDisplay";
    public static final String SPORT_MODE_DELETE_DISPLAY = "SportModeDeleteDisplay";
    public static final String SPORT_MODE_REMOVE_FROM_SHORT_LIST = "SportModeRemoveFromShortList";
    public static final String SPORT_MODE_MULTI_SPORT_NAVIGATION = "SportModeMultiSportNavigation";
    public static final String SPORT_MODE_SAVE_ERROR = "SportModeSaveError";

    // GPS events
    public static final String ST_GPS_TRACKING_STARTED = "GPSTrackingStarted";
    public static final String ST_GPS_TRACKING_STOPPED = "GPSTrackingStopped";
    public static final String MOBILE_ASSISTED_GPS_STARTED = "MobileAssistedGPSStarted";
    public static final String MOBILE_ASSISTED_GPS_STOPPED = "MobileAssistedGPSStopped";

    // App start stability reporting.
    public static final String APP_START_STABILITY_REPORT = "AppStartStabilityReport";

    // Diary graph
    public static final String DIARY_GRAPH_VALUE_TAPPED = "DiaryGraphValueTapped";

    public static final String TOTP_GENERATION_ERROR = "DebugTOTPGenerationError";

    // Companion device association.
    public static final String LINK_ANDROID_COMPANION_DEVICE_RESPONSE = "LinkAndroidCompanionDeviceResponse";

    // Top routes
    public static final String POPULAR_ROUTES_BOTTOM_SHEET_OPENED = "PopularRoutesBottomSheetOpened";
    public static final String POPULAR_ROUTES_ROUTE_LIST_FOR_ACTIVITY_TYPE = "PopularRoutesRouteListForActivityType";
    public static final String POPULAR_ROUTES_ROUTE_SUGGESTION_VIEWED = "PopularRoutesRouteSuggestionViewed";
    public static final String POPULAR_ROUTES_SEARCH_HERE_TAPPED = "PopularRoutesSearchHereTapped";
    public static final String POPULAR_ROUTES_SAVE_ROUTE_STARTED = "PopularRoutesSaveRouteStarted";
    public static final String POPULAR_ROUTES_ROUTE_SAVED = "PopularRoutesSaveRouteSaved";

    // Dashboard
    public static final String HOME_SCREEN_DASHBOARD_NAVIGATION = "HomeScreenDashboardNavigation";
    public static final String HOME_SCREEN_BROWSE_DASHBOARD = "HomeScreenBrowseDashboard";

    // Dashboard widget customization
    public static final String DASHBOARD_WIDGET_EDIT_MODE = "DashboardWidgetEditMode";
    public static final String DASHBOARD_WIDGET_ADD_WIDGET = "DashboardWidgetAddWidget";
    public static final String DASHBOARD_WIDGET_REMOVE_WIDGET = "DashboardWidgetRemoveWidget";
    public static final String TOP_6_DASHBOARD_WIDGETS = "Top6DashboardWidgets";

    // Achievement
    public static final String ACHIEVEMENT_REACHED = "AchievementReached";

    // OTA update
    public static final String WATCH_MANAGEMENT_CHECK_FOR_UPDATES = "WatchManagementCheckForUpdates";
    public static final String WATCH_MANAGEMENT_DOWNLOAD_UPDATE_STARTED = "WatchManagementDownloadUpdateStarted";
    public static final String WATCH_MANAGEMENT_DOWNLOAD_UPDATE_COMPLETED = "WatchManagementDownloadUpdateCompleted";
    public static final String WATCH_MANAGEMENT_DOWNLOAD_UPDATE_FAILED = "WatchManagementDownloadUpdateFailed";
    public static final String WATCH_MANAGEMENT_DOWNLOAD_UPDATE_LEARN_MORE = "WatchManagementWatchUpdateLearnMore";

    // Share summary
    public static final String SHARE_SUMMARY_SCREEN = "ShareSummaryScreen";
    public static final String SHARE_SUMMARY = "ShareSummary";
    public static final String SHARE_SUMMARY_ERROR = "ShareSummaryError";

    // Google Play In-App review
    public static final String IN_APP_REVIEW_REQUESTED = "InAppReviewRequested";
    public static final String IN_APP_REVIEW_REQUEST_ERROR = "InAppReviewRequestError";
    
    // POIs
    public static final String POI_CREATION_STARTED = "POICreationStarted";
    public static final String POI_CREATED = "POICreated";
    public static final String POI_TOGGLE_USE_IN_WATCH = "POIToggleUseInWatch";
    public static final String POI_SAVE_EDITED = "POISaveEdited";
    public static final String POI_WATCH_MAXIMUM_REACHED = "POIWatchMaximumReached";
    public static final String POI_LIBRARY_SCREEN = "POILibraryScreen";
    public static final String POI_DELETED = "POIDeleted";

    // hr belt
    public static final String HR_BELT_PAIRED = "HRBeltPaired";
    public static final String HR_BELT_BUY_ONLINE_TAPPED = "HRBeltBuyOnlineTapped";

    // TSS/Progress tab in diary
    public static final String DIARY_VALUE_DESCRIPTION_OPENED = "DiaryValueDescriptionOpened";
    public static final String DIARY_EXTERNAL_LINK_TAPPED = "DiaryExternalLinkTapped";

    // Laps
    public static final String WORKOUT_DETAILS_LAP_TABLE_CHANGE_INTERVAL = "WorkoutDetailsLapTableChangeInterval";
    public static final String WORKOUT_DETAILS_LAP_TABLE_EDIT_COLUMNS = "WorkoutDetailsLapTableEditColumns";

    // System widgets
    public static final String SYSTEM_WIDGET_ADDED = "AndroidAppWidgetAdded";
    public static final String SYSTEM_WIDGET_REMOVED = "AndroidAppWidgetRemoved";
    public static final String SYSTEM_WIDGET_RESIZED = "AndroidAppWidgetResized";
    public static final String SYSTEM_WIDGET_OPEN_MOBILE_APP = "AndroidAppWidgetOpenMobileApp";

    // Glance widgets
    public static final String GLANCE_WIDGET_ADDED = "AppWidgetAdded";
    public static final String GLANCE_WIDGET_REMOVED = "AppWidgetRemoved";

    // Predefined replies
    public static final String PREDEFINED_REPLY_ACTION_FROM_WATCH = "PredefinedReplyActionFromWatch";
    public static final String PREDEFINED_REPLY_EDIT_SAVED = "PredefinedReplyEditSaved";

    // SuuntoPlus™ Store
    public static final String SUUNTO_PLUS_STORE_HOME_SCREEN = "SuuntoPlusStoreScreen";
    public static final String SUUNTO_PLUS_STORE_FILTERED_SCREEN = "SuuntoPlusStoreFiltered";
    public static final String SUUNTO_PLUS_STORE_ADD_TO_MY_LIBRARY = "SuuntoPlusStoreAddToMyLibrary";
    public static final String SUUNTO_PLUS_STORE_REMOVE_FROM_MY_LIBRARY = "SuuntoPlusStoreRemoveFromMyLibrary";
    public static final String SUUNTO_PLUS_DETAIL_SCREEN = "SuuntoPlusDetailsScreen";

    // SuuntoPlus™ Guides
    public static final String SUUNTO_PLUS_GUIDES_SELECTION_SCREEN = "SuuntoplusGuidesSelectionScreen";
    public static final String SUUNTO_PLUS_GUIDES_SELECTION_CHANGED = "SuuntoplusGuidesSelectionChanged";
    public static final String SUUNTO_PLUS_GUIDES_LINK_TAPPED = "SuuntoplusGuidesLinkTapped";

    // SuuntoPlus™ Features
    public static final String SUUNTO_PLUS_FEATURES_SELECTION_CHANGED = "SuuntoPlusSelectionChanged";

    // SuuntoPlus™ sports apps
    public static final String SUUNTO_PLUS_MY_SPORTS_APPS_SCREEN = "MySportsAppsScreen";
    public static final String SUUNTO_PLUS_MY_SPORTS_APPS_USE_IN_WATCH_TOGGLED = "MySportsAppsUseInWatchToggled";
    public static final String SUUNTO_PLUS_REPORT_SPORTS_APP = "ReportSportsApp";

    // Workout playback
    public static final String WORKOUT_PLAYBACK_START = "WorkoutPlaybackStart";
    public static final String WORKOUT_PLAYBACK_RESUME = "WorkoutPlaybackResume";
    public static final String WORKOUT_PLAYBACK_END = "WorkoutPlaybackEnd";
    public static final String WORKOUT_PLAYBACK_CHANGE_DIMENSION = "WorkoutPlaybackChangeDimension";

    // Graph analysis
    public static final String WORKOUT_ANALYSIS_SCREEN = "WorkoutAnalysisScreen";
    public static final String WORKOUT_ANALYSIS_FULLSCREEN = "WorkoutAnalysisFullscreen";
    public static final String WORKOUT_ANALYSIS_BOTTOM_SHEET_OPENED = "WorkoutAnalysisBottomSheetOpened";
    public static final String WORKOUT_ANALYSIS_CUSTOMIZE_GRAPHS = "WorkoutAnalysisCustomizeGraphs";
    public static final String WORKOUT_ANALYSIS_MAP_MODE_CHANGED = "WorkoutAnalysisMapModeChanged";
    public static final String WORKOUT_ANALYSIS_FULLSCREEN_MODE = "WorkoutAnalysisFullscreenMode";
    public static final String WORKOUT_ANALYSIS_LAP_SELECTION_CHANGED = "WorkoutAnalysisLapSelectionChanged";

    // Dive Mode Customization
    public static final String DIVE_MODE_CUSTOMIZATION_SCREEN = "DiveModeCustomizationScreen";
    public static final String DIVE_MODE_CUSTOMIZATION_CREATE_MODE = "DiveModeCreateNew";
    public static final String DIVE_MODE_CUSTOMIZATION_EDIT_MODE = "DiveModeEditMode";
    public static final String DIVE_MODE_CUSTOMIZATION_DELETE_MODE = "DiveModeDeleteMode";
    public static final String DIVE_MODE_CUSTOMIZATION_SET_ACTIVE_MODE = "DiveModeSetActiveMode";
    public static final String DIVE_MODE_CUSTOMIZATION_MODE_GAS_LIST_DETAILS = "DiveModeGasListDetails";
    public static final String DIVE_MODE_CUSTOMIZATION_MODE_SAVED = "DiveModeSaved";
    public static final String DIVE_MODE_CUSTOMIZATION_SYNC_RESULT = "DiveModeSyncResult";

    // Long term analysis
    public static final String TRENDS_SCREEN = "TrendsScreen";
    public static final String TRENDS_VIEW_CHANGED = "TrendsViewChanged";
    public static final String TRENDS_GRAPH_SETUP_CHANGED = "TrendsGraphSetupChanged";
    public static final String TRENDS_GRAPH_PERIOD_CHANGED = "TrendsGraphPeriodChanged";
    public static final String TRENDS_GRAPH_VALUE_TAPPED = "TrendsGraphValueTapped";

    // Tags analysis
    public static final String WORKOUT_TAG_ADDED = "WorkoutTagAdded";
    public static final String WORKOUT_TAG_REMOVED = "WorkoutTagRemoved";

    // Questionnaire
    public static final String USER_PROFILE_SURVEY_SKIPPED = "UserProfileSurveySkipped";
    public static final String USER_PROFILE_SURVEY_FAVORITE_SPORTS = "UserProfileSurveyFavoriteSports";
    public static final String USER_PROFILE_SURVEY_MOTIVATIONS = "UserProfileSurveyMotivations";

    // Workout planner
    public static final String SUUNTO_WORKOUT_PLANNER_PLAN_STARTED = "WorkoutPlannerPlanStarted";
    public static final String SUUNTO_WORKOUT_PLANNER_STEP_COMPLETED = "WorkoutPlannerStepCompleted";
    public static final String SUUNTO_WORKOUT_PLANNER_PLAN_SAVED = "WorkoutPlannerPlanSaved";
    // Watch widget customization
    public static final String WATCH_EDIT_WIDGETS_SCREEN = "WatchEditWidgetsScreen";
    public static final String WATCH_WIDGETS_USED_CHANGED = "WatchWidgetsUsedChanged";
    public static final String WATCH_WIDGETS_ORDER_CHANGED = "WatchWidgetsOrderChanged";
    public static final String PREMIUM_BUY_POPUP_SHOWN = "PremiumBuyPopUpShown";
    public static final String PREMIUM_FEATURE_SUMMARY_OPENED = "PremiumFeatureSummaryOpened";
    public static final String PREMIUM_LANDING_PAGE_OPENED = "PremiumLandingPageOpened";
    public static final String PREMIUM_PURCHASE_SCREEN_OPENED = "PremiumPurchaseScreenOpened";
    public static final String PREMIUM_BUY_BUTTON = "PremiumBuyButton";
    public static final String PREMIUM_SUBSCRIBED_SUCCESSFULLY = "PremiumSubscribedSuccessful";

    // Wifi setup
    public static final String WATCH_WIFI_CONNECT_SUCCESS = "WatchWifiConnectionSuccessful";
    public static final String WATCH_WIFI_CONNECT_ERROR = "WatchWifiConnectionError";

    // open weather
    public static final String WATCH_WEATHER_DATA_FETCH_FROM_API = "WatchWeatherDataFetchFromAPI";
    public static final String WATCH_WEATHER_DATA_SYNC_TO_WATCH = "WatchWeatherDataSyncToWatch";

    // firebase-only events
    public static final String OVER_5_WORKOUTS = "over_5_workouts";
    public static final String HAS_FOLLOWERS = "has_followers";
    public static final String FOLLOWS_SOMEONE = "follows_someone";
    public static final String ROUTES_PLANNED = "RoutesPlanned";
    public static final String DAYS_SINCE_FIRST_SESSION = "DaysSinceFirstSession";

    public static final String SUUNTO_WATCH_FIRMWARE_VERSIONS = "WatchFirmwareVersions";

    public static final String PERSONAL_RECORDS = "PersonalRecords";
    //Wechat sport
    public static final String WECHAT_SPORT_BIND = "InBindWechatSportsPage";

    // Preference screen
    public static final String ABOUT_PRIVACY_POLICY = "AboutPrivacyPolicy";
    public static final String ABOUT_SERVICE_TERMS = "AboutServiceTerms";
    public static final String ABOUT_DATA_PRACTICES = "AboutDataPractices";

    // Training zone Summary
    public static final String TRAINING_ZONE_SUMMARY_LAST_FILTERING = "TrainingZoneSummaryLastFiltering";

    public static final String SUUNTO_HEADSET_SETTING_CHANGED = "HeadphonesSettingsChanged";
    public static final String LAP_COLOURING_TOGGLED = "LapColouringToggled";

    public static final String FIRST_PAIRED_DEVICE = "PairingNewWatchPaired";

    public static final String SUPPORT_CHAT_BOT = "SupportChatBot";

    public static final String MENSTRUAL_CYCLE_ONBOARDING_DONE = "MenstrualCycleOnboardingDone";
    public static final String MENSTRUAL_CYCLE_PERIOD_LOGGED = "MenstrualPeriodLogged";

    public static final String OPEN_CUSTOMER_SERVICE = "OpenCustomerService";

    // DiveRouteUserFeedback
    public static final String DIVE_ROUTE_USER_FEEDBACK = "DiveRouteUserFeedback";

    // AI Planner
    public static final String AI_PROGRAM_DESCRIPTION_SCREEN = "ProgramDescriptionScreen";
    public static final String AI_PLAN_SURVEY_STARTED = "PlanSurveyStarted";
    public static final String AI_PLAN_SURVEY_SUBMITTED = "PlanSurveySubmitted";
    public static final String AI_MY_PLAN_SCREEN = "MyPlanScreen";
    public static final String AI_TRAINING_PLAN_ENDED = "TrainingPlanEnded";
    public static final String AI_PLANNED_WORKOUT_DETAILS_SCREEN = "PlannedWorkoutDetailsScreen";
    public static final String AI_PLAN_OVERVIEW_SCREEN = "PlanOverviewScreen";

    // SMC download debug event
    public static final String DEBUG_SMC_DOWNLOAD = "DebugSmcDownload";

    public static final String H5SHARE = "H5Share";
    public static final String H5CLICK = "H5Click";
    public static final String H5EXPOSURE = "H5Exposure";

    public static final String HEADPHONE_EXPOSURE = "HeadphoneFeaturesPagesExposed";
    public static final String HEADPHONE_CONNECTION = "HeadphoneManageConnectionScreenExposed";
    public static final String HEADPHONE_SETTINGS = "HeadphoneFeatureSetting";

    public static final String BANNER_EXPOSURE = "BannerExposure";
    public static final String BANNER_CLICK = "BannerClick";

    public static final String NAVIGATION_QUICKSTART_WITH_MOBILE = "NavigationQuickStartWithMobile";

    public static final String NAVIGATION_QUICKSTART_WITH_MOBILE_FAILED = "NavigationQuickStartWithMobileFailed";

    public static final String THIRTY_DAY_SUMMARY_VIEW_CLICK = "ThirtyDaySummaryViewClick";
    public static final String THIRTY_DAY_SUMMARY_GRAPH_CLICK = "ThirtyDaySummaryGraphClick";
    public static final String THIRTY_DAY_SUMMARY_GRAPH_SWIPE = "ThirtyDaySummaryGraphSwipe";

    public static final String FIRST_PAIRED = "FirstPaired";

    public static final String PUSH_DELIVERED = "PushDelivered";

    public static final String INBOX_MESSAGE_CLICK = "InboxMessageClick";
    public static final String INBOX_PAGE_EXPOSURE = "InboxPageExposure";
    public static final String NOTIFICATIONS_PAGE_CLICK = "NotificationsPageClick";

    public static final String APP_UPDATE_NOW_BUTTON_CLICK = "AppUpdateNowButtonClick";

    // Profile
    public static final String ALL_ACTIVITIES_PAGE_VIEW = "AllActivitiesPageView";
    public static final String SEARCH_CLICK = "SearchClick";
    public static final String SEARCH_REQUEST = "SearchRequest";

    //Widget Detail
    public static final String WIDGET_DETAIL_PAGE_EXPOSURE = "WidgetDetailPageExposure";
    public static final String LEAVE_WIDGETS_DETAIL_PAGE = "LeaveWidgetsDetailPage";
    public static final String WIDGET_DETAIL_PAGE_BUTTON_CLICK = "WidgetDetailPageButtonClick";

    // Training zone
    public static final String TRAINING_ZONE_PAGE_EXPOSURE = "TrainingZonePageExposure";
    public static final String TRAINING_ZONE_STATISTICS_TABLE_EXPOSURE =
        "TrainingStatisticsTableExposure";
    public static final String TRAINING_ZONE_STATISTICS_GRAPH_EXPOSURE =
        "TrainingStatisticsGraphExposure";
    public static final String TRAINING_ZONE_LEAVE = "LeaveTrainingZone";
    public static final String TRAINING_ZONE_TAB_LEAVE = "LeaveTrainingZoneTab";
    public static final String TRAINING_ZONE_STATISTICS_GRAPH_CHANGED =
        "TrainingStatisticsGraphChangedResult";
    public static final String TRAINING_ZONE_BUTTON_CLICK = "TrainingZoneButtonClick";

    @Retention(RetentionPolicy.SOURCE)
    @StringDef({
        FIRST_OPEN, SIGN_UP, SIGN_UP_ERROR, LOG_IN, LOGIN_ERROR, FORGOT_PASSWORD,
        WORKOUT_START, WORKOUT_PAUSED, WORKOUT_DISCARDED,
        WORKOUT_SAVED, FOLLOW_USER, FOLLOW_REJECTED, FOLLOW_ACCEPTED, FOLLOW_ALL_FB_FRIENDS,
        SHARE_APP_LINK_ACTION, LOADING_VIDEO, PLAY_VIDEO, START_BUTTON,
        FEED_SCROLLED, EDIT_WORKOUT_SAVED, SIGN_OUT, GOAL_SET_NEW, WELCOME_SCREEN, SIGN_UP_SCREEN,
        LOGIN_SCREEN, FACEBOOK_FRIENDS_SCREEN, FIND_PEOPLE_SCREEN, FOLLOWING_SCREEN,
        FOLLOWERS_SCREEN, DIARY_SCREEN, MAP_SCREEN, MAP_EXPLORE_VIEW, SETTINGS_SCREEN, HOME_SCREEN,
        ROUTE_MY_ROUTES_SCREEN,
        SHARE_APP_LINK_SCREEN, WORKOUT_SETTINGS_SCREEN, WORKOUT_SETUP_COMPLETE,
        LIKE_WORKOUT, EDIT_WORKOUT_SCREEN, SUUNTO_CONTINUE_TO_FB, SUUNTO_ENABLE_BT_BUTTON,
        SUUNTO_PAIRING_WATCH_PAIRING_STARTED, SUUNTO_SYNC_WATCH_UPDATE, SUUNTO_SYNC_WATCH_RETRY,
        SUUNTO_FORGET_WATCH, SUUNTO_FB_VIA_PARTNER_SCREEN, SUUNTO_ST_PARTNERING_SCREEN,
        SUUNTO_BLUETOOTH_OFF_SCREEN, SUUNTO_PAIRING_WATCH_FOUND_SCREEN, SUUNTO_PAIRING_WATCH_PAIRED,
        SUUNTO_WATCH_SCREEN, SUUNTO_WATCH_MANAGE_CONNECTION, SUUNTO_WATCH_SETTINGS_SCREEN,
        SUUNTO_WATCH_USER_GUIDE_OPENED, SUUNTO_NOTIFICATIONS_ON_WATCH_SCREEN,
        SUUNTO_VIEW_EVENT_SYNC_WATCH_SUCCESSFUL, SUUNTO_SYSTEM_EVENT_PAIRING_LOOKING_FOR_WATCHES,
        SUUNTO_SYSTEM_EVENT_PAIRING_ERROR,
        MAP_CHANGE_MODE,
        MAP_EXPLORE_ENTER_WORKOUT_DETAILS,
        ROUTE_PLANNING_FINISHED, ROUTE_PLANNING_SAVE_ROUTE, ROUTE_PLANNING_CANCELLED, ROUTE_FOLLOW,
        ROUTE_DELETE, ROUTE_DETAILS_SCREEN, ROUTE_PLANNING_STARTED,
        ROUTE_IMPORT, ROUTE_PLANNING_EDIT_ROUTE,
        ROUTE_PLANNING_CLEAR_ROUTE, ROUTE_IMPORT_ERROR,
        SUUNTO_SYSTEM_EVENT_DEBUG_DEVICE_CONNECTION_PROBLEM,
        LOCATION_ADD_MANUAL_LOCATION_STARTED,
        LOCATION_ADD_MANUAL_LOCATION_SAVED,
        LOCATION_ADDED_LOCATION_DELETED,
        LOCATION_ADDED_LOCATION_EDITING_STARTED,
        LOCATION_ADDED_LOCATION_EDITING_SAVED,
        LOCATION_CONFIRM_AUTOMATIC_LOCATION_STARTED,
        LOCATION_CONFIRM_AUTOMATIC_LOCATION_COMPLETED,
        LOCATION_AUTOMATIC_LOCATION_PERMISSION_TOGGLED,
        POPULAR_ROUTES_BOTTOM_SHEET_OPENED,
        POPULAR_ROUTES_ROUTE_LIST_FOR_ACTIVITY_TYPE,
        POPULAR_ROUTES_ROUTE_SUGGESTION_VIEWED,
        POPULAR_ROUTES_SEARCH_HERE_TAPPED,
        POPULAR_ROUTES_SAVE_ROUTE_STARTED,
        POPULAR_ROUTES_ROUTE_SAVED, SATELLITE_DATA_DOWNLOAD_FAILED, CHECK_APP_NOTIFICATIONS,
        USER_PROFILE_SCREEN, WORKOUT_DETAILS_SCREEN, COMMENT_WORKOUT, SHARE_WORKOUT, WORKOUT_SHARE_SCREEN, WORKOUT_SHARE_ADD_PHOTO, BACKEND_SYNC_ERROR, SUUNTO_SYNC_WORKOUT_DATA, SUUNTO_SYNC_WORKOUT_DATA_DIVE,
        SUUNTO_VIEW_EVENT_ONBOARDING_FLOW_NAVIGATION, SUUNTO_VIEW_EVENT_ONBOARDING_FLOW,
        SUUNTO_VIEW_EVENT_ONBOARDING_FLOW_FINISHED, TERMS_AGREED, TERMS_AGREEMENT_SCREEN, TERMS_UPDATED_SCREEN,
        SUUNTO_TRAINING_PLAN_TOGGLED, SUUNTO_SIGN_UP_AND_OFFERS_SUBSCRIPTION, SUUNTO_CONNECTED_SERVICE_LIST_SCREEN,
        SUUNTO_CONNECTED_SERVICE_DETAIL_SCREEN,SUUNTO_CONNECTED_SERVICE_CONNECTION_ERROR,
        SUUNTO_CONNECTED_SERVICE_CONNECTION_SUCCESSFUL, SUUNTO_CONNECTED_SERVICE_CONNECTION_SUCCESSFUL_ON_CLIENT,
        SUUNTO_CONNECTED_SERVICE_CONNECTION_DISCONNECTED, SUUNTO_DEBUG_SYSTEM_EVENT,
        INBOX_MESSAGE_LIST, DIARY_GRAPH_TOGGLE, SUUNTO_SYNC_WATCH_RESULT,
        DOZE_MODE, SPORT_MODE_CUSTOMIZATION_SCREEN, SPORT_MODE_LOADING_TIME, SPORT_MODE_SETTINGS_SCREEN,
        SPORT_MODE_SETTINGS_SAVED, SPORT_MODE_TOO_MANY, SPORT_MODE_ADD_NEW, SPORT_MODE_EDIT_CANCELLED,
        SPORT_MODE_SYNC_MODE, SPORT_MODE_DELETE_CUSTOM_SPORT_MODE, SPORT_MODE_SYNC_ERROR, SPORT_MODE_SYNC_SUCCESSFUL,
        SPORT_MODE_DISPLAY_NAVIGATION, SPORT_MODE_ADD_DISPLAY, SPORT_MODE_DELETE_DISPLAY,
        SPORT_MODE_REMOVE_FROM_SHORT_LIST, SPORT_MODE_MULTI_SPORT_NAVIGATION, SPORT_MODE_SAVE_ERROR,
        SUUNTO_ROUTE_TOGGLE_USE_IN_WATCH, SUUNTO_SYNC_ROUTE_ERROR_TOO_MANY_ROUTES,
        SUUNTO_DEBUG_CONNECTIVITY_SERVICE_RESTART, MOBILE_ASSISTED_GPS_STARTED, SUUNTO_NOTIFICATIONS_ON_WATCH_APP_TOGGLED,
        SUUNTO_NOTIFICATIONS_ON_WATCH_MUSIC_CONTROLS_USED,
        MOBILE_ASSISTED_GPS_STOPPED, SUUNTO_247_DATA, SUUNTO_DAY_DETAILS_SCREEN, SUUNTO_DAY_EDIT_GOALS_BUTTON,
        HELPSHIFT_LINK_CLICKED, SUUNTO_PAIRING_RESTART_PAIRING, SUUNTO_OPEN_PHONE_SETTINGS,
        PERMISSION_ENABLED_BY_BUTTON, ST_GPS_TRACKING_STARTED,
        ST_GPS_TRACKING_STOPPED, APP_START_STABILITY_REPORT, RESET_PASSWORD_SCREEN, RESET_PASSWORD_REQUESTED,
        RESET_PASSWORD_TRY_AGAIN, DELETE_ACCOUNT_REQUESTED, DELETE_ACCOUNT_SCREEN,
        CONTACT_SUPPORT, FOLLOWER_REMOVED, DIARY_SUMMARY_SCREEN, ROUTE_FROM_ACTIVITY,
        DOWNLOAD_WORKOUT, DOWNLOAD_WORKOUT_ERROR, TOTP_GENERATION_ERROR, NEW_USER_WELCOME_CARD_SEEN,
        NEW_USER_WELCOME_CARD_TAPPED, SUUNTO_WORKOUT_DETAILS_DIVE_SHOW_EVENTS, WORKOUT_DETAILS_VALUE_DESCRIPTION_OPENED,
        PROFILE_DESCRIPTION_ADD, PROFILE_DESCRIPTION_EDIT, PROFILE_PICTURE_BUTTON, PROFILE_PICTURE_EDITED, MAP_SEARCH_GO_TO_LOCATION, CHANGE_EMAIL_SCREEN, CHANGE_EMAIL_SAVE,
        WORKOUT_COMPARE_SCREEN, REPORT_CONTENT, HELPSHIFT_ARTICLE_OPENED, USER_BLOCKED, USER_UNBLOCKED, USER_REPORTED, DIARY_GRAPH_SWIPED,
        CALENDAR_CHANGE_CALENDAR_LEVEL, DIARY_GRANULARITY_CHANGED, DIARY_CHANGE_SUB_GRAPH_TYPE, DIARY_CHANGE_MAIN_GRAPH_TYPE,
        DIARY_CALENDAR_SCREEN, ST_DIARY_SUMMARIES_CHANGE_TYPE, MAP_WORKOUT_MAP_SCREEN, DIARY_CALENDAR_TAB_SCROLLED_TO_WORKOUT_MAP, PERMISSION_SCREEN,
        PERMISSION_PROMPT_RESPONSE,
        POPULAR_STARTING_POINT_TAPPED, MAP_MODE_SLIDER_OPENED,
        CALENDAR_WORKOUT_LIST_SCREEN, PERMISSION_SKIPPED, DIARY_GRAPH_VALUE_TAPPED, ONBOARDING_VERIFICATION_SCREEN,
        ONBOARDING_VERIFICATION_RESEND_CODE, ONBOARDING_VERIFICATION_EDIT_PHONE_NUMBER, ONBOARDING_VERIFICATION_ERROR,
        SUUNTO_SIGN_UP_AND_OFFERS_ERROR, WAYPOINT_CREATION_STARTED, WAYPOINT_CREATED, WAYPOINT_SAVE_EDITED,
        WAYPOINT_DELETE_WAYPOINT, WAYPOINT_INSTRUCTION_BUTTON_TAPPED, ONBOARDING_START_SCREEN,
        ONBOARDING_USERNAME_NOT_FOUND, ONBOARDING_ASK_EMAIL_FOR_UNVERIFIED_USER_SCREEN, SUUNTO_TENCENT_SDK_INIT_ERROR,
        WAYPOINT_DELETE_WAYPOINT, WAYPOINT_INSTRUCTION_BUTTON_TAPPED, ONGOING_WORKOUT_FULL_SCREEN_MAP,
        SUUNTO_SIGN_UP_AND_OFFERS_ERROR, LINK_ANDROID_COMPANION_DEVICE_RESPONSE,
        HOME_SCREEN_DASHBOARD_NAVIGATION, HOME_SCREEN_BROWSE_DASHBOARD,
        ACHIEVEMENT_REACHED, PARTNER_SERVICES_LIST_FILTERED, US_STATE_CHOSEN, SUUNTO_NO_WATCH_PAIRED_DIALOG_SHOWN, SUUNTO_PAIRING_WATCH_LIST_LINK_SHOWN,
        SUUNTO_NO_WATCH_PAIRED_DIALOG_RESPONSE, SUUNTO_PAIRING_WATCH_LIST_LINK_TAPPED,
        SUUNTO_PAIRING_WATCH_LIST_SELECTION_MADE, SUUNTO_AMBIT3_OR_TRAVERSE_FOUND,
        SUUNTO_WATCH_FW_VERSION_UPDATED, SUUNTO_DEBUG_GPS_SYNC_NO_VALID_FILES,
        WATCH_MANAGEMENT_CHECK_FOR_UPDATES,
        WATCH_MANAGEMENT_DOWNLOAD_UPDATE_STARTED,
        WATCH_MANAGEMENT_DOWNLOAD_UPDATE_COMPLETED,
        WATCH_MANAGEMENT_DOWNLOAD_UPDATE_FAILED,
        WATCH_MANAGEMENT_DOWNLOAD_UPDATE_LEARN_MORE,
        SHARE_SUMMARY_SCREEN, SHARE_SUMMARY, SHARE_SUMMARY_ERROR,
        IN_APP_REVIEW_REQUESTED, IN_APP_REVIEW_REQUEST_ERROR,
        WORKOUT_MULTISPORT_DETAILS_SCREEN,
        DEBUG_SMC_DOWNLOAD,
        POI_CREATION_STARTED,
        POI_CREATED,
        POI_TOGGLE_USE_IN_WATCH,
        POI_SAVE_EDITED,
        POI_WATCH_MAXIMUM_REACHED,
        POI_LIBRARY_SCREEN,
        POI_DELETED,
        HR_BELT_PAIRED,
        HR_BELT_BUY_ONLINE_TAPPED,
        DIARY_VALUE_DESCRIPTION_OPENED,
        DIARY_EXTERNAL_LINK_TAPPED,
        EXPORT_FILE,
        WORKOUT_DETAILS_LAP_TABLE_CHANGE_INTERVAL,
        WORKOUT_DETAILS_LAP_TABLE_EDIT_COLUMNS,
        SYSTEM_WIDGET_ADDED, SYSTEM_WIDGET_REMOVED, SYSTEM_WIDGET_RESIZED, SYSTEM_WIDGET_OPEN_MOBILE_APP,
        ROUTE_PLANNING_INSTRUCTIONS_BUTTON_TAPPED,
        ROUTE_PLANNING_ACTIVITIES_CHANGED,
        PREDEFINED_REPLY_ACTION_FROM_WATCH,
        PREDEFINED_REPLY_EDIT_SAVED,
        SUUNTO_PLUS_STORE_HOME_SCREEN,
        SUUNTO_PLUS_STORE_FILTERED_SCREEN,
        SUUNTO_PLUS_STORE_ADD_TO_MY_LIBRARY,
        SUUNTO_PLUS_STORE_REMOVE_FROM_MY_LIBRARY,
        SUUNTO_PLUS_DETAIL_SCREEN,
        SUUNTO_PLUS_GUIDES_SELECTION_SCREEN,
        SUUNTO_PLUS_GUIDES_SELECTION_CHANGED,
        SUUNTO_PLUS_FEATURES_SELECTION_CHANGED,
        SUUNTO_PLUS_GUIDES_LINK_TAPPED,
        SUUNTO_SYNC_SUUNTO_PLUS_GUIDES,
        WORKOUT_PLAYBACK_START,
        WORKOUT_PLAYBACK_RESUME,
        WORKOUT_PLAYBACK_END,
        WORKOUT_PLAYBACK_CHANGE_DIMENSION,
        CALENDAR_SCREEN,
        WORKOUT_ANALYSIS_SCREEN,
        WORKOUT_ANALYSIS_FULLSCREEN,
        WORKOUT_ANALYSIS_BOTTOM_SHEET_OPENED,
        WORKOUT_ANALYSIS_CUSTOMIZE_GRAPHS,
        WORKOUT_ANALYSIS_MAP_MODE_CHANGED,
        WORKOUT_ANALYSIS_FULLSCREEN_MODE,
        WORKOUT_ANALYSIS_LAP_SELECTION_CHANGED,
        DIVE_MODE_CUSTOMIZATION_SYNC_RESULT,
        DIVE_MODE_CUSTOMIZATION_SCREEN,
        DIVE_MODE_CUSTOMIZATION_CREATE_MODE,
        DIVE_MODE_CUSTOMIZATION_EDIT_MODE,
        DIVE_MODE_CUSTOMIZATION_DELETE_MODE,
        DIVE_MODE_CUSTOMIZATION_SET_ACTIVE_MODE,
        DIVE_MODE_CUSTOMIZATION_MODE_GAS_LIST_DETAILS,
        DIVE_MODE_CUSTOMIZATION_MODE_SAVED,
        TRENDS_SCREEN,
        TRENDS_VIEW_CHANGED,
        TRENDS_GRAPH_SETUP_CHANGED,
        TRENDS_GRAPH_PERIOD_CHANGED,
        TRENDS_GRAPH_VALUE_TAPPED,
        WORKOUT_TAG_ADDED,
        WORKOUT_TAG_REMOVED,
        USER_PROFILE_SURVEY_SKIPPED,
        USER_PROFILE_SURVEY_FAVORITE_SPORTS,
        USER_PROFILE_SURVEY_MOTIVATIONS,
        DASHBOARD_WIDGET_EDIT_MODE,
        DASHBOARD_WIDGET_ADD_WIDGET,
        DASHBOARD_WIDGET_REMOVE_WIDGET,
        SUUNTO_WORKOUT_PLANNER_SCREEN,
        SUUNTO_WORKOUT_PLANNER_PLAN_STARTED,
        SUUNTO_WORKOUT_PLANNER_STEP_COMPLETED,
        SUUNTO_WORKOUT_PLANNER_PLAN_SAVED,
        WATCH_WIDGETS_USED_CHANGED,
        WATCH_EDIT_WIDGETS_SCREEN,
        SUUNTO_WATCH_ONBOARDING_FLOW_VIDEO_COMPLETED,
        WATCH_WIDGETS_ORDER_CHANGED,
        SUUNTO_PLUS_MY_SPORTS_APPS_SCREEN,
        SUUNTO_PLUS_MY_SPORTS_APPS_USE_IN_WATCH_TOGGLED,
        PREMIUM_BUY_POPUP_SHOWN,
        PREMIUM_FEATURE_SUMMARY_OPENED,
        PREMIUM_LANDING_PAGE_OPENED,
        PREMIUM_PURCHASE_SCREEN_OPENED,
        PREMIUM_BUY_BUTTON,
        PREMIUM_SUBSCRIBED_SUCCESSFULLY,
        WATCH_MAPS_DOWNLOAD_MAPS_SCREEN,
        WATCH_MAPS_SEARCH_LOCATION,
        WATCH_MAPS_DOWNLOAD_MAP_STARTED,
        WATCH_MAPS_CANCEL_DOWNLOAD,
        WATCH_MAPS_DELETE_REGION,
        WATCH_MAPS_DOWNLOAD_MAP_BLOCKED,
        WATCH_MAPS_LIBRARY_SCREEN,
        WATCH_WIFI_CONNECT_SUCCESS,
        WATCH_WIFI_CONNECT_ERROR,
        WATCH_WEATHER_DATA_FETCH_FROM_API,
        WATCH_WEATHER_DATA_SYNC_TO_WATCH,
        SUUNTO_PLUS_REPORT_SPORTS_APP,
        OVER_5_WORKOUTS,
        HAS_FOLLOWERS,
        FOLLOWS_SOMEONE,
        ROUTES_PLANNED,
        DAYS_SINCE_FIRST_SESSION,
        SUUNTO_WATCH_FIRMWARE_VERSIONS,
        // copying also firebase specific event names
        FirebaseAnalytics.Event.AD_IMPRESSION,
        FirebaseAnalytics.Event.ADD_PAYMENT_INFO,
        FirebaseAnalytics.Event.ADD_TO_CART,
        FirebaseAnalytics.Event.ADD_TO_WISHLIST,
        FirebaseAnalytics.Event.APP_OPEN,
        FirebaseAnalytics.Event.BEGIN_CHECKOUT,
        FirebaseAnalytics.Event.CAMPAIGN_DETAILS,
        FirebaseAnalytics.Event.GENERATE_LEAD,
        FirebaseAnalytics.Event.JOIN_GROUP,
        FirebaseAnalytics.Event.LEVEL_END,
        FirebaseAnalytics.Event.LEVEL_START,
        FirebaseAnalytics.Event.LEVEL_UP,
        FirebaseAnalytics.Event.LOGIN,
        FirebaseAnalytics.Event.POST_SCORE,
        FirebaseAnalytics.Event.SEARCH,
        FirebaseAnalytics.Event.SELECT_CONTENT,
        FirebaseAnalytics.Event.SHARE,
        FirebaseAnalytics.Event.SIGN_UP,
        FirebaseAnalytics.Event.SPEND_VIRTUAL_CURRENCY,
        FirebaseAnalytics.Event.TUTORIAL_BEGIN,
        FirebaseAnalytics.Event.TUTORIAL_COMPLETE,
        FirebaseAnalytics.Event.UNLOCK_ACHIEVEMENT,
        FirebaseAnalytics.Event.VIEW_ITEM,
        FirebaseAnalytics.Event.VIEW_ITEM_LIST,
        FirebaseAnalytics.Event.VIEW_SEARCH_RESULTS,
        FirebaseAnalytics.Event.EARN_VIRTUAL_CURRENCY,
        FirebaseAnalytics.Event.SCREEN_VIEW,
        FirebaseAnalytics.Event.REMOVE_FROM_CART,
        FirebaseAnalytics.Event.ADD_SHIPPING_INFO,
        FirebaseAnalytics.Event.PURCHASE,
        FirebaseAnalytics.Event.REFUND,
        FirebaseAnalytics.Event.SELECT_ITEM,
        FirebaseAnalytics.Event.SELECT_PROMOTION,
        FirebaseAnalytics.Event.VIEW_CART,
        FirebaseAnalytics.Event.VIEW_PROMOTION,
        AI_PROGRAM_DESCRIPTION_SCREEN,
        AI_PLAN_SURVEY_STARTED,
        AI_PLAN_SURVEY_SUBMITTED,
        AI_MY_PLAN_SCREEN,
        AI_TRAINING_PLAN_ENDED,
        AI_PLANNED_WORKOUT_DETAILS_SCREEN,
        AI_PLAN_OVERVIEW_SCREEN,
        PREMIUM_SUBSCRIBED_SUCCESSFULLY,
        WECHAT_SPORT_BIND,
        ABOUT_PRIVACY_POLICY,
        ABOUT_SERVICE_TERMS,
        ABOUT_DATA_PRACTICES,
        TRAINING_ZONE_SUMMARY_LAST_FILTERING,
        SUUNTO_HEADSET_SETTING_CHANGED,
        LAP_COLOURING_TOGGLED,
        FIRST_PAIRED_DEVICE,
        WORKOUT_DETAILS_GRID_VIEW_MORE,
        TRAINING_ZONE_SUMMARY_SCREEN,
        SUPPORT_CHAT_BOT,
        MENSTRUAL_CYCLE_ONBOARDING_DONE,
        MENSTRUAL_CYCLE_PERIOD_LOGGED,
        OPEN_CUSTOMER_SERVICE,
        DIVE_ROUTE_USER_FEEDBACK,
        PERSONAL_RECORDS,
        H5SHARE,
        H5CLICK,
        H5EXPOSURE,
        HEADPHONE_EXPOSURE,
        HEADPHONE_CONNECTION,
        HEADPHONE_SETTINGS,
        BANNER_EXPOSURE,
        BANNER_CLICK,
        NAVIGATION_QUICKSTART_WITH_MOBILE,
        NAVIGATION_QUICKSTART_WITH_MOBILE_FAILED,
        THIRTY_DAY_SUMMARY_VIEW_CLICK,
        THIRTY_DAY_SUMMARY_GRAPH_CLICK,
        THIRTY_DAY_SUMMARY_GRAPH_SWIPE,
        FIRST_PAIRED,
        PUSH_DELIVERED,
        INBOX_MESSAGE_CLICK,
        INBOX_PAGE_EXPOSURE,
        NOTIFICATIONS_PAGE_CLICK,
        APP_UPDATE_NOW_BUTTON_CLICK,
        ALL_ACTIVITIES_PAGE_VIEW,
        SEARCH_CLICK,
        SEARCH_REQUEST,
        TOP_6_DASHBOARD_WIDGETS,
        WIDGET_DETAIL_PAGE_EXPOSURE,
        LEAVE_WIDGETS_DETAIL_PAGE,
        TRAINING_ZONE_PAGE_EXPOSURE,
        TRAINING_ZONE_STATISTICS_TABLE_EXPOSURE,
        TRAINING_ZONE_STATISTICS_GRAPH_EXPOSURE,
        TRAINING_ZONE_LEAVE,
        TRAINING_ZONE_TAB_LEAVE,
        TRAINING_ZONE_STATISTICS_GRAPH_CHANGED,
        TRAINING_ZONE_BUTTON_CLICK,
    })
    public @interface EventName {
    }
}
