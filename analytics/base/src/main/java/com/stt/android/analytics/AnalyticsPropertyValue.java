package com.stt.android.analytics;

import androidx.annotation.StringDef;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * Constants for analytics event property values.
 */
public abstract class AnalyticsPropertyValue {

    public static final String YES = "Yes";
    public static final String NO = "No";
    public static final String TRUE = "True";
    public static final String FALSE = "False";
    public static final String ON = "On";
    public static final String OFF = "Off";
    public static final String ADD = "Add";
    public static final String REPLACE = "Replace";
    public static final String NEW = "New";
    public static final String EDITED = "Edited";
    public static final String CONFIRM = "Confirm";
    public static final String CANCEL = "Cancel";
    public static final String OWN = "Own";
    public static final String OTHER = "Other";
    public static final String NOT_RELEVANT = "NotRelevant";
    public static final String SHOWN = "Shown";
    public static final String NOT_SHOWN = "NotShown";
    public static final String NO_WATCH_PAIRED = "NoWatchPaired";
    public static final String UNKNOWN = "Unknown";

    public static final String ERROR_TYPE_UNKNOWN = "UnknownReason";

    public static final String SUUNTO_ALREADY_PAIRED = "AlreadyPaired";
    public static final String SUUNTO_KNOWN_DEVICE = "KnownDevice";

    public static final String SUUNTO_SYNC_STEP_MOVE_SYNC = "MoveSync";
    public static final String SUUNTO_SYNC_STEP_ROUTES_SYNC = "RoutesSync";
    public static final String SUUNTO_SYNC_STEP_SETTINGS_SYNC = "SettingsSync";
    public static final String SUUNTO_SYNC_STEP_GPS_SYNC = "GpsSync";

    public static final String SUUNTO_SPARTAN_TRAINER = "Suunto Spartan Trainer";
    public static final String SUUNTO_SPARTAN_ULTRA = "Suunto Spartan Ultra";
    public static final String SUUNTO_SPARTAN_SPORT = "Suunto Spartan Sport";
    public static final String SUUNTO_SPARTAN_SPORT_WHR = "Suunto Spartan Sport WHR";
    public static final String SUUNTO_SPARTAN_SPORT_WHRB = "Suunto Spartan Sport WHR Baro";
    public static final String SUUNTO_3 = "Suunto 3";
    public static final String SUUNTO_3_FITNESS = "Suunto 3 Fitness";
    public static final String SUUNTO_9 = "Suunto 9 Baro";
    public static final String SUUNTO_9_LIMA = "Suunto 9";
    public static final String SUUNTO_9_PEAK = "Suunto 9 Peak";
    public static final String SUUNTO_9_PEAK_PRO = "Suunto 9 Peak Pro";
    public static final String SUUNTO_5 = "Suunto 5";
    public static final String SUUNTO_5_PEAK = "Suunto 5 Peak";

    public static final String SUUNTO_VERTICAL = "Suunto Vertical";
    public static final String AMBIT3_PEAK = "Suunto Ambit3 Peak";
    public static final String AMBIT3_SPORT = "Suunto Ambit3 Sport";
    public static final String AMBIT3_RUN = "Suunto Ambit3 Run";
    public static final String AMBIT3_VERTICAL = "Suunto Ambit3 Vertical";

    public static final String SUUNTO_RACE = "Suunto Race";
    public static final String SUUNTO_RACE_S = "Suunto Race S";
    public static final String SUUNTO_OCEAN = "Suunto Ocean";
    public static final String TRAVERSE = "Suunto Traverse";
    public static final String TRAVERSE_ALPHA = "Suunto Traverse Alpha";
    public static final String EON_CORE = "Suunto EON Core";
    public static final String EON_STEEL = "Suunto EON Steel";
    public static final String EON_STEEL_BLACK = "Suunto EON Steel Black";
    public static final String SUUNTO_D5 = "Suunto D5";
    public static final String SUUNTO_7 = "Suunto 7";
    public static final String SUUNTO_RUN = "Suunto Run";
    public static final String SUUNTO_RACE_2 = "Suunto Race 2";
    public static final String SUUNTO_VERTICAL_2 = "Suunto Vertical 2";
    public static final String SUUNTO_GT = "Suunto GT"; // todo Use final analytics name
    public static final String SUUNTO_UNKNOWN_WATCH_MODEL = "Unknown";

    // For pairing help device list selection event
    public static final String AMBIT1 = "Suunto Ambit";
    public static final String AMBIT2 = "Suunto Ambit2";
    public static final String AMBIT3 = "Suunto Ambit3";

    // Cable connected dive devices shown in pairing help device list
    public static final String SUUNTO_D4F = "Suunto D4f";
    public static final String SUUNTO_D4I = "Suunto D4i";
    public static final String SUUNTO_D4I_NOVO = "Suunto D4i Novo";
    public static final String SUUNTO_COBRA3 = "Suunto Cobra3";
    public static final String SUUNTO_D6I_NOVO = "Suunto D6i Novo";
    public static final String SUUNTO_D6I = "Suunto D6i";
    public static final String SUUNTO_D6M = "Suunto D6M";
    public static final String SUUNTO_D6M_AUS = "Suunto D6M AUS";
    public static final String SUUNTO_D6_NOVO = "Suunto D6 Novo";
    public static final String SUUNTO_D9 = "Suunto D9";
    public static final String SUUNTO_DX = "Suunto DX";
    public static final String SUUNTO_VYPER_NOVO = "Suunto Vyper Novo";
    public static final String SUUNTO_ZOOP_NOVO = "Suunto Zoop Novo";

    public static final String SYNC_TYPE_GPS = "GPS";
    public static final String SYNC_TYPE_GLONASS = "Glonass";

    public static final String SUUNTO_NOT_BIANCA_USER = "NotBiancaUser";

    public static final String MAP_NO_HEATMAP = "NoHeatMap";
    public static final String MAP_NO_ROAD_SURFACE = "Hide";

    public static final String CALENDAR_LEVEL = "CalendarLevel";
    public static final String ST_DIARY_SUMMARY_TYPE = "SummaryType";
    public static final String SOURCE_CALENDAR_SCREEN = "CalendarScreen";
    public static final String SOURCE_WORKOUT_SHARE_ADD_PHOTOS = "WorkoutShareAddPhotos";
    public static final String ANALYSIS_GRAPH_TYPE_NOT_USED = "NotUsed";

    public static abstract class FollowUserOutcomeProperty {
        public static final String FOLLOW = "Follow";
        public static final String PENDING = "Pending";
        public static final String ERROR = "Error";
    }

    public static abstract class AddAllFbFriendsOutcomeProperty {
        public static final String SUCCESS = "Success";
        public static final String ERROR = "Error";
        public static final String PARTIAL = "Partial";
        public static final String UNDO = "Undo";
    }

    public static abstract class ShareAppLinkOutcomeProperty {
        public static final String NO_THANKS = "No Thanks";
        public static final String SHARE = "Share";
    }

    public static abstract class FacebookShare {
        public static final String NOT_SELECTED = "NotSelected";
        public static final String SUCCESS = "Success";
        public static final String CANCEL = "Cancel";
        public static final String ERROR = "Error";
    }

    public static abstract class SharingOptionVisibilityProperty {
        public static final String PUBLIC = "Public";
        public static final String FOLLOWERS = "Followers";
        public static final String SHARED_BY_LINK = "SharedByLink";
        public static final String PRIVATE = "Private";
    }

    public static abstract class HardwareProperty {
        public static final String HR = "HR";
        public static final String IN_MEMORY_HR = "inMemoryHR";
        public static final String CADENCE = "Cadence";
        public static final String ANDROID_WEAR = "AndroidWear";
    }

    public static abstract class SourceProperty {
        public static final String EDIT_BUTTON = "EditButton";
        public static final String WORKOUT_CARD_EDIT_BUTTON_IN_FEED = "WorkoutCardEditButtonInFeed";
        public static final String WORKOUT_CARD_EDIT_BUTTON_IN_DIARY = "WorkoutCardEditButtonInDiary";
        public static final String ADD_LOCATION_BUTTON = "AddLocationButton";
        public static final String WORKOUT_DETAILS = "WorkoutDetails";
        public static final String ADD_DESCRIPTION_AND_TAGS = "AddDescriptionAndTagsLink";
        public static final String STOP_AUTOMATIC_COMMUTE_TAGS_FLOW = "StopAutomaticCommuteTagsFlow";
    }

    public static abstract class WorkoutDetailsSourceProperty {
        public static final String PERSONAL_PROFILE_WORKOUT_LIST = "PersonalProfileWorkoutList";
        public static final String OTHERS_PROFILE_SCREEN = "OthersProfileScreen";
        public static final String PERSONAL_POSTS = "PersonalPosts";
        public static final String OTHERS_POSTS = "OthersPosts";
        public static final String DEEP_LINK = "DeepLink";
        public static final String LAST_ACTIVITY = "LastActivity";
        public static final String FEED = "Feed";
        public static final String PERSONAL_RECORD = "PersonalRecords";
        public static final String CALENDAR_WORKOUT_LIST_SCREEN = "CalendarWorkoutListScreen";
        public static final String MAP_SCREEN_WORKOUT_CARD = "MapScreenWorkoutCard";
        public static final String WORKOUT_DETAILS = "WorkoutDetails";
        public static final String DAY_DETAILS_SCREEN = "DayDetailsScreen";
        public static final String OTHER = "Other";
        public static final String WIDGET_DETAIL_PAGE = "WidgetDetailPage"; // TODO Apply in widget details
    }

    public static abstract class TargetAccountType {
        public static final String NORMAL = "Normal";
        public static final String AMBASSADOR = "Ambassador";
    }

    public static abstract class TargetRelationship {
        public static final String SELF = "Self";
        public static final String FOLLOWING_EACH_OTHER = "FollowingEachOther";
        public static final String FOLLOWING_TARGET = "FollowingTarget";
        public static final String FOLLOWED_BY_TARGET = "FollowedByTarget";
        public static final String NO_RELATIONSHIP = "NoRelationship";
    }

    public static abstract class GoalType {
        public static final String DURATION = "Duration";
        public static final String SLEEP = "Sleep";
        public static final String STEPS = "Steps";
        public static final String ENERGY = "Energy";
    }

    public static abstract class NewView {
        public static final String DURATION = "Duration";
        public static final String SLEEP_SCHEDULE = "SleepSchedule";
    }

    /**
     * These source properties are used for follow actions
     */
    public static abstract class FollowSourceProperty {
        public static final String PEOPLE_SEARCH = "PeopleSearch";
        public static final String FIND_FB_FRIENDS = "FindFBFriends";
        public static final String FOLLOW_SUGGESTIONS = "FollowSuggestions";
        public static final String FOLLOW_SUGGESTIONS_FEED = "FollowSuggestionsInFeed";
        public static final String LIKES_LIST = "LikesList";
        public static final String OWN_FOLLOWERS_LIST = "OwnFollowersList";
        public static final String PROFILE_VIEW = "OtherUserProfile";
        @Retention(RetentionPolicy.SOURCE)
        @StringDef({
            PEOPLE_SEARCH, FIND_FB_FRIENDS, FOLLOW_SUGGESTIONS, FOLLOW_SUGGESTIONS_FEED, LIKES_LIST,
            OWN_FOLLOWERS_LIST, PROFILE_VIEW,
        })
        public @interface SourceProperties {
        }
    }

    public static abstract class Onboarding3PageNameProperty {
        public static final String STEPS = "Steps";
        public static final String SLEEP = "Sleep";
        public static final String STRESS = "Stress";
        public static final String RECORD = "Record";
        public static final String GUIDANCE = "Guidance";
        public static final String APPGPS = "APPGps";
        public static final String END = "End";
    }

    public static abstract class OnboardingSuunto5PeakPageNameProperty {
        public static final String EXPLORE = "Explore";
        public static final String ROUTINE = "Routine";
        public static final String MUSIC = "Music";
        public static final String GUIDANCE = "Guidance";
        public static final String SLEEP = "Sleep";
        public static final String END = "End";
    }

    public static abstract class OnboardingSuunto9PageNameProperty {
        public static final String BATTERY = "Battery";
        public static final String TRAINING = "Training";
        public static final String EXPLORE = "Explore";
        public static final String CUSTOMIZE = "Customize";
        public static final String PARTNER = "Partner";
        public static final String END = "End";
    }

    public static abstract class OnboardingSuunto9PeakPageNameProperty {
        public static final String MAPS_ROUTES_POIS = "MapsRoutesPOIs";
        public static final String ANALYZE_VIEW_SUMMARIES = "AnalyzeViewSummaries";
        public static final String TRAINING_LOAD = "TrainingLoad";
        public static final String AUTOMATIC_UPDATES = "AutomaticUpdates";
        public static final String CUSTOMIZE = "Customize";
        public static final String END = "End";
    }

    public static abstract class OnboardingSuunto7PageNameProperty {
        public static final String WEAR_OS_INTRO = "WearOSIntro";
        public static final String POWER_BUTTON = "PowerButton";
        public static final String SPORTS_BUTTON = "SportsButton";
        public static final String CHOOSE_SPORT_MODE = "ChooseSportMode";
        public static final String BUTTONS_OR_TOUCH = "ButtonsOrTouch";
        public static final String VIEWS_DURING_EXERCISE = "ViewsDuringExercise";
        public static final String ENDING_THE_EXERCISE = "EndingTheExercise";
        public static final String END = "End";
    }

    public static abstract class OnboardingSuuntoD5PageNameProperty {
        public static final String NOTIFICATIONS = "Notifications";
        public static final String CONNECT_WIRELESSLY = "ConnectWirelessly";
        public static final String RELIVE_AND_SHARE = "ReliveAndShare";
        public static final String MATCH_YOUR_STYLE = "MatchYourStyle";
        public static final String CONNECT_AND_CUSTOMIZE = "ConnectAndCustomize";
        public static final String END = "End";
    }

    public static abstract class OnboardingSpartanPageNameProperty {
        public static final String READY_FROM_START = "ReadyFromStart";
        public static final String MORE_THAN_TRAINING = "MoreThanTraining";
        public static final String EXPLORE_MORE = "ExploreMore";
        public static final String SHARE = "Share";
        public static final String END = "End";
    }

    public static abstract class OnboardingEonPageNameProperty {
        public static final String AUTOMATIC_LOCATION = "AutomaticLocation";
        public static final String PROMINENT_VIEW = "ProminentView";
        public static final String CUSTOMIZE = "Customize";
        public static final String EXTREME_DURABILITY = "ExtremeDurability";
        public static final String WIRELESS_TANK_PRESSURE = "WirelessTankPressure";
        public static final String CONNECT_WIRELESSLY = "ConnectWirelessly";
        public static final String RELIVE_AND_SHARE = "ReliveAndShare";
        public static final String CONNECT_AND_TRANSFER = "ConnectAndTransfer";
        public static final String END = "End";
    }

    public static abstract class OnboardingNG3PageNameProperty {
        public static final String START_SCREEN = "StartScreen";
        public static final String STARTING_EXERCISE = "StartingExercise";
        public static final String WIDGETS = "Widgets";
        public static final String NAVIGATION = "Navigation";
        public static final String END = "End";
    }
    public static abstract class OnboardingCallToActionProperty {
        public static final String START = "StartOnboarding";
        public static final String GUIDANCE = "TrainingGuidance";
        public static final String CALIBRATION = "WatchCalibration";
        public static final String READ_MORE = "ReadMore";
        public static final String VISIT_HELP = "VisitHelp";
        public static final String HOW_TO_USE = "HowToUse";
        public static final String UPGRADE_WATCH_FOR_NEW_ALGORITHM = "UpgradeWatchForNewAlgorithm";
        public static final String SET_SPORT_LIST = "SetSportList";
        public static final String SELECT_WATCH_WIDGETS = "SelectWatchWidgets";
        public static final String SETUP_WIFI = "SetupWifi";
    }

    public static abstract class OnboardingReactionProperty {
        public static final String START = "Start";
        public static final String SKIP = "Skip";
        public static final String TURN_ON = "TurnOn";
        public static final String CALIBRATE = "Calibrate";
        public static final String READ = "Read";
        public static final String VISIT = "Visit";
        public static final String HOW_TO_USE = "HowToUse";
        public static final String READ_MORE = "ReadMore";
        public static final String SET_SPORT_LIST = "SetSportList";
        public static final String SELECT_WATCH_WIDGETS = "SelectWatchWidgets";
        public static final String SETUP_WIFI = "SetupWifi";
    }

    public static abstract class OnboardingVerificationProperty {
        public static final String LOG_IN_PREVIOUSLY_UNVERIFIED_NUMBER = "LogInPreviouslyUnverifiedNumber";
        public static final String NETWORK_PROBLEM = "NetworkProblem";
        public static final String VERIFICATION_NUMBER_NOT_MATCH = "VerificationNumberNotMatching";
    }

    public static abstract class OtherGuidesProperty {
        public static final String WEAR_OS_BY_GOOGLE = "WearOSByGoogle";
    }

    public static abstract class NavigationMethodProperty {
        public static final String SWIPE = "Swipe";
        public static final String ARROWS = "Arrows";
        public static final String START_BUTTON = "StartButton";

        @Retention(RetentionPolicy.SOURCE)
        @StringDef({
            SWIPE, ARROWS, START_BUTTON
        })
        public @interface NavigationMethodProperties {
        }
    }

    public static abstract class SignupMethod {
        public static final String EMAIL = "Email";
        public static final String FACEBOOK = "Facebook";
    }

    public static abstract  class AutomaticLocationOutcome {
        public static final String SUGGESTED_LOCATION_CONFIRMED = "SuggestedLocationConfirmed";
        public static final String NEW_LOCATION_CHOSEN = "NewLocationChosen";
    }

    public static abstract class ManualLocationContext {
        public static final String EDIT_WORKOUT = "EditWorkout";
        public static final String ADD_MANUAL_WORKOUT = "AddManualWorkout";
    }

    public static abstract class AutomaticLocationContext {
        public static final String DIVE_STARTING_FLOW = "DiveStartingFlow";
        public static final String WATCH_SCREEN = "WatchScreen";
    }

    public static abstract class TermsContext {
        public static final String SIGN_UP = "SignUp";
        public static final String UPDATED_TERMS = "UpdatedTerms";
    }

    public static abstract class SignupErrorType {
        public static final String INVALID_PHONE = "InvalidPhoneNumber";
        public static final String INVALID_EMAIL = "InvalidEmailAddress";
        public static final String CAUSE_INVALID_COUNTRY_CODE = "InvalidCountryCode";
        public static final String CAUSE_NOT_A_NUMBER = "NotANumber";
        public static final String CAUSE_TOO_SHORT_AFTER_IDD = "TooShortAfterIDD";
        public static final String CAUSE_TOO_SHORT_NSN = "TooShortNSN";
        public static final String CAUSE_TOO_LONG = "TooLong";
    }

    public static abstract class SuuntoDiaryType {
        // todo Remove old diary related code when the old diary is removed
        public static final String WORKOUTS = "Workouts";
        public static final String STEPS = "Steps";
        public static final String CALORIES = "Calories";
        public static final String FITNESS_LEVEL = "FitnessLevel";
        // Shared plus long term analysis
        public static final String TRAINING = "Training";
        public static final String ACTIVITY = "Activity";
        public static final String SLEEP = "Sleep";
        public static final String RECOVERY = "Recovery";
        public static final String SCUBA_DIVING = "ScubaDiving";
        public static final String FREE_DIVING = "Freediving";
        public static final String CALENDAR = "Calendar";
        public static final String PROGRESS = "Progress";
        public static final String OVERVIEW = "Overview";
        public static final String SUMMARY = "Summary";
        public static final String STATISTICS = "Statistics";
    }

    public static abstract class DiaryCalendarGranularity {
        public static final String DAILY = "Day";
        public static final String WEEKLY = "Week";
        public static final String LAST_30_DAYS = "Last30Days";
        public static final String MONTHLY = "Month";
        public static final String YEARLY = "Year";
    }

    public static abstract class DiaryCalendarTimeRange {
        public static final String THIRTEEN_MONTHS = "13Months";
        public static final String EIGHT_WEEKS = "8Weeks";
        public static final String EIGHT_MONTHS = "8Months";
        public static final String EIGHT_YEARS = "8Years";
        public static final String CURRENT_WEEK = "CurrentWeek";
        public static final String CURRENT_MONTH = "CurrentMonth";
        public static final String CURRENT_YEAR = "CurrentYear";
        public static final String SIX_WEEKS = "6Weeks";
        public static final String SIX_MONTHS = "6Months";
        public static final String SEVEN_DAYS = "7Days";
        public static final String THIRTY_DAYS = "30Days";
        public static final String ONE_YEAR = "365Days";
    }

    public static abstract class MapFeatureChanged {
        public static final String MAP_MODE = "MapMode";
        public static final String MY_ROUTES = "MyRoutes";
        public static final String HEATMAP = "Heatmap";
        public static final String ROAD_SURFACE = "RoadSurfaceLayer";
        public static final String MAP_3D_MODE = "3DMap";
        public static final String POIS = "POIs";
    }

    public static abstract class Map3dModeInputMethod {
        public static final String BUTTON = "Button";
        public static final String TILTING_WITH_FINGERS = "TiltingWithFingers";
    }

    public static abstract class MyTracksGranularityProperty {
        public static final String OFF = "Off";
        public static final String THIS_WEEK = "ThisWeek";
        public static final String THIS_MONTH = "ThisMonth";
        public static final String THIS_YEAR = "ThisYear";
        public static final String PAST_WEEK = "PastWeek";
        public static final String PAST_MONTH = "PastMonth";
        public static final String LAST_30_DAYS = "Last30Days";
        public static final String PAST_YEAR = "PastYear";
        public static final String CUSTOM_DATES = "CustomDates";
    }

    public static abstract class SuuntoDiarySubGraphType {
        public static final String QUALITY = "Quality";
        public static final String HEARTRATE = "HeartRate";
        public static final String TRAINING = "Training";
        public static final String RESOURCES = "MorningResources";
        public static final String SPO2 = "BloodOxygen";
    }

    public static abstract class SuuntoDiaryContext {
        public static final String ENTERING_DIARY_SCREEN = "EnteringDiaryScreen";
        public static final String CHANGING_GRAPH_TYPE = "ChangingGraphType";
    }

    public static abstract class SuuntoDiaryGraphToggleSource {
        public static final String HOME_SCREEN_DASHBOARD_TOTAL_DURATION =
            "HomeScreenDashboardTotalDuration";
        public static final String HOME_SCREEN_DASHBOARD_CALENDAR = "HomeScreenDashboardCalendar";
        public static final String HOME_SCREEN_DASHBOARD_ACTIVITIES =
            "HomeScreenDashboardActivities";
        public static final String DIARY_SCREEN = "DiaryScreen";
    }

    public static abstract class SportModeType {
        public static final String FACTORY = "Factory";
        public static final String CUSTOM = "Custom";
    }

    public static abstract class SportModeSyncType {
        public static final String NEW_SPORT_MODE = "NewSportMode";
        public static final String CUSTOM_MODE_EDIT = "CustomModeEdit";
        public static final String SETTINGS_EDIT = "SettingsEdit";
    }

    public static abstract class SportModeMethod {
        public static final String SWIPE = "Swipe";
        public static final String TAP = "Tap";
    }

    public static abstract class HelpshiftSource {
        public static final String PAIR_WATCH = "PairWatchScreen";
        public static final String SYNC_FAILED = "SyncFailedScreen";
        public static final String PAIRING_FAILED = "PairingFailedScreen";
        public static final String LOOKING_FOR_WATCHES = "LookingForWatchesScreen";
        public static final String NO_WATCHES_FOUND = "NoWatchesFoundScreen";
        public static final String CONNECTING_TO_EXISTING_WATCH = "ConnectingToExistingWatchScreen";
        public static final String EXISTING_WATCH_NOT_CONNECTED = "ExistingWatchNotConnectedScreen";
    }

    public static abstract class PhoneSettingsTrigger {
        public static final String NOTIFICATIONS_ON = "NotificationsOn";
        public static final String NOTIFICATIONS_OFF = "NotificationsOff";
    }

    public static abstract class PermissionButton {
        public static final String ALLOW_LOCATION = "AllowLocation";
        public static final String ALLOW_NEARBY_DEVICES = "AllowNearbyDevices";
        public static final String TURN_ON_LOCATION = "TurnOnLocation";
        public static final String TURN_ON_BLUETOOTH = "TurnOnBluetooth";
    }

    public static abstract class PermissionType {
        public static final String LOCATION = "Location";
        public static final String NEARBY_DEVICES = "NearbyDevices";
    }

    public static abstract class PermissionContext {
        public static final String ON_BOARDING = "Onboarding";
        public static final String PAIRING_WATCH = "PairingWatch";
    }

    public static abstract class PermissionResult {
        public static final String ALLOW = "Allow";
        public static final String DONT_AllOW = "DontAllow";
        public static final String ONLY_WHILE_USING_APP = "OnlyWhileUsingApp";
        public static final String ONLY_COARSE_LOCATION = "OnlyApproximateLocation";
    }

    public static abstract class DayDetailsScreenSourceOptions {
        public static final String DIARY_STEPS = "DiarySteps";
        public static final String DIARY_CALORIES = "DiaryCalories";
        public static final String DIARY_SLEEP = "DiarySleep";
        public static final String DAY_DETAILS_SCREEN = "DayDetailsScreen";
        public static final String DAY_DETAILS_SCREEN_SWIPE = "DayDetailsScreenSwipe";
        public static final String DAY_DETAILS_SCREEN_CALENDAR = "DayDetailsScreenCalendar";
        public static final String HOME_SCREEN_STEPS_GAUGE = "HomeScreenStepsGauge";
        public static final String HOME_SCREEN_SLEEP_GAUGE = "HomeScreenSleepGauge";
        public static final String HOME_SCREEN_CALORIES_GAUGE = "HomeScreenCaloriesGauge";
        public static final String HOME_SCREEN_DAY_TYPE_TEXT = "HomeScreenDayTypeText";
        public static final String CALENDAR_DAY = "CalendarDay";
        public static final String TAB_BAR = "TabBar";
    }

    public static abstract class DayType {
        public static final String TRAINING = "Training";
        public static final String ACTIVE = "Active";
        public static final String REST = "Rest";
        public static final String INCOMPLETE = "CurrentDay";
        public static final String UNKNOWN = "Unknown";
    }

    public static abstract class BottomBarProperty {
        public static final String BOTTOMBAR_NORMAL = "Normal";
        public static final String BOTTOMBAR_ALWAYS = "Always";
    }

    public static abstract class RouteImportProperty {
        public static final String FILE_SYSTEM = "FileSystem";
        public static final String IMPORT_BUTTON = "ImportButton";
        public static final String WORKOUT = "Workout";
    }

    public static abstract class ContactSupportSource {
        public static final String DELETE_ACCOUNT = "DeleteAccount";
        public static final String SIGN_UP_FLOW = "SignUpFlow";
        public static final String RESET_PASSWORD = "ChangePassword";
    }

    public static abstract class RouteFormatProperty {
        public static final String GPX = "GPX";
    }

    public static abstract class SuuntoSyncRoutesContext {
        public static final String SAVE_ROUTE = "SaveNewRoute";
        public static final String ROUTE_LIST = "ActivateFromRouteList";
        public static final String EDIT_ROUTE = "EditRoute";
        public static final String CHOOSE_ROUTE = "ChooseRouteToFollow";
        public static final String ROUTE_FROM_ACTIVITY = "RouteFromActivity";
    }

    public static abstract class MapSearchGoToLocationProperty {
        public static final String CHOOSE_FROM_SUGGESTIONS_LIST = "ChooseFromSuggestionsList";
    }

    public static abstract class CompareWorkoutScreenSource {
        public static final String FEED = "Feed";
        public static final String SUMMARY = "Summary";
        public static final String RANKINGLIST = "RankingList";
    }

    public static abstract class ReportProperty {
        public static final String WORKOUT = "Workout";
    }

    public static abstract class WeekdayProperty {
        public static final String MONDAY = "Monday";
        public static final String TUESDAY = "Tuesday";
        public static final String WEDNESDAY = "Wednesday";
        public static final String THURSDAY = "Thursday";
        public static final String FRIDAY = "Friday";
        public static final String SATURDAY = "Saturday";
        public static final String SUNDAY = "Sunday";
        public static final String UNKNOWN = "Unknown";
    }

    public static abstract class SummaryHighlightedProperty {
        public static final String TOTALS = "Totals";
        public static final String TIME = "Duration";
        public static final String DISTANCE = "Distance";
        public static final String ENERGY = "Calories";
        public static final String AVG_HR = "AvgHR";
        public static final String AVG_SPEED = "AvgSpeed";
        public static final String AVG_PACE = "AvgPace";
        public static final String WORKOUTS = "Activities";
        public static final String ASCENT = "Ascent";
    }

    public static abstract class NewsLetterProperty {
        public static final String SIGN_UP = "SignUp";
        public static final String LOGIN = "LogIn";
    }

    public static abstract class WorkoutAnalysisScreenSource {
        public static final String MAP_OVERVIEW = "MapOverview";
        public static final String VIEW_ON_MAP_INSIGHTS_GRAPH = "ViewOnMapInsightsGraph";
        public static final String VIEW_ON_MAP_HR_ZONES = "ViewOnMapHRZones";
        public static final String MAP_THUMBNAIL = "MapThumbnail";
        public static final String PLAY_BUTTON_WORKOUT_DETAILS = "PlayButtonWorkoutDetails";
        public static final String PLAY_BUTTON_FEED = "PlayButtonFeed";
        public static final String NO_MAP_ANALYSIS_BUTTON = "NoMapAnalysisButton";
        public static final String MAP_ANALYSIS_OPEN_FULLSCREEN_BUTTON = "MapAnalysisOpenFullscreenButton";
        public static final String INSIGHTS_GRAPH = "InsightsGraph";
        public static final String HR_ZONES_GRAPH = "HRZonesGraph";
    }

    public static abstract class RelatedWatchModelProperty {
        public static final String SUUNTO7 = "Suunto 7";
    }

    public static abstract class HelpshiftArticleOpenedSourceProperty {
        public static final String HOW_TO_PAIR_BIG_BUTTON = "HowToPairBigButton";
        public static final String HOW_TO_PAIR_TEXT_LINK = "HowToPairTextLink";
        public static final String PAIRING_FAILED = "PairingFailed";
        public static final String NOT_CONNECTED = "NotConnected";
    }

    public static abstract class CompanionAssociationLinkingProperty {
        public static final String OK = "Ok";
        public static final String CANCEL = "Cancel";
        public static final String ERROR = "Error";
    }

    public static abstract class RoutePlanningContext {
        public static final String CREATE_NEW_ROUTE = "CreateNewRoute";
        public static final String EDIT_EXISTING_ROUTE = "EditExistingRoute";
    }

    public static abstract class PopularRoutesContentFoundContext {
        public static final String SEARCH_HERE_BUTTON_PRESSED = "SearchHereButtonPressed";
        public static final String AUTOMATIC_BACKGROUND_SEARCH = "AutomaticBackgroundSearch";
    }

    public static abstract class PopularRoutesBottomSheetOpenedMethod {
        public static final String TAP_ON_MAP_SCREEN = "TapOnMapScreen";
        public static final String SLIDE_FROM_BOTTOM = "SlideFromBottom";
    }

    public static abstract class PopularRoutesBottomSheetOpenedPopularRoutesStatus {
        public static final String ROUTES_AVAILABLE = "RoutesAvailable";
        public static final String NO_ROUTES = "NoRoutes";
        public static final String ZOOM_TOO_FAR = "ZoomTooFar";
    }

    public static abstract class PopularRoutesRouteSuggestionViewedMethod {
        public static final String TAP_ON_MAP = "TapOnMap";
        public static final String CHOOSE_FROM_BOTTOM_SLIDER = "ChooseFromBottomSlider";
    }

    public static abstract class PopularRoutesSaveRouteContext {
        public static final String DIRECTLY_FROM_THE_LIST = "DirectlyFromTheList";
    }

    public static abstract class PopularRoutesSaveRouteSavedRouteName {
        public static final String SUGGESTED = "Suggested";
        public static final String EDITED = "Edited";
    }

    public static abstract class RoutePlanningStartedLocationPinProperty {
        public static final String NO_LOCATION_PIN = "NoLocationPin";
        public static final String ROUTE_FROM_LOCATION = "RouteFromLocation";
        public static final String ROUTE_TO_LOCATION = "RouteToLocation";
        public static final String ROUTE_FROM_POPULAR_LOCATION = "RouteFromPopularLocation";
        public static final String ROUTE_TO_POPULAR_LOCATION = "RouteToPopularLocation";
        public static final String ROUTE_FROM_POI = "RouteFromPOI";
        public static final String ROUTE_TO_POI = "RouteToPOI";
    }

    public static abstract class RoutePlanningStartedSource {
        public static final String POI_DETAILS = "POIDetails";
    }

    public static abstract class MapScreenSource {
        public static final String NAVIGATION_BAR = "NavigationBar";
        public static final String HOME_SCREEN_DASHBOARD = "HomeScreenDashboard";
    }

    public static abstract class TrendsScreenSource {
        public static final String NAVIGATION_BAR = "NavigationBar";
    }

    public static abstract class PartnerConnectionFlowSource {
        public static final String DEEPLINK = "Deeplink";
    }

    /**
     * Dashboard
     */

    public static abstract class DashboardWidgetType {
        public static final String TOTAL_WORKOUTS_DURATION = "TotalWorkoutsDuration";
        public static final String CALENDAR = "Calendar";
        public static final String ACTIVITIES = "Activities";
        public static final String MAP = "Map";
        public static final String TRAINING = "Training";
        public static final String PROGRESS = "Progress";
        public static final String RESOURCES = "Resources";
        public static final String SLEEP = "Sleep";
        public static final String STEPS = "Steps";
        public static final String CALORIES = "Calories";
        public static final String CO2_EMISSIONS_REDUCED = "CO2EmissionsReduced";
    }

    public static abstract class MapOptionButtonUsed {
        public static final String MAP_STYLE = "MapStyle";
        public static final String HEAT_MAPS = "HeatMaps";
        public static final String ROAD_SURFACE = "RoadSurfaceLayer";
        public static final String MY_TRACKS = "MyTracks";
    }

    public static abstract class AchievementType {
        public static final String FIRST_OF_ACTIVITY_TYPE = "FirstOfActivityType";
        public static final String FURTHEST = "Furthest";
        public static final String FURTHEST_THIS_YEAR = "FurthestThisYear";
        public static final String FURTHEST_THIS_MONTH = "FurthestThisMonth";
        public static final String FASTEST = "Fastest";
        public static final String FASTEST_THIS_YEAR = "FastestThisYear";
        public static final String FASTEST_THIS_MONTH = "FastestThisMonth";
        public static final String FIRST_THIS_YEAR = "FirstThisYear";
        public static final String FIRST_THIS_MONTH = "FirstThisMonth";
        public static final String CUMULATIVE_ALL_TIME = "CumulativeAllTime";
        public static final String CUMULATIVE_THIS_YEAR = "CumulativeThisYear";
        public static final String CUMULATIVE_THIS_YEAR_ACTIVITY_TYPE = "CumulativeThisYearActivityType";
        public static final String CUMULATIVE_WEEK = "CumulativeWeek";
        public static final String CUMULATIVE_WEEK_ACTIVITY_TYPE = "CumulativeWeekActivityType";
        public static final String CUMULATIVE_MONTH_ACTIVITY_TYPE = "CumulativeMonthActivityType";
        public static final String EARLY_BIRD = "EarlyBird";
        public static final String FIRST_IN_N_MONTHS = "FirstIn%dMonths";
        public static final String FIRST_IN_N_WEEKS = "FirstIn%dWeeks";
    }

    public static abstract class ConnectedServicesListSource {
        public static final String SETTINGS_SCREEN = "SettingsScreen";
        public static final String PROFILE_SCREEN = "ConnectedServicesMenu";
        public static final String SERVICE_DETAILS_SCREEN = "ServiceDetailsScreen";
        public static final String SUUNTO_PLUS_GUIDES_SELECTION_SCREEN =
            "SuuntoplusGuidesSelectionScreen";
        public static final String STORE_PARTNERS_SCREEN = "StorePartnersScreen";
    }

    public static abstract class NoWatchPairedDialogResponse {
        public static final String ANSWERED = "Answered";
        public static final String CANCEL = "Cancel";
    }

    public static abstract class NoWatchPairedDialogReason {
        public static final String ALREADY_PAIRED_TO_ANOTHER_DEVICE = "AlreadyPairedToAnotherDevice";
        public static final String NO_SUUNTO_YET = "NoSuuntoYet";
        public static final String DEVICE_NOT_ARRIVED = "DeviceNotArrived";
        public static final String HAD_PROBLEMS_PAIRING = "HadProblemsInPairing";
        public static final String LATER = "Later";
    }

    public static abstract class WatchManagementCheckForUpdatesResult {
        public static final String CANCEL = "Cancel";
        public static final String UPDATE_FOUND = "UpdateFound";
        public static final String UPDATE_FOUND_BUT_NOT_RECOMMENDED =
            "UpdateFoundButNotRecommended";
        public static final String UPDATE_ALREADY_DOWNLOADED = "UpdateAlreadyDownloaded";
        public static final String WATCH_UP_TO_DATE = "WatchUpToDate";
        public static final String ERROR = "Error";

        @StringDef({
            CANCEL, UPDATE_FOUND, UPDATE_FOUND_BUT_NOT_RECOMMENDED, UPDATE_ALREADY_DOWNLOADED,
            WATCH_UP_TO_DATE, ERROR
        })
        public @interface Value {
        }
    }

    public static abstract class WatchManagementUpdateCheckMethod {
        public static final String MANUAL = "Manual";
        public static final String AUTOMATIC = "Automatic";
        public static final String DEEPLINK = "DeepLink";

        @StringDef({
            MANUAL, AUTOMATIC, DEEPLINK
        })
        public @interface Value {
        }
    }

    public static abstract class WatchManagementUpdateFlowStatus {
        public static final String UPDATE_FOUND = "UpdateFound";
        public static final String UPDATE_BEING_DOWNLOADED = "UpdateBeingDownloaded";
        public static final String UPDATE_READY_TO_INSTALL = "UpdateReadyToInstall";
        @StringDef({
            UPDATE_FOUND, UPDATE_BEING_DOWNLOADED, UPDATE_READY_TO_INSTALL
        })
        public @interface Value {
        }
    }

    public static abstract class WatchManagementFailureReason {
        public static final String PHONE_OUT_OF_STORAGE = "PhoneOutOfStorage";
        public static final String UPDATE_FILE_CORRUPTED = "UpdateFileCorrupted";
    }

    public static abstract class UnpairContext {
        public static final String MANUAL_FORGET = "ManualForget";
        public static final String LOG_OUT = "LogOut";
        public static final String SPONTANEOUS_UNPAIR = "SpontaneousUnpair";
        public static final String SWITCH_DEVICE = "SwitchWatch";
        public static final String PAIR_ANOTHER_DEVICE = "PairAnotherWatch";
    }

    public static abstract class UsStateContext {
        public static final String US_AS_NEW_LOCATION_IN_SETTINGS = "USAsNewLocationInSettings";
        public static final String IN_SETTINGS_SCREEN = "InSettingsScreen";
        public static final String EXISTING_USER_AFTER_UPDATE = "ExistingUserAfterUpdate";
        public static final String NEW_USER_ONBOARDING = "NewUserOnboarding";
    }

    public static abstract class UsStateOption {
        public static final String I_DONT_LIVE_IN_THE_US = "IDontLiveInUS";
    }

    public static abstract class DeviceCategory {
        public static final String PHONE = "Phone";
        public static final String TABLET = "Tablet";
        public static final String COMPUTER = "Computer";
    }

    // Share summaries

    public static abstract class SharePhotoInitiatedFrom {
        public static final String CALENDAR_PHOTO = "CalendarPhoto";
        public static final String TOTALS_PHOTO = "TotalsPhoto";
    }

    public static abstract class ShareType {
        public static final String PHOTO = "Photo";
        public static final String IMAGE = "Image";
        public static final String VIDEO = "Video";
        public static final String LINK = "Link";
    }

    public static abstract class ShareCalendarLevel {
        public static final String YEAR = "Year";
        public static final String WEEK = "Week";
        public static final String MONTH = "Month";
        public static final String LAST_30_DAYS = "30Days";
        public static final String UNKNOWN = "Unknown";
    }

    public static abstract class ShareErrorType {
        public static final String PHOTO_GENERATION_FAILED = "PhotoGenerationFailed";
        public static final String SHARING_FAILED = "SharingFailed";
    }

    public static abstract class POIAnalytics {
        public static final String NO_COMPATIBLE_WATCH_PAIRED = "NoCompatibleWatchPaired";
        public static final String SOURCE_POI_DETAILS = "POIDetails";
        public static final String SOURCE_POI_LIBRARY = "POILibrary";
    }

    public static abstract class WorkoutDetailsContext {
        public static final String WORKOUT_DETAILS_SCREEN = "WorkoutDetailsScreen";
        public static final String WORKOUT_MULTISPORT_DETAILS_SCREEN = "WorkoutMultisportDetailsScreen";
    }

    public static abstract class LinkNames {
        public static final String PROGRESS_READ_ABOUT_VALUES = "ProgressReadAboutValues";
        public static final String PROGRESS_READ_ABOUT_PROGRESSION = "ProgressReadAboutProgression";
    }

    public static abstract class ProfilePictureAddedNewOrChanged {
        public static final String PROFILE_PICTURE_NEW = "New";
        public static final String PROFILE_PICTURE_CHANGED = "Changed";
    }

    public static abstract class ExportType {
        public static final String ROUTE_GPX = "RouteGPX";
        public static final String WORKOUT_GPX = "WorkoutGPX";
        public static final String WORKOUT_FIT = "WorkoutFIT";
        public static final String ROUTE_KML = "RouteKML";
    }

    public static abstract class ExportResult {
        public static final String OK = "Ok";
        public static final String ERROR = "Error";
        public static final String NOT_INCLUDED = "NotIncluded";
    }

    public static abstract class ExportSource {
        public static final String WORKOUT_DETAILS = "WorkoutDetails";
        public static final String ROUTES_SCREEN = "RoutesScreen";
        public static final String ROUTE_DETAILS_SCREEN = "RouteDetailsScreen";
    }

    public static abstract class SystemWidgetType {
        public static final String CALORIES = "Calories";
        public static final String RESOURCES = "Resources";
        public static final String SLEEP = "Sleep";
        public static final String STEPS = "Steps";
        public static final String TRAINING = "Training";
        public static final String ASCENT = "Ascent";
        public static final String MINIMUM_HEART_RATE = "MinimumHeartRate";
        public static final String GOAL = "Goal";
        public static final String COMMUTE_THIS_MONTH = "CommuteThisMonth";
    }

    public static abstract class SystemWidgetLayoutType {
        public static final String NORMAL = "Normal";
        public static final String COMPACT = "Compact";
    }

    public static abstract class DayViewDetails {
        public static final String NO_SPO2_MEASURED = "NoSpO2Measured";
    }

    public static abstract class LapsTableType {
        public static final String ONE_KM_AUTO_LAP = "1 km";
        public static final String FIVE_KM_AUTO_LAP = "5 km";
        public static final String TEN_KM_AUTO_LAP = "10 km";
        public static final String ONE_MILE_AUTO_LAP = "1 mi";
        public static final String FIVE_MILE_AUTO_LAP = "5 mi";
        public static final String TEN_MILE_AUTO_LAP = "10 mi";
        public static final String DISTANCE_AUTO_LAP = "DistanceAutolap";
        public static final String DURATION_AUTO_LAP = "DurationAutolap";
        public static final String MANUAL = "Manual";
        public static final String INTERVAL = "Interval";
        public static final String DOWNHILL = "Downhill";
        public static final String DIVE = "Dive";
    }

    public static abstract class DeviceSwitch {
        public static final String NO_EXISTING_WATCHES_CONTEXT = "NoExistingWatches";
        public static final String SWITCH_WATCH_CONTEXT = "SwitchWatch";
        public static final String PAIR_ANOTHER_WATCH_CONTEXT = "PairAnotherWatch";
        public static final String TRADITIONAL_PAIRING_CONTEXT = "TraditionalPairing";
    }

    public static abstract class SuuntoPlusStoreFilteredFilterValue {
        public static final String FEATURES = "SportsApps";
        public static final String PARTNERS = "SuuntoPlusPartners";
        public static final String GUIDES = "GuidesBySuunto";
    }

    public static abstract class SuuntoPlusItemTypeValue {
        public static final String FEATURE = "SportsApp";
        public static final String PARTNER = "Partner";
        public static final String GUIDE = "Guide";
    }

    public static abstract class CategoryPropertyValue {
        public static final String TRAINING_PLAN = "TrainingPlan";
        public static final String TRAINING_GUIDE = "TrainingGuide";
    }

    public static abstract class SuuntoPlusDetailSourceValue {
        public static final String FEATURES_SCREEN = "StoreSportsAppsScreen";
        public static final String GUIDES_SCREEN = "StoreGuidesScreen";
        public static final String STORE_HOME_SCREEN = "StoreHomeScreen";
        public static final String H5 = "H5";
    }

    public static abstract class SuuntoPlusGuideLinkType {
        public static final String BOTTOM_LINK = "BottomLink";
    }

    public static abstract class WorkoutPlaybackTimelinePoint {
        public static final String START = "Start";
        public static final String MIDDLE = "Middle";
    }

    public static abstract class WorkoutPlaybackPauseReason {
        public static final String PAUSE_BUTTON = "PauseButton";
        public static final String MOVE_TIMELINE_POINT = "MoveTimelinePoint";
        public static final String SCREEN_EXIT = "ScreenExit";
        public static final String BOTTOM_SHEET_STATE_CHANGE = "BottomSheetStateChange";
        public static final String SELECTED_LAP_CHANGE = "SelectedLapChange";
    }

    public static abstract class WorkoutPlaybackDimension {
        public static final String TWO_DIMENSIONAL = "2D";
        public static final String THREE_DIMENSIONAL = "3D";
    }

    public static abstract class WorkoutPlaybackType {
        public static final String MOVING_CAMERA_2D = "MovingCamera2D";
        public static final String MOVING_CAMERA_3D = "MovingCamera3D";
        public static final String STATIC_CAMERA = "StaticCamera";
    }

    public static abstract class WorkoutPlaybackInitiatedFrom {
        public static final String FEED = "Feed";
        public static final String WORKOUT_DETAILS_SCREEN = "WorkoutDetailsScreen";
        public static final String WORKOUT_ANALYSIS_SCREEN = "WorkoutAnalysisScreen";
        public static final String UNKNOWN = "Unknown";
    }

    public static abstract class WorkoutAnalysisBottomSheetOpenedContext {
        public static final String MANUAL = "Manual";
        public static final String ENTERING_ANALYSIS_VIEW = "EnteringAnalysisView";
    }

    public static abstract class WorkoutAnalysisLapSelectionContext {
        public static final String DEFAULT_VIEW = "DefaultView";
        public static final String FULLSCREEN = "Fullscreen";
    }

    public static abstract class WorkoutAnalysisLapSelectionType {
        public static final String LAP = "Lap";
        public static final String WHOLE_EXERCISE = "WholeExercise";
        public static final String MANUAL_ZOOM = "ManualZoom";
    }

    public static abstract class WorkoutAnalysisLapType {
        public static final String AUTOMATIC = "Automatic";
        public static final String MANUAL = "Manual";
    }

    // Dive Mode Customization
    public static abstract class DiveModeCustomizationSaveType {
        public static final String CREATE_NEW = "CreateNew";
        public static final String EDIT_EXISTING = "EditExisting";
    }

    public static abstract class DiveModeCustomizationAltitude {
        public static final String M_0_TO_300 = "0-300m";
        public static final String M_300_TO_1500 = "300-1500m";
        public static final String M_1500_TO_3000 = "1500-3000m";
    }

    public static abstract class DiveModeCustomizationDiveStyle {
        public static final String SCUBA = "ScubaDiving";
        public static final String FREE = "Freediving";
        public static final String OFF = "Off";
    }

    public static abstract class DiveModeCustomizationGasListType {
        public static final String OC = "OC";
        public static final String CC = "CC";
    }

    public static abstract class SurveySkippedContext {
        public static final String NEW_USER_FLOW = "NewUserFlow";
        public static final String OTHER = "Other";
    }

    public static abstract class SurveyStepSkipped {
        public static final String FAVORITE_SPORTS = "FavoriteSports";
        public static final String MOTIVATION = "Motivation";
    }

    public static abstract class Motivation {
        public static final String ADVENTURES_AND_CONTESTS = "AdventuresAndContests";
        public static final String IMPROVE_IN_MYSPORT = "ImproveInMySport";
        public static final String EXPLORING_THE_OUTDOORS = "ExploringTheOutdoors";
        public static final String HEALTH_TRACKING = "HealthTracking";
        public static final String MENTAL_WELL_BEING = "MentalWellBeing";
        public static final String ENJOYING_BEING_ACTIVE = "EnjoyingBeingActive";
    }

    public static abstract class WorkoutPlanner {
        public static final String PLAN_TYPE_STRUCTURED_INTERVAL = "StructuredWorkout";
        public static final String PLANNER_SCREEN_SOURCE_DEEP_LINK = "Deeplink";
        public static final String PLANNER_SCREEN_SOURCE_WATCH_SCREEN = "WatchScreen";

        public static final String PLANNER_SCREEN_SOURCE_HOME_SCREEN = "HomeScreen";

        public static final String PLANNER_SCREEN_SOURCE_PLAN_CREATED = "PlanCreated";
        public static final String PLANNER_STEP_STEPS = "Steps";
    }

    public static abstract class WatchEditWidgetScreenSource {
        public static final String WATCH_SCREEN = "WatchScreen";
        public static final String ONBOARDING_FLOW = "OnboardingFlow";
    }

    public static abstract class DownloadMapsScreenSource {
        public static final String MAP_SCREEN = "MapScreen";
        public static final String WATCH_MANAGEMENT_SCREEN = "WatchManagementScreen";
        public static final String HOME_SCREEN_MENU = "HomeScreenMenu";
        public static final String ONBOARDING_FLOW = "OnboardingFlow";
        public static final String WATCH_MAPS_LIBRARY_SCREEN = "WatchMapsLibraryScreen";
    }

    public static abstract class MapsLibraryScreenSource {
        public static final String DOWNLOAD_MAP_SCREEN = "DownloadMapScreen";
        public static final String MAP_SCREEN = "MapScreen";
    }

    public static abstract class DownloadMapsIssueType {
        public static String WIFI_NOT_SETUP = "WifiNotSetUp";
        public static String WIFI_OFF = "WifiOff";
        public static String WATCH_STORAGE_FULL = "WatchStorageFull";
    }

    public static abstract class BuyPremiumPopupShownSource {
        public static final String TSS_ANALYSIS = "TrendsScreenProgressView";
        public static final String WORKOUT_RECORDING_SETTINGS = "WorkoutStartScreen";
        public static final String WORKOUT_RECORDING_VOICE_FEEDBACK_SETTINGS = "VoiceFeedbackOptionsScreen";
        public static final String WORKOUT_RECORDING_GHOST_SELECTION = "GhostSelectionScreen";
        public static final String WORKOUT_RECORDING_FOLLOW_ROUTE_SELECTION = "RouteSelectionScreen";
        public static final String WORKOUT_RECORDING_MAP = "WorkoutStartMapScreen";
        public static final String SAVE_RECORDED_WORKOUT_MAP = "WorkoutSaveMapScreen";
        public static final String WORKOUT_MAP_ANALYSIS = "WorkoutDetailsMapScreen";
        public static final String LANDSCAPE_LOCKED_FULLSCREEN_GRAPH_ANALYSIS = "WorkoutAnalysisFullScreen";
        public static final String EXPLORE_TAB = "MapScreen";
        public static final String MAP_SEARCH_SCREEN = "MapSearchScreen";
        public static final String CALENDAR_MAP_SCREEN = "CalendarWorkoutListMap";
        public static final String POI_DETAILS = "POILibraryPOIDetails";
        public static final String POPULAR_ROUTES = "PopularRoutesRouteListForActivityType";
        public static final String COMMUNITY_ACTIVITIES = "PopularRoutesCommunity";
        public static final String ROUTE_PLANNER = "RoutePlanningScreen";
        public static final String ROUTE_DETAILS = "RouteDetailsScreen";
        public static final String COMPARE_WORKOUTS_SCREEN = "WorkoutCompareScreen";
        public static final String COMPARE_WORKOUTS_MAP_SCREEN = "WorkoutCompareMapScreen";
        public static final String PREVIOUS_WORKOUTS_SCREEN = "WorkoutPreviousScreen";
        public static final String USER_WORKOUTS_SCREEN = "UserProfileMapScreen";
        public static final String RECENT_WORKOUT_TRENDS_SCREEN = "RecentWorkoutTrendsScreen";
        public static final String WORKOUT_DETAILS = "WorkoutDetails";
        public static final String FEED = "Feed";
    }

    public static abstract class BuyPremiumPopupShownReason {
        public static final String VOICE_FEEDBACK_ON = "VoiceFeedbackOn";
        public static final String ROUTE_SELECTED = "RouteSelected";
        public static final String GHOST_SELECTED = "GhostSelected";
        public static final String PREMIUM_MAP_STYLE_SELECTED = "MapStyle";
        public static final String PREMIUM_ROAD_SURFACE_SELECTED = "RoadSurface";
        public static final String PREMIUM_HEATMAP_SELECTED = "Heatmap";
        public static final String PREMIUM_MY_TRACKS_SELECTED = "MyTracks";
        public static final String CREATE_NEW_ROUTE = "NewRoute";
        public static final String SCREEN_ACCESS_NOT_ALLOWED = "NotAllowed";
        public static final String WORKOUT_PLAYBACK = "WorkoutPlayback";
        public static final String WORKOUT_IMPACT_TAG = "WorkoutImpactTag";

        public static final String EDIT_ROUTE = "EditRoute";

        public static final String COPY_ROUTE = "CopyRoute";
        public static final String MAP_STYLE = "MapStyle";
    }

    public static abstract class PremiumPurchaseFlowScreenSource {
        public static final String BUY_PREMIUM_POPUP = "BuyPopup";
        public static final String DEEPLINK = "Deeplink";
        public static final String PREMIUM_FEATURE_SUMMARY_SCREEN = "PremiumFeatureSummary";
        public static final String IN_DEPTH_PREMIUM_DESCRIPTION_SCREEN = "PremiumLandingPage";
        public static final String DASHBOARD_WIDGET_TYPE_PREFIX = "DashboardWidget";
        public static final String DASHBOARD_WIDGET_LIST = "DashboardWidgetList";
        public static final String WORKOUT_TAGS_EDITING_SCREEN = "WorkoutTagsEditingScreen";
        public static final String WORKOUT_COMPARE_VIEW = "WorkoutCompareView";
        public static final String WORKOUT_RECENT_TRENDS_VIEW = "WorkoutPreviousView";
        public static final String ROUTE_LIBRARY_SCREEN = "RouteLibraryScreen";
        public static final String POPULAR_ROUTES_SCREEN = "PopularRoutesScreen";
        public static final String ROUTE_DETAILS_SCREEN = "RouteDetailsScreen";
        public static final String FEED_TOP_BANNER = "HomeScreenBanner";
        public static final String USER_PROFILE_SCREEN = "UserProfileScreen";
        public static final String WORKOUT_DETAILS_IMPACT_TAG = "WorkoutDetailsImpactTag";
        public static final String SUMMARY = "Summary";
    }

    public static abstract class ShareTargetName {
        public static final String SAVE = "SaveToPictures";
        public static final String WECHAT = "WeChat";
        public static final String MOMENT = "WeChatMoments";
        public static final String WEIBO = "Weibo";
        public static final String REDBOOK = "Redbook";
        public static final String DOUYIN = "Douyin";
        public static final String SYSTEM = "System";
    }

    public static abstract class H5LinkName {
        public static final String ANNUAL_REPORT_2024 = "2024Annualreport";
    }

    public static abstract class H5ClickButtonName {
        public static final String IMAGE = "Image";
        public static final String VIDEO = "Video";
        public static final String LOTTERY = "Lottery";
    }

    public static abstract class MessageSource {
        public static final String EMAIL = "Email";
        public static final String INBOX = "Inbox";
        public static final String PUSH = "Push";
        public static final String POPUP = "Popup";
        public static final String INLINE = "Inline";
        public static final String BANNER = "Banner";
    }

    public static abstract class HeadphoneFeaturePageName {
        public static final String NECK_ASSESSMENT_LOGS = "NeckAssessmentLogs";
        public static final String NECK_ASSESSMENT_RESULT = "NeckAssessmentResult";
        public static final String NECK_ASSESSMENT_INSTRUCTIONS = "NeckAssessmentInstructions";
        public static final String JUMP_ASSESSMENT_RESULT = "JumpAssessmentResult";
    }

    public static abstract class SportSwitchValue {
        public static final String POOL_SWIMMING = "PoolSwimming";
        public static final String OPEN_WATER_SWIMMING = "OpenWaterSwimming";
        public static final String JUMP_ROPE = "JumpRope";
        public static final String RUNNING = "Running";
    }

    public static abstract class SoundModeValue {
        public static final String NORMAL = "Normal";
        public static final String OUTDOOR = "Outdoor";
        public static final String UNDERWATER = "Underwater";
    }

    public static abstract class MusicModeValue {
        public static final String BLUETOOTH_MUSIC = "BluetoothMusic";
        public static final String OFFLINE_MUSIC = "OfflineMusic";
    }

    public static abstract class HeadphoneFeatureName {
        public static final String ACTIVATE_VOICE_ASSISTANT = "ActivateVoiceAssistant";
        public static final String HEAD_MOVEMENT_CONTROL = "HeadMovementControl";
        public static final String NECK_FATIGUE_ALERT = "NeckFatigueAlertOn/Off";
        public static final String SWITCH_PLAYBACK_ORDER = "SwitchPlaybackOrder";
        public static final String NEXT_SONG = "NextSong";
        public static final String LAST_SONG = "LastSong";
        public static final String CHANGE_INTERNAL_PLAYLIST = "ChangeInternalPlaylist";
        public static final String MUSIC_MODE_CONTROL = "MusicModeControl";
        public static final String SOUND_MODE = "SoundMode";
        public static final String SPORTS_SWITCH = "SportsSwitch";
        public static final String HEAD_MOVEMENT_CONTROL_SWITCH = "HeadMovementControlSwitch";
        public static final String NECK_FATIGUE_ALERT_SWITCH = "NeckFatigueAlertSwitch";
        public static final String MUSIC_MODE = "MusicMode";
    }

    public static abstract class HeadphoneSettingValue {
        public static final String DOUBLE_PRESS = "DoublePress\"MFB\"";
        public static final String TRIPLE_PRESS = "TriplePress\"MFB\"";
        public static final String HOLD_DOWN_MFB = "HoldDown\"MFB\"For3seconds";
        public static final String HOLD_DOWN_SUBTRACT = "HoldDown\"-\"For3seconds";
        public static final String HOLD_DOWN_PLUS_SUBTRACT = "HoldDown\"+\"and\"-\"For3seconds";
        public static final String HOLD_DOWN_MFB_SUBTRACT = "HoldDown\"MFB\"and\"-\"For3seconds";
        public static final String NONE = "None";
    }

    public static abstract class AppOpenSourceType {
        public static final String APP_ICON = "AppIcon";
        public static final String APP_WIDGET = "AppWidget";
        public static final String DEEP_LINK = "Deeplink";
        public static final String PUSH_MESSAGE = "PushMessage";
    }

    public static abstract class AppOpenSourceTypeDetail {
        public static final String ACTIVITIES_SYNCED = "ActivitiesSynced";
        public static final String NEXT_CYCLE = "NextCycle";
        public static final String LOG_PERIOD = "LogPeriod";
        public static final String FRIENDS_ACTIVITIES = "FriendsActivities";
        public static final String LIKES = "Likes";
        public static final String COMMENTS = "Comments";
        public static final String NEW_FOLLOWERS = "NewFollowers";
    }

    public static abstract class PushMessageType {
        public static final String FRIENDS_ACTIVITIES = "FriendsActivities";
        public static final String LIKES = "Likes";
        public static final String COMMENTS = "Comments";
        public static final String NEW_FOLLOWERS = "NewFollowers";
        public static final String MARKETING = "Marketing";
        public static final String FACEBOOK = "Facebook";
    }

    public static abstract class NotificationMessageType {
        public static final String NEWS = "News";
        public static final String LIKES = "Likes";
        public static final String COMMENTS = "Comments";
        public static final String NEW_FOLLOWERS = "NewFollowers";
        public static final String FACEBOOK = "Facebook";
    }

    public static abstract class TrainingZonePageNameProperty {
        public static final String TRAINING = "Training";
        public static final String PROGRESS = "Progress";
        public static final String RECOVERY = "Recovery";
        public static final String DAILY_HEALTH = "DailyHealth";
        public static final String STATISTICS = "Statistics";
    }

    public static abstract class LeaveTrainingZoneTabModuleNameProperty {
        public static final String TRAINING_VOLUME = "TrainingVolume";
        public static final String INTENSITY_ZONE = "IntensityZone";
        public static final String IMPACT = "Impact";
    }

    public static abstract class TrainingZoneButtonClickProperty {
        public static final String LAST = "Last";
        public static final String NEXT = "Next";
        public static final String ACTIVITY = "Activity";
        public static final String DURATION = "Duration";
        public static final String TSS = "TSS";
        public static final String DISTANCE = "Distance";
        public static final String ASCENT = "Ascent";
        public static final String HEART_RATE = "HeartRate";
        public static final String PACE = "Pace";
        public static final String RUNNING_POWER = "RunningPower";
        public static final String CYCLING_POWER = "CyclingPower";
        public static final String CARDIO = "Cardio";
        public static final String MUSCULAR = "Muscular";
        public static final String TIPS = "Tips";
    }

    public static abstract class Top6DashboardWidgetsProperty {
        public static final String STEPS_TODAY = "StepsToday";
        public static final String CALORIES_TODAY = "CaloriesToday";
        public static final String SLEEP_LASTNIGHT = "SleepLastnight";
        public static final String DURATION_THISWEEK = "DurationThisweek";
        public static final String DURATION_LAST_7_DAYS = "DurationLast7days";
        public static final String DURATION_THISMONTH = "DurationThismonth";
        public static final String DURATION_LAST_30_DAYS = "DurationLast30days";
        public static final String ASCENT_THISWEEK = "AscentThisweek";
        public static final String HEARTRATE_TODAY = "HeartrateToday";
        public static final String HRV_TODAY = "HRVToday";
        public static final String ACTIVITY_THISWEEK = "ActivityThisweek";
        public static final String ACTIVITY_THISMONTH = "ActivityThismonth";
        public static final String ACTIVITY_LAST_30_DAYS = "ActivityLast30days";
        public static final String MAP_THISWEEK = "MapThisweek";
        public static final String MAP_THISMONTH = "MapThismonth";
        public static final String MAP_LAST_30_DAYS = "MapLast30days";
        public static final String PROGRESS_TODAY = "ProgressToday";
        public static final String RESOURCES_TODAY = "ResourcesToday";
        public static final String COMMUTES_THISMONTH = "CommutesThismonth";
        public static final String REST_HR_TODAY = "RestHRToday";
        public static final String MIN_DAYTIME_HR_TODAY = "MindaytimeHRToday";
        public static final String SLEEP_MIN_HR_TODAY = "SleepminHRToday";
        public static final String VO2_MAX_TODAY = "VO2MaxToday";
        public static final String TRAINING_LOAD_THISWEEK = "TrainingLoadThisweek";
        public static final String RECOVERY_TODAY = "RecoveryToday";
        public static final String PERIOD_TODAY = "PeriodToday";
        public static final String RUNNING_AVG_PACE_THISWEEK = "RunningAvgPaceThisweek";
        public static final String RUNNING_DISTANCE_THISWEEK = "RunningDistanceThisweek";
        public static final String CYCLING_SPEED_THISWEEK = "CyclingSpeedThisweek";
        public static final String CYCLING_DISTANCE_THISWEEK = "CyclingDistanceThisweek";
        public static final String HIKING_AVG_PACE_THISWEEK = "HikingAvgPaceThisweek";
        public static final String HIKING_DISTANCE_THISWEEK = "HikingDistanceThisweek";
        public static final String DIVE_DURATION_THISWEEK = "DiveDurationThisweek";
        public static final String DIVE_MAX_DEPTH_THISWEEK = "DiveMaxDepthThisweek";
        public static final String DIVE_COUNT_TOTAL_TIME = "DiveCountTotalTime";
        public static final String CALENDAR_THISWEEK = "CalendarThisweek";
        public static final String CALENDAR_THISMONTH = "CalendarThismonth";
        public static final String CALENDAR_LAST_30_DAYS = "CalendarLast30days";
    }

    public static abstract class WidgetDetailPageExposureWidgetNameProperty {
        public static final String STEPS = "Steps";
        public static final String CALORIES = "Calories";
        public static final String SLEEP = "Sleep";
        public static final String DURATION = "Duration";
        public static final String ASCENT = "Ascent";
        public static final String HEARTRATE = "HeartRate";
        public static final String RESTING_HR = "RestingHR";
        public static final String MIN_SLEEP_HR = "MinSleepHR";
        public static final String MIN_DAYTIME_HR = "MinDaytimeHR";
        public static final String RESOURCES = "Resources";
        public static final String HRV = "HRV";
        public static final String ACTIVITY = "Activity";
        public static final String CALENDAR = "Calendar";
        public static final String MAP = "Map";
        public static final String PROGRESS = "Progress";
        public static final String COMMUTES = "Commutes";
        public static final String VO2MAX = "VO2MAX";
        public static final String RECOVERY = "Recovery";
        public static final String TRAINING_LOAD = "TrainingLoad";
        public static final String RUNNING_AVG_PACE = "RunningAvgPace";
        public static final String RUNNING_DISTANCE = "RunningDistance";
        public static final String CYCLING_AVG_SPEED = "CyclingAvgSpeed";
        public static final String CYCLING_DISTANCE = "CyclingDistance";
        public static final String HIKING_AVG_PACE = "HikingAvgPace";
        public static final String HIKING_DISTANCE = "HikingDistance";
        public static final String DIVE_DURATION = "DiveDuration";
        public static final String DIVE_MAX_DEPTH = "DiveMaxDepth";
        public static final String DIVE_COUNT = "DiveCount";
        public static final String PERIOD = "Period";
    }

    public static abstract class WidgetDetailPageExposureTimeDimProperty {
        public static final String DAY = "Day";
        public static final String WEEK = "Week";
        public static final String MONTH = "Month";
        public static final String YEAR = "Year";
        public static final String LAST_7_DAYS = "Last7Days";
        public static final String LAST_30_DAYS = "Last30Days";
        public static final String LAST_6_WEEKS = "Last6Weeks";
        public static final String LAST_6_MONTHS = "Last6Months";
        public static final String LAST_8_YEARS = "Last8Years";
    }

    public static abstract class WidgetDetailPageExposureSourceProperty {
        public static final String DASHBOARD = "Dashboard";
        public static final String RECOVERY_DETAIL_PAGE = "RecoveryDetailPage";
        public static final String RECOVERY = "Recovery";
        public static final String OTHER = "Other";
    }

    public static abstract class WidgetDetailPageModuleNameProperty {
        public static final String ALL = "All";
        public static final String GOAL = "Goal";
        public static final String INTRODUCTION1 = "Introduction1";
        public static final String INTRODUCTION2 = "Introduction2";
        public static final String INTRODUCTION3 = "Introduction3";
        public static final String SLEEP_HISTORY = "SleepHistory";
        public static final String SLEEP_QUALITY = "SleepQuality";
        public static final String SLEEP_TIME = "SleepTime";
        public static final String WAKEUP_RESOURCES = "WakeupResources";
        public static final String WORKOUT_SESSIONS = "WorkoutSessions";
        public static final String CHART1 = "Chart1";
        public static final String CHART2 = "Chart2";
        public static final String CHART3 = "Chart3";
        public static final String CHART4 = "Chart4";
        public static final String CHART5 = "Chart5";
        public static final String DATA_BAR = "DataBar";
        public static final String DATA_BAR1 = "DataBar1";
        public static final String DATA_BAR2 = "DataBar2";
        public static final String RECOVERY_STATE = "RecoveryState";
        public static final String PERIOD_HISTORY = "PeriodHistory";
        public static final String COMPARISON_CHART = "ComparisonChart";
        public static final String STAGES = "Stages";
        public static final String OTHER = "Other";
    }

    public static abstract class WidgetDetailPageButtonClickProperty {
        public static final String RESTING_HR = "RestingHR";
        public static final String AVG_HR = "AVGHR";
        public static final String MIN_SLEEP_HR = "MinSleepHR";
        public static final String MIN_DAYTIME_HR = "MinDaytimeHR";
        public static final String INTRODUCTION1 = "Introduction1";
        public static final String INTRODUCTION2 = "Introduction2";
        public static final String INTRODUCTION3 = "Introduction3";
        public static final String HISTOGRAM = "Histogram";
        public static final String STACKED_CHART = "StackedChart";
        public static final String DONE = "Done";
    }


}

