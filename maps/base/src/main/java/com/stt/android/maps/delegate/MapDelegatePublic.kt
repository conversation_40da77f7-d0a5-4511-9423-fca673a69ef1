package com.stt.android.maps.delegate

import android.graphics.Bitmap
import com.google.android.gms.maps.model.LatLng
import com.stt.android.maps.SuuntoCameraPosition
import com.stt.android.maps.SuuntoCameraUpdate
import com.stt.android.maps.SuuntoCircle
import com.stt.android.maps.SuuntoCircleOptions
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.SuuntoPolyline
import com.stt.android.maps.SuuntoPolylineOptions
import com.stt.android.maps.SuuntoProjection
import com.stt.android.maps.SuuntoRulerLineOptions
import com.stt.android.maps.SuuntoScaleBarOptions
import com.stt.android.maps.SuuntoStartingPointFeature
import com.stt.android.maps.SuuntoTileOverlay
import com.stt.android.maps.SuuntoTileOverlayOptions
import com.stt.android.maps.SuuntoTopRouteFeature
import com.stt.android.maps.SuuntoUiSettings
import com.stt.android.maps.cluster.TopRouteFeatureDescriptor
import com.stt.android.maps.colortrack.ColorTrackDescriptor
import com.stt.android.maps.location.SuuntoLocationSource

/**
 * Map delegate public interface. These methods are visible from [SuuntoMap].
 */
interface MapDelegatePublic {

    fun animateCamera(
        update: SuuntoCameraUpdate,
        durationMs: Int,
        callback: SuuntoMap.CancelableCallback?
    )

    fun update3dLocation(latLng: LatLng?, altitude: Double = 0.0)

    fun addCircle(options: SuuntoCircleOptions): SuuntoCircle

    fun addMarker(options: SuuntoMarkerOptions): SuuntoMarker?

    suspend fun isMarkerClicked(longitude: Double, latitude: Double): Boolean

    fun addPolyline(options: SuuntoPolylineOptions): SuuntoPolyline

    fun addTileOverlay(options: SuuntoTileOverlayOptions): SuuntoTileOverlay?

    fun clear()

    fun getCameraPosition(): SuuntoCameraPosition?

    fun getLocationSource(): SuuntoLocationSource?

    fun getProjection(): SuuntoProjection

    fun getTerrainExaggeration(): Double

    fun getUiSettings(): SuuntoUiSettings

    fun isMap3dModeEnabled(): Boolean

    fun isMap3dModeSupported(): Boolean

    fun isMyLocationEnabled(): Boolean

    fun moveCamera(update: SuuntoCameraUpdate)

    fun setLocationSource(source: SuuntoLocationSource?)

    fun setMap3dModeEnabled(enabled: Boolean)

    fun setMapType(type: String, onStyleLoaded: (() -> Unit)? = null)

    fun setMyLocationEnabled(enabled: Boolean)

    fun setPadding(left: Int, top: Int, right: Int, bottom: Int)

    fun snapshot(callback: SuuntoMap.SnapshotReadyCallback, bitmap: Bitmap? = null)

    /**
     * Scale bar functions, use with Mapbox only, not with Google maps
     */

    fun showScaleBar(options: SuuntoScaleBarOptions)

    fun removeScaleBar()

    fun setCompassEnabled(enabled: Boolean)

    /**
     * Top routes, use with Mapbox only, not with Google map
     */
    suspend fun selectFeature(topRouteName: String, layerId: String, latLng: LatLng): SuuntoTopRouteFeature?

    /**
     * Top routes, use with Mapbox only, not with Google map
     */
    fun selectFeature(layerId: String, routeId: String)

    /**
     * Top routes, use with Mapbox only, not with Google map
     */
    fun clearSelectFeature()

    /**
     * Top routes, use with Mapbox only, not with Google map
     */
    suspend fun getVisibleTopRouteFeatures(layerId: String): List<SuuntoTopRouteFeature>

    /**
     * Popular starting points, use with Mapbox only, not with Google map
     */
    suspend fun getVisibleStartingPointFeature(layerId: String, latLng: LatLng): SuuntoStartingPointFeature?

    /**
     * Top routes, use with Mapbox only, not with Google map
     */
    suspend fun hasVisibleFeatures(
        layerId: String
    ): Boolean

    /**
     * Get map provider name. Returns MapboxMapsProvider.NAME or GoogleMapsProvider.NAME
     */
    fun getProviderName(): String

    fun <R> batchUpdate(block: () -> R): R

    suspend fun getClickLayerSourceIds(latLng: LatLng, layerIds: List<String>): Set<String>

    fun addRulerLine(options: SuuntoRulerLineOptions): SuuntoPolyline?

    fun removeRulerLine()

    fun addColorTrack(descriptor: ColorTrackDescriptor, lineWidth: Double)

    suspend fun addTopRoutesFeatures(descriptor: TopRouteFeatureDescriptor)

    fun clearTopRoutesFeatures()

    suspend fun hasTopRoutesFeatures(latLng: LatLng): Boolean?

    suspend fun getTopRoutesFeatures(latLng: LatLng): List<SuuntoTopRouteFeature>?
}
