package com.stt.android.maps.mapbox.delegate.manager

import android.os.Handler
import android.os.Looper
import androidx.annotation.CallSuper
import com.google.android.gms.maps.model.LatLng
import com.mapbox.android.gestures.Utils.dpToPx
import com.mapbox.geojson.Point
import com.mapbox.maps.MapView
import com.mapbox.maps.MapboxMap
import com.mapbox.maps.RenderedQueryGeometry
import com.mapbox.maps.RenderedQueryOptions
import com.mapbox.maps.ScreenCoordinate
import com.mapbox.maps.extension.style.expressions.dsl.generated.literal
import com.mapbox.maps.plugin.annotation.Annotation
import com.mapbox.maps.plugin.annotation.AnnotationManagerImpl
import com.mapbox.maps.plugin.annotation.OnAnnotationClickListener
import com.mapbox.maps.plugin.annotation.OnAnnotationDragListener
import com.mapbox.maps.plugin.gestures.OnMapLongClickListener
import com.mapbox.maps.plugin.gestures.gestures
import com.stt.android.maps.SuuntoLayerType
import com.stt.android.maps.SuuntoMap
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

typealias AnnotationFlavor = Int

abstract class BaseAnnotationManager<
    SuuntoAnnotation,
    Options,
    MapboxAnnotation : Annotation<*>,
    MapboxOptions : com.mapbox.maps.plugin.annotation.AnnotationOptions<*, MapboxAnnotation>,
    MapboxDragListener : OnAnnotationDragListener<MapboxAnnotation>,
    MapboxClickListener : OnAnnotationClickListener<MapboxAnnotation>,
    MapboxAnnotationManager : AnnotationManagerImpl<*, MapboxAnnotation, MapboxOptions, MapboxDragListener, MapboxClickListener, *, *, *>
    >(
    protected val name: String,
    protected val map: MapboxMap,
    protected val mapView: MapView
) {
    abstract val annotationFlavors: List<AnnotationFlavor>

    private val mapboxAnnotationManagers =
        mutableMapOf<AnnotationFlavor, MapboxAnnotationManager>()
    private val mapboxIdToSuuntoAnnotationMappings =
        mutableMapOf<AnnotationFlavor, MutableMap<String, SuuntoAnnotation>>()
    protected val suuntoToMapboxAnnotationMapping =
        mutableMapOf<SuuntoAnnotation, MapboxAnnotation>()

    internal var onAnnotationClickListener: SuuntoMap.OnAnnotationClickListener<SuuntoAnnotation>? =
        null
    internal var onAnnotationDragListener: SuuntoMap.OnAnnotationDragListener<SuuntoAnnotation>? =
        null

    private var batchProcessing = false
    private val pendingOperations = mutableMapOf<SuuntoAnnotation, PendingOperation>()

    private var activeMarker: MapboxAnnotation? = null
    private val longPressTimeout = 1000L
    private var longPressHandler: Handler = Handler(Looper.getMainLooper())
    private val uiScope = MainScope()
    private var longClickListenerAdded = false
    private var mapLongClickListener: SuuntoMap.OnMapLongClickListener? = null

    private val onMapLongClickListener = object : OnMapLongClickListener {
        override fun onMapLongClick(point: Point): Boolean {
            val screenCoordinate = map.pixelForCoordinate(point)

            uiScope.launch {
                val marker = findTargetMarkerAt(screenCoordinate)
                if (marker != null && isDraggableAnnotation(marker)) {
                    activeMarker = marker
                    longPressHandler.removeCallbacksAndMessages(null)
                    longPressHandler.postDelayed({
                        marker.isDraggable = true
                    }, longPressTimeout)
                }
            }
            mapLongClickListener?.onMapLongClick(LatLng(point.latitude(), point.longitude()))
            return false
        }
    }

    abstract fun addAnnotation(options: Options): SuuntoAnnotation

    abstract fun isDraggableAnnotation(mapboxAnnotation: MapboxAnnotation): Boolean

    @CallSuper
    open fun clear() {
        mapboxAnnotationManagers.values.forEach { it.deleteAll() }
        mapboxIdToSuuntoAnnotationMappings.values.forEach { it.clear() }
        suuntoToMapboxAnnotationMapping.clear()
    }

    fun createMapboxAnnotationManagers(belowLayerId: String) {
        // Create annotation managers starting from the topmost flavor so that if will receive
        // touch events first. Use belowLayerId to put the layers in the correct order.
        var prevLayerId = belowLayerId
        annotationFlavors.reversed().forEach { flavor ->
            val layerId = getLayerIdByFlavor(flavor)
            mapboxAnnotationManagers[flavor] = createMapboxAnnotationManager(
                flavor,
                layerId,
                prevLayerId
            ).apply {
                addDragListener(createDragListener(flavor))
                addClickListener(createClickListener(flavor))
            }
            mapboxIdToSuuntoAnnotationMappings[flavor] = mutableMapOf()
            prevLayerId = layerId
            if (!longClickListenerAdded) {
                mapView.gestures.addOnMapLongClickListener(onMapLongClickListener)
                longClickListenerAdded = true
            }
        }
    }

    private fun getMapboxAnnotationManager(flavor: AnnotationFlavor): MapboxAnnotationManager =
        requireNotNull(mapboxAnnotationManagers[flavor]) {
            "Annotation manager for flavor $flavor not created"
        }

    @CallSuper
    open fun onDestroy() {
        uiScope.cancel()
        if (longClickListenerAdded) {
            mapView.gestures.removeOnMapLongClickListener(onMapLongClickListener)
            longClickListenerAdded = false
        }
        longPressHandler.removeCallbacksAndMessages(null)
        mapboxAnnotationManagers.values.forEach { it.onDestroy() }
        mapboxIdToSuuntoAnnotationMappings.clear()
        suuntoToMapboxAnnotationMapping.clear()
    }

    protected fun getAnnotation(
        mapboxAnnotationId: String,
        flavor: AnnotationFlavor
    ): SuuntoAnnotation? {
        return mapboxIdToSuuntoAnnotationMappings[flavor]?.get(mapboxAnnotationId)
    }

    fun getMapboxAnnotation(annotation: SuuntoAnnotation): MapboxAnnotation? =
        suuntoToMapboxAnnotationMapping[annotation]

    abstract fun getAnnotationFlavor(options: Options): AnnotationFlavor

    protected fun getLayerIdByFlavor(flavor: AnnotationFlavor): String =
        getLayerIdByIndex(name, annotationFlavors.indexOf(flavor))

    protected abstract fun createMapboxAnnotationManager(
        flavor: AnnotationFlavor,
        layerId: String,
        belowLayerId: String
    ): MapboxAnnotationManager

    protected abstract fun optionsToMapbox(options: Options): MapboxOptions

    open fun attach(annotation: SuuntoAnnotation, options: Options) {
        if (batchProcessing) {
            pendingOperations[annotation] =
                PendingOperation(PendingOperationType.ATTACH, annotation, options)
            return
        }

        if (suuntoToMapboxAnnotationMapping[annotation] != null) return

        val flavor = getAnnotationFlavor(options)
        val annotationManager = getMapboxAnnotationManager(flavor)
        val mapboxAnnotation = annotationManager.create(optionsToMapbox(options))
        mapboxIdToSuuntoAnnotationMappings[flavor]?.put(mapboxAnnotation.id, annotation)
        suuntoToMapboxAnnotationMapping[annotation] = mapboxAnnotation
    }

    suspend fun findTargetMarkerAt(screenCoordinate: ScreenCoordinate): MapboxAnnotation? {
        for (flavor in annotationFlavors.reversed()) {
            if (!hasAnnotations(flavor)) continue

            val mapboxId = getMapboxAnnotationId(screenCoordinate, flavor) ?: continue

            val suuntoAnn = mapboxIdToSuuntoAnnotationMappings[flavor]?.get(mapboxId) ?: continue
            val mapboxAnn = suuntoToMapboxAnnotationMapping[suuntoAnn]
            if (mapboxAnn != null) return mapboxAnn
        }
        return null
    }

    suspend fun isMarkerClicked(point: Point): Boolean {
        if (activeMarker != null) { // triggered drag event already
            return true
        } else {
            val screen = map.pixelForCoordinate(point)
            return findTargetMarkerAt(screen) != null
        }
    }

    fun setOnMapLongClickListener(listener: SuuntoMap.OnMapLongClickListener?) {
        mapLongClickListener = listener
    }

    open fun detach(annotation: SuuntoAnnotation, options: Options) {
        if (batchProcessing) {
            pendingOperations[annotation] =
                PendingOperation(PendingOperationType.DETACH, annotation, options)
            return
        }

        val mapboxAnnotation = suuntoToMapboxAnnotationMapping[annotation]
        if (mapboxAnnotation != null) {
            val flavor = getAnnotationFlavor(options)
            val annotationManager = getMapboxAnnotationManager(flavor)
            annotationManager.delete(mapboxAnnotation)
            mapboxIdToSuuntoAnnotationMappings[flavor]?.values?.remove(annotation)
            suuntoToMapboxAnnotationMapping.remove(annotation)
        }
    }

    fun update(annotation: SuuntoAnnotation, options: Options) {
        if (batchProcessing) {
            pendingOperations[annotation] =
                PendingOperation(PendingOperationType.UPDATE, annotation, options)
            return
        }

        val flavor = getAnnotationFlavor(options)
        val annotationManager = getMapboxAnnotationManager(flavor)
        val mapboxAnnotation = suuntoToMapboxAnnotationMapping[annotation]
        if (mapboxAnnotation != null) {
            annotationManager.update(mapboxAnnotation)
        }
    }

    fun beginBatch() {
        check(!batchProcessing)
        batchProcessing = true
    }

    fun endBatch() {
        check(batchProcessing)
        batchProcessing = false

        val operationsByFlavor = pendingOperations.values.groupBy {
            getAnnotationFlavor(it.options)
        }

        operationsByFlavor.forEach { (flavor, operations) ->
            handlePendingAttachOperations(
                flavor,
                operations.filter { it.type == PendingOperationType.ATTACH }
            )

            handlePendingDetachOperations(
                flavor,
                operations.filter { it.type == PendingOperationType.DETACH }
            )

            handlePendingUpdateOperations(
                flavor,
                operations.filter { it.type == PendingOperationType.UPDATE }
            )
        }

        pendingOperations.clear()
    }

    private fun handlePendingAttachOperations(
        flavor: AnnotationFlavor,
        operations: List<PendingOperation>
    ) {
        if (operations.isEmpty()) return

        val annotationManager = getMapboxAnnotationManager(flavor)
        val mapboxAnnotations = annotationManager.create(
            operations.map { optionsToMapbox(it.options) }
        )
        mapboxAnnotations.forEachIndexed { index, mapboxAnnotation ->
            val suuntoAnnotation = operations[index].annotation
            mapboxIdToSuuntoAnnotationMappings[flavor]?.put(
                mapboxAnnotation.id,
                suuntoAnnotation
            )
            suuntoToMapboxAnnotationMapping[suuntoAnnotation] = mapboxAnnotation
        }
    }

    private fun handlePendingDetachOperations(
        flavor: AnnotationFlavor,
        operations: List<PendingOperation>
    ) {
        if (operations.isEmpty()) return

        val annotationManager = getMapboxAnnotationManager(flavor)
        val suuntoAnnotations = operations.map { it.annotation }
        val mapboxAnnotations = suuntoAnnotations.mapNotNull {
            suuntoToMapboxAnnotationMapping[it]
        }
        annotationManager.delete(mapboxAnnotations)
        mapboxIdToSuuntoAnnotationMappings[flavor]?.values?.removeAll(suuntoAnnotations)
        suuntoToMapboxAnnotationMapping.keys.removeAll(suuntoAnnotations)
    }

    private fun handlePendingUpdateOperations(
        flavor: AnnotationFlavor,
        operations: List<PendingOperation>
    ) {
        if (operations.isEmpty()) return

        val annotationManager = getMapboxAnnotationManager(flavor)
        val mapboxAnnotations = operations.mapNotNull {
            suuntoToMapboxAnnotationMapping[it.annotation]
        }
        annotationManager.update(mapboxAnnotations)
    }

    private fun hasAnnotations(flavor: AnnotationFlavor) =
        mapboxIdToSuuntoAnnotationMappings[flavor]?.isNotEmpty() ?: false

    private suspend fun getMapboxAnnotationId(
        screenCoordinate: ScreenCoordinate,
        flavor: AnnotationFlavor
    ): String? = suspendCoroutine { continuation ->
        val layerId = getLayerIdByFlavor(flavor)
        val annotationManager = getMapboxAnnotationManager(flavor)

        val r = dpToPx(24f)
        val box = com.mapbox.maps.ScreenBox(
            ScreenCoordinate(screenCoordinate.x - r, screenCoordinate.y - r),
            ScreenCoordinate(screenCoordinate.x + r, screenCoordinate.y + r)
        )

        map.queryRenderedFeatures(
            RenderedQueryGeometry(box),
            RenderedQueryOptions(listOf(layerId), literal(true))
        ) { features ->
            val mapboxAnnotationId = features.value
                ?.firstOrNull()
                ?.queriedFeature
                ?.feature
                ?.getProperty(annotationManager.getAnnotationIdKey())
                ?.asString

            continuation.resume(mapboxAnnotationId)
        }
    }

    private enum class PendingOperationType {
        ATTACH,
        DETACH,
        UPDATE,
    }

    private inner class PendingOperation(
        val type: PendingOperationType,
        val annotation: SuuntoAnnotation,
        val options: Options
    )

    protected abstract fun createDragListener(flavor: AnnotationFlavor): MapboxDragListener

    protected abstract fun createClickListener(flavor: AnnotationFlavor): MapboxClickListener

    protected open inner class DelegatingAnnotationDragListener(
        protected val flavor: AnnotationFlavor
    ) : OnAnnotationDragListener<MapboxAnnotation> {

        override fun onAnnotationDragStarted(annotation: Annotation<*>) {
            getAnnotation(annotation.id, flavor)?.let {
                onAnnotationDragListener?.onAnnotationDragStart(it)
            }
        }

        override fun onAnnotationDrag(annotation: Annotation<*>) {
            getAnnotation(annotation.id, flavor)?.let {
                onAnnotationDragListener?.onAnnotationDrag(it)
            }
        }

        override fun onAnnotationDragFinished(annotation: Annotation<*>) {
            getAnnotation(annotation.id, flavor)?.let {
                onAnnotationDragListener?.onAnnotationDragEnd(it)
            }
            activeMarker = null
        }
    }

    protected open inner class DelegatingAnnotationClickListener(
        protected val flavor: AnnotationFlavor
    ) : OnAnnotationClickListener<MapboxAnnotation> {

        override fun onAnnotationClick(annotation: MapboxAnnotation): Boolean {
            return getAnnotation(annotation.id, flavor)?.let {
                onAnnotationClickListener?.onAnnotationClick(it)
            } ?: false
        }
    }

    companion object {
        fun getLayerIdByIndex(name: String, index: Int) =
            SuuntoLayerType.ANNOTATION.getLayerId(name, index.toString())
    }
}
