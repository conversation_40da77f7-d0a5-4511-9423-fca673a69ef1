package com.stt.android.maps.google.delegate

import android.Manifest.permission.ACCESS_COARSE_LOCATION
import android.Manifest.permission.ACCESS_FINE_LOCATION
import android.annotation.SuppressLint
import android.graphics.Bitmap
import androidx.annotation.RequiresPermission
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MapStyleOptions
import com.google.android.gms.maps.model.Marker
import com.google.android.gms.maps.model.Polyline
import com.stt.android.maps.SuuntoCameraPosition
import com.stt.android.maps.SuuntoCameraUpdate
import com.stt.android.maps.SuuntoCircle
import com.stt.android.maps.SuuntoCircleOptions
import com.stt.android.maps.SuuntoFreeCameraUpdate
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.SuuntoPolyline
import com.stt.android.maps.SuuntoPolylineOptions
import com.stt.android.maps.SuuntoProjection
import com.stt.android.maps.SuuntoRulerLineOptions
import com.stt.android.maps.SuuntoScaleBarOptions
import com.stt.android.maps.SuuntoStartingPointFeature
import com.stt.android.maps.SuuntoTileOverlay
import com.stt.android.maps.SuuntoTileOverlayOptions
import com.stt.android.maps.SuuntoTopRouteFeature
import com.stt.android.maps.SuuntoUiSettings
import com.stt.android.maps.cluster.TopRouteFeatureDescriptor
import com.stt.android.maps.colortrack.ColorTrackDescriptor
import com.stt.android.maps.delegate.MapDelegate
import com.stt.android.maps.google.GoogleMapsProvider
import com.stt.android.maps.google.SuuntoUrlTileProvider
import com.stt.android.maps.google.toGoogle
import com.stt.android.maps.google.toGoogleMapType
import com.stt.android.maps.google.toSuunto
import com.stt.android.maps.google.toSuuntoMapType
import com.stt.android.maps.location.SuuntoLocationSource

// suppressing lint to add the marker listeners below
@SuppressLint("PotentialBehaviorOverride")
class GoogleMapDelegate(
    private val map: GoogleMap,
    private val mapView: GoogleMapViewDelegate,
    private val provider: GoogleMapsProvider
) : MapDelegate {

    private var locationSource: SuuntoLocationSource? = null
    private var solidPolyline: Polyline? = null
    init {
        // Set map style if the initial map type is custom
        provider.options.customMapTypes[map.mapType.toSuuntoMapType()]?.let {
            map.setMapStyle(MapStyleOptions.loadRawResourceStyle(mapView.context, it.styleResourceId))
        }
    }

    override fun update3dLocation(latLng: LatLng?, altitude: Double) {
        // Not supported
    }

    override fun animateCamera(
        update: SuuntoCameraUpdate,
        durationMs: Int,
        callback: SuuntoMap.CancelableCallback?
    ) {
        if (update is SuuntoFreeCameraUpdate) return // Not supported
        map.animateCamera(update.toGoogle(this), durationMs, callback?.toGoogle())
    }

    override fun addCircle(options: SuuntoCircleOptions): SuuntoCircle {
        return SuuntoCircle(GoogleCircleDelegate(map.addCircle(options.toGoogle())))
    }

    override fun addMarker(options: SuuntoMarkerOptions): SuuntoMarker? {
        val googleMarker = map.addMarker(options.toGoogle()) ?: return null
        val markerDelegate = GoogleMarkerDelegate(googleMarker)

        return SuuntoMarker(
            markerDelegate,
            options
        ).also {
            googleMarker.tag = it
        }
    }

    override suspend fun isMarkerClicked(longitude: Double, latitude: Double): Boolean {
        return false
    }

    override fun addPolyline(options: SuuntoPolylineOptions): SuuntoPolyline {
        // TODO Adds supports for borderColor and borderWidth
        return SuuntoPolyline(GooglePolylineDelegate(map.addPolyline(options.toGoogle())))
    }

    override fun addTileOverlay(options: SuuntoTileOverlayOptions): SuuntoTileOverlay? {
        val tileOverlayOptions = options.toGoogle().apply {
            val tileSource = options.tileSource
            if (tileProvider == null && tileSource != null) {
                tileProvider(SuuntoUrlTileProvider(tileSource))
            }
        }
        val tileOverlay = map.addTileOverlay(tileOverlayOptions) ?: return null

        // Copy the options so that the overlay delegate may safely update them without affecting
        // the original
        val optionsCopy = options.copy()
        return SuuntoTileOverlay(GoogleTileOverlayDelegate(optionsCopy, tileOverlay))
    }

    override fun clear() = map.clear()

    override fun getCameraPosition(): SuuntoCameraPosition = map.cameraPosition.toSuunto()

    override fun getLocationSource() = locationSource

    override fun getProjection(): SuuntoProjection {
        return SuuntoProjection(GoogleProjectionDelegate(map.projection))
    }

    override fun getTerrainExaggeration(): Double = 1.0

    override fun getUiSettings(): SuuntoUiSettings {
        return SuuntoUiSettings(GoogleUiSettingsDelegate(map.uiSettings))
    }

    override fun isMap3dModeEnabled(): Boolean = false

    override fun isMap3dModeSupported(): Boolean = false

    override fun isMyLocationEnabled(): Boolean = map.isMyLocationEnabled

    override fun moveCamera(update: SuuntoCameraUpdate) {
        if (update is SuuntoFreeCameraUpdate) return // Not supported
        map.moveCamera(update.toGoogle(this))
    }

    override fun setLocationSource(source: SuuntoLocationSource?) {
        if (source != locationSource) {
            locationSource = source
            map.setLocationSource(source?.toGoogle())
        }
    }

    override fun setMap3dModeEnabled(enabled: Boolean) {
        // Not supported
    }

    override fun setMapType(type: String, onStyleLoaded: (() -> Unit)?) {
        provider.options.customMapTypes[type]?.let {
            map.mapType = it.googleMapType
            map.setMapStyle(
                MapStyleOptions.loadRawResourceStyle(
                    mapView.context,
                    it.styleResourceId
                )
            )
        } ?: run {
            map.mapType = type.toGoogleMapType()
            map.setMapStyle(null)
        }
    }

    @RequiresPermission(anyOf = [ ACCESS_FINE_LOCATION, ACCESS_COARSE_LOCATION ])
    override fun setMyLocationEnabled(enabled: Boolean) {
        map.isMyLocationEnabled = enabled
    }

    override fun setOnCameraMoveListener(listener: GoogleMap.OnCameraMoveListener?) {
        map.setOnCameraMoveListener(listener)
    }

    override fun setOnCameraIdleListener(listener: GoogleMap.OnCameraIdleListener?) {
        map.setOnCameraIdleListener(listener)
    }

    override fun setOnCameraMoveStartedListener(listener: GoogleMap.OnCameraMoveStartedListener?) {
        map.setOnCameraMoveStartedListener(listener)
    }

    override fun setOnMap3dModeChangedWithTiltListener(listener: SuuntoMap.OnMap3dModeChangedListener?) {
        // Not supported
    }

    override fun setOnMapClickListener(listener: SuuntoMap.OnMapClickListener?) {
        map.setOnMapClickListener {
            listener?.onMapClick(it)
        }
    }

    override fun setOnMapLongClickListener(listener: SuuntoMap.OnMapLongClickListener?) {
        map.setOnMapLongClickListener {
            listener?.onMapLongClick(it)
        }
    }

    override fun setOnMapMoveListener(listener: SuuntoMap.OnMapMoveListener?) {}

    override fun setOnMarkerClickListener(listener: SuuntoMap.OnMarkerClickListener?) {
        if (listener == null) {
            map.setOnMarkerClickListener(null)
        } else {
            map.setOnMarkerClickListener { googleMarker ->
                getSuuntoMarker(googleMarker)
                    ?.let { listener.onAnnotationClick(it) }
                    ?: false
            }
        }
    }

    override fun setOnMarkerDragListener(listener: SuuntoMap.OnMarkerDragListener?) {
        if (listener == null) {
            map.setOnMarkerDragListener(null)
        } else {
            map.setOnMarkerDragListener(object : GoogleMap.OnMarkerDragListener {
                override fun onMarkerDrag(marker: Marker) {
                    getSuuntoMarker(marker)?.let { listener.onAnnotationDrag(it) }
                }

                override fun onMarkerDragEnd(marker: Marker) {
                    getSuuntoMarker(marker)?.let { listener.onAnnotationDragEnd(it) }
                }

                override fun onMarkerDragStart(marker: Marker) {
                    getSuuntoMarker(marker)?.let { listener.onAnnotationDragStart(it) }
                }
            })
        }
    }

    override fun setPadding(left: Int, top: Int, right: Int, bottom: Int) {
        map.setPadding(left, top, right, bottom)
    }

    override fun snapshot(callback: SuuntoMap.SnapshotReadyCallback, bitmap: Bitmap?) {
        map.snapshot(callback.toGoogle(), bitmap)
    }

    override fun showScaleBar(options: SuuntoScaleBarOptions) {}

    override fun removeScaleBar() {}

    override fun setOnScaleListener(listener: SuuntoMap.OnScaleListener?) {}

    override suspend fun selectFeature(topRouteName: String, layerId: String, latLng: LatLng): SuuntoTopRouteFeature? {
        // Not supported
        return null
    }

    override fun selectFeature(layerId: String, routeId: String) {
        // Not supported
    }

    override fun clearSelectFeature() {
        // Not supported
    }

    override suspend fun getVisibleTopRouteFeatures(layerId: String): List<SuuntoTopRouteFeature> {
        // Not supported
        return emptyList()
    }

    override suspend fun getVisibleStartingPointFeature(
        layerId: String,
        latLng: LatLng
    ): SuuntoStartingPointFeature? {
        // Not supported
        return null
    }

    override suspend fun hasVisibleFeatures(
        layerId: String
    ): Boolean {
        // Not supported
        return false
    }

    override fun setCompassEnabled(enabled: Boolean) {
        map.uiSettings.isCompassEnabled = enabled
    }

    override fun getProviderName(): String = GoogleMapsProvider.NAME

    override fun <R> batchUpdate(block: () -> R): R {
        // Batch operations not supported
        return block()
    }

    override suspend fun getClickLayerSourceIds(
        latLng: LatLng,
        layerIds: List<String>
    ): Set<String> {
        // not supported
        return emptySet()
    }

    override fun addRulerLine(options: SuuntoRulerLineOptions): SuuntoPolyline {
        solidPolyline = map.addPolyline(
            SuuntoPolylineOptions(options.points)
                .color(options.solidLineColor)
                .width(options.solidLineWidth)
                .zIndex(options.solidLineZIndex).toGoogle()
        )
        return SuuntoPolyline(GooglePolylineDelegate(map.addPolyline(options.toGoogle())))
    }

    override fun removeRulerLine() {
        solidPolyline?.remove()
        solidPolyline = null
    }

    override fun addColorTrack(descriptor: ColorTrackDescriptor, lineWidth: Double) {
        // not supported
    }

    override suspend fun addTopRoutesFeatures(descriptor: TopRouteFeatureDescriptor) {
        // do nothing here
    }

    override fun clearTopRoutesFeatures() {
        // do nothing here
    }

    override suspend fun hasTopRoutesFeatures(latLng: LatLng): Boolean? {
        // Not supported
        return null
    }

    override suspend fun getTopRoutesFeatures(latLng: LatLng): List<SuuntoTopRouteFeature>? {
        // Not supported
        return null
    }

    private fun getSuuntoMarker(googleMarker: Marker): SuuntoMarker? =
        (googleMarker.tag as? SuuntoMarker)
}
