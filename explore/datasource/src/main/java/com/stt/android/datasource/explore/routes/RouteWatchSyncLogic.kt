package com.stt.android.home.explore.routes

import com.stt.android.data.source.local.routes.RouteDao
import javax.inject.Inject

class RouteWatchSyncLogic @Inject constructor(
    private val routeDao: RouteDao
) : RouteWatchSyncTrigger {
    override suspend fun prepareAfterInitialConnect() {
        val updated = routeDao.fetchAllWatchEnabled()
            .map {
                it.copy(watchSyncState = "PENDING", watchSyncResponseCode = 0)
            }
        routeDao.upsert(updated)
    }
}
