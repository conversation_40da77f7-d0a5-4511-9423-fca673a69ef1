package com.stt.android.home.explore.routes.planner;

import android.Manifest;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.os.Parcelable;
import android.os.SystemClock;
import android.text.Html;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.method.LinkMovementMethod;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.text.style.TextAppearanceSpan;
import android.util.SparseIntArray;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import static android.view.View.GONE;
import static android.view.View.INVISIBLE;
import static android.view.View.VISIBLE;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.ViewTreeObserver;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.DimenRes;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.collection.SparseArrayCompat;
import androidx.compose.ui.platform.ComposeView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Group;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.view.OneShotPreDrawListener;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.LifecycleOwnerKt;
import androidx.lifecycle.Observer;
import androidx.lifecycle.Transformations;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.snackbar.Snackbar;
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance;
import com.stt.android.AppConfig;
import com.stt.android.FontRefs;
import com.stt.android.analytics.AmplitudeAnalyticsTracker;
import com.stt.android.analytics.AnalyticsEvent;
import static com.stt.android.analytics.AnalyticsEvent.ROUTE_PLANNING_STARTED;
import com.stt.android.analytics.AnalyticsEventProperty;
import static com.stt.android.analytics.AnalyticsEventProperty.LOCATION_PIN;
import static com.stt.android.analytics.AnalyticsEventProperty.ROUTE_PLANNING_STARTED_SOURCE;
import static com.stt.android.analytics.AnalyticsEventProperty.TURN_WAYPOINTS;
import com.stt.android.analytics.AnalyticsProperties;
import static com.stt.android.analytics.AnalyticsPropertyValue.Map3dModeInputMethod.TILTING_WITH_FINGERS;
import static com.stt.android.analytics.AnalyticsPropertyValue.RoutePlanningStartedLocationPinProperty.NO_LOCATION_PIN;
import com.stt.android.analytics.EmarsysAnalytics;
import com.stt.android.analytics.FirebaseAnalyticsTracker;
import com.stt.android.common.ui.SimpleDialogFragment;
import com.stt.android.common.ui.ViewModelActivity2;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.di.FeatureTogglePreferences;
import com.stt.android.domain.Point;
import com.stt.android.domain.diarycalendar.LocationWithActivityType;
import com.stt.android.domain.explore.pois.POI;
import static com.stt.android.domain.routes.BaseImportRouteUseCaseKt.MAX_SNAP_TO_ROUTE_WAYPOINT_DISTANCE_IN_METERS;
import com.stt.android.domain.routes.RouteSegment;
import com.stt.android.domain.user.HeatmapType;
import com.stt.android.domain.user.MeasurementUnitKt;
import com.stt.android.domain.user.RoadSurfaceType;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workouts.ActivityGroupMapper;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.extensions.ContextExtensionsKt;
import com.stt.android.home.diary.diarycalendar.RouteAndActivityType;
import com.stt.android.home.explore.ExploreAnalytics;
import com.stt.android.home.explore.ExploreUtils;
import com.stt.android.home.explore.R;
import com.stt.android.home.explore.library.LibraryActivity;
import com.stt.android.home.explore.pois.POIMarkerManager;
import com.stt.android.home.explore.routes.MapPresenter;
import com.stt.android.home.explore.routes.MapView;
import com.stt.android.home.explore.routes.RoutePlannerNavigator;
import com.stt.android.home.explore.routes.RouteUtils;
import static com.stt.android.home.explore.routes.planner.RoutingApiModel.MAX_WAYPOINT_COUNT;
import com.stt.android.home.explore.routes.planner.actionresult.AddWaypointAction;
import com.stt.android.home.explore.routes.planner.actionresult.AddWaypointsActionResult;
import com.stt.android.home.explore.routes.planner.actionresult.EditWaypointAction;
import com.stt.android.home.explore.routes.planner.actionresult.EditWaypointActionResult;
import com.stt.android.home.explore.routes.planner.addtoroute.AddToRouteFragment;
import com.stt.android.home.explore.routes.planner.addtoroute.AddToRouteType;
import com.stt.android.home.explore.routes.planner.waypoints.NearestPoints;
import com.stt.android.home.explore.routes.planner.waypoints.PointsWithDistances;
import com.stt.android.home.explore.routes.planner.waypoints.WaypointAnalyticsTracker;
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointDetails;
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointDetailsFragment;
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointDetailsMode;
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointType;
import com.stt.android.home.explore.routes.planner.waypoints.details.type.WaypointDetailsTypeFragment;
import com.stt.android.home.explore.routes.ui.ClimbGuidanceAwareRouteAltitudeChartWithAxis;
import com.stt.android.home.explore.routes.ui.InputRouteNameJavaInterop;
import com.stt.android.home.mytracks.MyTracksUtils;
import com.stt.android.intentresolver.IntentKey;
import com.stt.android.intentresolver.IntentResolver;
import com.stt.android.intentresolver.TopRouteAction;
import com.stt.android.intentresolver.LibraryTab;
import com.stt.android.maps.MapFloatingActionButtonsJavaInterop;
import com.stt.android.maps.MapFloatingActionButtonsState;
import com.stt.android.maps.MapType;
import com.stt.android.maps.MapTypeExtKt;
import com.stt.android.maps.MarkerZPriority;
import com.stt.android.maps.OnMapReadyCallback;
import com.stt.android.maps.SuuntoBitmapDescriptorFactory;
import com.stt.android.maps.SuuntoCameraOptions;
import com.stt.android.maps.SuuntoCameraPosition;
import com.stt.android.maps.SuuntoCameraUpdate;
import com.stt.android.maps.SuuntoCameraUpdateFactory;
import com.stt.android.maps.SuuntoMap;
import com.stt.android.maps.SuuntoMapOptions;
import com.stt.android.maps.SuuntoMapView;
import com.stt.android.maps.SuuntoMaps;
import com.stt.android.maps.SuuntoMarker;
import com.stt.android.maps.SuuntoMarkerOptions;
import com.stt.android.maps.SuuntoPolyline;
import com.stt.android.maps.SuuntoPolylineOptions;
import com.stt.android.maps.SuuntoSupportMapFragment;
import com.stt.android.maps.SuuntoTileOverlay;
import com.stt.android.maps.SuuntoTileOverlayGroup;
import com.stt.android.maps.SuuntoTileOverlayOptions;
import com.stt.android.maps.extensions.SuuntoMapExtensionsKt;
import com.stt.android.models.MapSelectionModel;
import com.stt.android.routes.PointExtKt;
import com.stt.android.routes.widget.ActivityTypeIconsAdapter;
import com.stt.android.session.SignInFlowHook;
import com.stt.android.ui.components.ActivityTypeSelectionEditor;
import com.stt.android.ui.components.LockableNestedScrollView;
import com.stt.android.ui.map.Map3dEnabledLiveData;
import com.stt.android.ui.map.MapHelper;
import com.stt.android.ui.map.ShowPOIsLiveData;
import com.stt.android.ui.map.SuuntoScaleBarDefaultOptionsFactory;
import com.stt.android.ui.map.TurnByTurnEnabledLiveData;
import com.stt.android.ui.map.mapoptions.MapOption;
import com.stt.android.ui.map.selection.MapSelectionDialogFragment;
import com.stt.android.ui.utils.DialogHelper;
import com.stt.android.ui.utils.SpeedDialogFragment;
import com.stt.android.ui.utils.TextFormatter;
import com.stt.android.ui.utils.ThrottlingOnClickListener;
import com.stt.android.utils.CoordinateUtils;
import com.stt.android.utils.CustomFontStyleSpan;
import com.stt.android.utils.FlavorUtils;
import com.stt.android.utils.PermissionUtils;
import com.stt.android.utils.STTConstants;
import io.reactivex.disposables.CompositeDisposable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.inject.Inject;
import kotlin.Unit;
import kotlin.collections.CollectionsKt;
import pub.devrel.easypermissions.EasyPermissions;
import timber.log.Timber;

public abstract class BaseRoutePlannerActivity extends ViewModelActivity2
    implements RoutePlannerView, OnMapReadyCallback, SuuntoMap.OnMapClickListener, MapView,
    SpeedDialogFragment.SpeedDialogListener, SuuntoMap.OnMarkerDragListener,
    RoutingModeDialogFragment.RoutingModeListener,
    EasyPermissions.PermissionCallbacks, SimpleDialogFragment.Callback,
    SuuntoMap.OnMapLongClickListener, WaypointDetailsFragment.Listener,
    WaypointDetailsTypeFragment.Listener,
    GoogleMap.OnCameraIdleListener, SuuntoMap.OnMapMoveListener,
    AddToRouteFragment.Listener, PlannerOtherOptionsDialogFragment.PlannerOtherOptionsListener {

    private long lastAnnotationDragEndMillis = 0;
    private static final long INTERVAL_BETWEEN_CLICK_AND_DRAG = 500;

    @Inject
    @FeatureTogglePreferences
    SharedPreferences featureTogglePreferences;

    @NonNull
    @Override
    public RoutePlannerViewModel getViewModel() {
        return new ViewModelProvider(this).get(RoutePlannerViewModel.class);
    }

    public static class Navigator implements RoutePlannerNavigator {

        final SignInFlowHook signInFlowHook;
        @Inject
        public Navigator(
            SignInFlowHook signInFlowHook
        ) {
            this.signInFlowHook = signInFlowHook;
        }

        @Override
        public void startCreateRouteActivityOrRedirectToLogin(
            @NonNull Context context,
            @NonNull CurrentUserController currentUserController) {
            BaseRoutePlannerActivity.startCreateRouteActivityOrRedirectToLogin(
                currentUserController,
                signInFlowHook,
                context);
        }

        @Override
        public void startActivityForCreatingRouteFromWorkoutOrRedirectToLogin(
            @NonNull CurrentUserController currentUserController,
            @NonNull Context context,
            @NonNull WorkoutHeader workoutHeader) {
            BaseRoutePlannerActivity.startActivityForCreatingRouteFromWorkoutOrRedirectToLogin(
                currentUserController,
                signInFlowHook,
                context,
                workoutHeader
            );
        }
    }

    private static final int START_POINT_SEGMENT = -1;
    private static final String ROUTE_IMPORT_ERROR_DIALOG = "routeImportErrorDialog";
    private static final String LOCATION_PERMISSION_DIALOG_TAG = "LocationPermissionDialog";
    private static final String ERROR_ADDING_INITIAL_SEGMENT_DIALOG_TAG = "InitialSegmentFailedDialog";
    private static final String MAP_FRAGMENT_TAG = "RoutePlannerMapFragment";
    private static final String ROUTE_IN_PROGRESS_ID = "RouteInProgressId";
    private static final String START_POINT = "RouteInProgressStartPoint";
    private static final String CAMERA_POSITION = "RouteInProgressCameraPosition";
    private static final String TRACK_USER_LOCATION = "RouteInProgressTrackUserLocation";
    private static final String TURN_BY_TURN_MAX_COUNT_DIALOG_TAG = "TurnByTurnMaxCountDialog";
    private static final String WAYPOINT_MAX_COUNT_DIALOG_TAG = "WaypointMaxCountDialog";
    private static final String WAYPOINT_LIMITED_SUPPORT_DIALOG_TAG = "WaypointLimitedSupportDialog";

    public static final String ROUTE_BUTTON_IMPORT =
        AppConfig.APPLICATION_ID + ".route_button_import";
    public static final String ROUTE_BUTTON_IMPORT_URI = "ROUTE_BUTTON_IMPORT_URI";
    private static final String CURSOR_INDEX = "CURSOR_INDEX";
    protected static final String WITHOUT_GPS_PROMPT_DIALOG = "without_gps_prompt_dialog";
    protected static final String KEY_SHOW_ADD_TO_WATCH = "com.stt.android.home.explore.routes.planner.KEY_SHOW_ADD_TO_WATCH";
    private static final String KEY_FROM_POPULAR_ROUTE_DETAIL = "com.stt.android.home.explore.routes.planner.KEY_FROM_POPULAR_ROUTE_DETAIL";

    /**
     * Store the current polylines in the map per each segment.
     * This will be used to arbitrarily modify/remove them.
     */
    final SparseArrayCompat<SuuntoPolyline> polylines = new SparseArrayCompat<>();

    /**
     * Store the split segment when highlight route
     */
    final List<SuuntoPolyline> highlightPolylines = new ArrayList<>();

    /**
     * Store the markers in the map per each segment.
     * This will be used to arbitrarily modify/remove them.
     */
    final SparseArrayCompat<SuuntoMarker> markers = new SparseArrayCompat<>();

    /**
     * Contains the pending waypoint marker when visible
     */
    SuuntoMarker pendingWaypointMarker = null;

    /**
     * Given a marker hashcode find the segment hashcode that has it as end point.
     * Used to figure out what segment to modify when moving markers around.
     */
    final SparseIntArray segmentsByEndMarker = new SparseIntArray();

    /**
     * We need to store marker original positions in separate array in order to have fallback
     * position if there is error in moving marker. Position from onMarkerDragStart won't give
     * correct position since icon is popped and moved outside of finger
     */
    final SparseArrayCompat<LatLng> markerOriginalPositions = new SparseArrayCompat<>();

    /**
     * Stores segment hash codes in an order they are added.
     * This is used for changing markers when segment is added or removed.
     */
    private final List<Integer> segmentHashCodes = new ArrayList<>();

    private POIMarkerManager poiMarkerManager;

    private int movedMarkerSegmentHash;

    private boolean wasAnnotationDragged = false;

    @Inject
    EmarsysAnalytics emarsysAnalytics;

    @Inject
    WaypointAnalyticsTracker waypointAnalytics;

    @Inject
    AmplitudeAnalyticsTracker amplitudeAnalyticsTracker;

    @Inject
    protected MapPresenter mapPresenter;

    @Inject
    SignInFlowHook signInFlowHook;

    @Inject
    SuuntoScaleBarDefaultOptionsFactory scaleBarOptionsFactory;

    @Inject
    ShowPOIsLiveData showPOIsLiveData;

    @Inject
    TurnByTurnEnabledLiveData turnByTurnEnabledLiveData;

    @Inject
    MapSelectionModel mapSelectionModel;

    @Inject
    Map3dEnabledLiveData map3dEnabledLiveData;

    @Inject
    MyTracksUtils myTracksUtils;

    @Inject
    SuuntoBitmapDescriptorFactory bitmapDescriptorFactory;

    @Inject
    ExploreAnalytics exploreAnalytics;

    @Inject
    FirebaseAnalyticsTracker firebaseAnalyticsTracker;

    @Inject
    ActivityGroupMapper activityGroupMapper;

    protected SuuntoSupportMapFragment mapFragment;

    private final CompositeDisposable disposable = new CompositeDisposable();

    protected ComposeView routeInputName;
    protected Button cancel;
    ViewGroup rootView;
    ConstraintLayout bottomSheetView;
    LockableNestedScrollView bottomSheetScrollView;
    Group routeInfoGroup;
    TextView routeDistance;
    TextView routeDuration;
    TextView speedLabel;
    TextView routeAscentValue;
    View ascentContainer;
    TextView routeDescentValue;
    View descentContainer;
    TextView mapCredit;
    ProgressBar loadingSpinner;
    ProgressBar savingProgress;
    ConstraintLayout buttonContainerStart;
    ConstraintLayout buttonContainerEnd;
    FloatingActionButton routingOptionsBt;
    FloatingActionButton otherOptionsBt;
    FloatingActionButton addWaypointBt;
    ActivityTypeSelectionEditor activityTypeEditor;
    FloatingActionButton undoBt;
    View emptyMapClickCatcherView;
    View mapContainer;
    ProgressBar loadingRouteProgress;
    Button saveButton;
    ComposeView routeSections;
    ComposeView waypointList;
    ComposeView mapOptionsLayout;
    ClimbGuidanceAwareRouteAltitudeChartWithAxis routeAltitudeChartWithAxis;
    TextView tapTheMapTip;
    TextView editActivityTypesBt; // editActivityTypes
    SwitchCompat turnByTurnSwitch;
    TextView suitableFor;
    View bottomSheetFloatingLayer;
    View bottomSection;
    ConstraintLayout topSummary;
    ImageView dragHandle;
    Toolbar topBarView;
    View addWaypointTooltipsView;

    // Route tip bottom sheet manager
    RouteTipBottomSheetManager routeTipBottomSheetManager;

    @Nullable
    SuuntoTileOverlay heatmapOverlay;

    @Nullable
    SuuntoTileOverlayGroup roadSurfaceOverlay;

    @Nullable
    SuuntoTileOverlay startingPointsOverlay;

    BottomSheetBehavior<ConstraintLayout> bottomSheetBehavior;

    @Nullable
    private SuuntoMap map;

    private static final int MIN_SHOW_TIME = 1000; // ms
    private static final int MIN_DELAY = 300; // ms

    long progressStartTime = -1;
    boolean progressPostedHide = false;
    boolean progressPostedShow = false;
    boolean progressDismissed = false;

    private int compassTopPadding;

    private boolean showTurnByTurnMaxCountDialog = true;
    private boolean skipWaypointNamesInfo = false;
    private boolean skipShowRoutingButtons = false;

    protected int cursorIndex = 0;
    protected boolean needRestore = false;
    protected boolean needTips = true;
    protected boolean isNavigate = false;
    private boolean addWaypointMode = false;
    private boolean ignoreHalfExpanded = false;
    protected boolean navigateOngoing = false;
    protected boolean startDisabled = false;
    private Menu currentMenu;

    private boolean isEditState = false;

    private ObjectAnimator animator = null;
    BottomSheetBehavior.BottomSheetCallback bottomSheetCallback =
        new BottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                setButtonVisibility(newState != BottomSheetBehavior.STATE_EXPANDED);
                if (!isNavigate || isSaveState()) {
                    switch (newState) {
                        case BottomSheetBehavior.STATE_EXPANDED,
                             BottomSheetBehavior.STATE_HALF_EXPANDED:
                            invalidateOptionsMenu();
                            break;
                        case BottomSheetBehavior.STATE_COLLAPSED:
                            animateElevation(bottomSheetFloatingLayer.getElevation(), getResources().getDimension(
                                com.stt.android.R.dimen.bottom_sheet_elevation));

                            invalidateOptionsMenu();
                            break;
                        case BottomSheetBehavior.STATE_DRAGGING:
                            animateElevation(bottomSheetFloatingLayer.getElevation(), getResources().getDimension(
                                com.stt.android.R.dimen.bottom_sheet_floating_layer_elevation));

                            break;
                        case BottomSheetBehavior.STATE_HIDDEN:
                        case BottomSheetBehavior.STATE_SETTLING:
                            break;
                    }
                }
            }

            private void setButtonVisibility(boolean visible) {
                if (!visible) {
                    routingOptionsBt.hide();
                    otherOptionsBt.hide();
                    addWaypointBt.hide();
                    undoBt.hide();
                    hideMapOptionsLayout();
                    setCompassEnabled(false);
                } else {
                    routingOptionsBt.show();
                    otherOptionsBt.show();
                    addWaypointBt.show();
                    undoBt.show();
                    showMapOptionsLayout();
                    setCompassEnabled(true);
                }
            }

            @SuppressLint("RestrictedApi")
            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {
                if (bottomSheetBehavior.getState() == BottomSheetBehavior.STATE_SETTLING) {
                    float thresholds = 0.05f;
                    float halfExpandedRatio = bottomSheetBehavior.getHalfExpandedRatio();
                    switch (bottomSheetBehavior.getLastStableState()) {
                        case BottomSheetBehavior.STATE_EXPANDED:
                            if (halfExpandedRatio < slideOffset
                                && slideOffset < halfExpandedRatio + thresholds) {
                                bottomSheetBehavior.setState(
                                    BottomSheetBehavior.STATE_HALF_EXPANDED);
                            }
                            break;
                        case BottomSheetBehavior.STATE_COLLAPSED:
                            if (ignoreHalfExpanded) {
                                if (slideOffset > halfExpandedRatio) {
                                    ignoreHalfExpanded = false;
                                }
                            } else {
                                if (halfExpandedRatio - thresholds < slideOffset
                                    && slideOffset < halfExpandedRatio) {
                                    bottomSheetBehavior.setState(
                                        BottomSheetBehavior.STATE_HALF_EXPANDED);
                                }
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        };

    private final Runnable progressDelayedHide = () -> {
        progressPostedHide = false;
        progressStartTime = -1;
        setRouteInfoVisibilityIfNeeded(VISIBLE);
    };

    private final Runnable progressDelayedShow = () -> {
        progressPostedShow = false;
        if (!progressDismissed) {
            progressStartTime = SystemClock.elapsedRealtime();
            setRouteInfoVisibilityIfNeeded(GONE);
        }
    };

    private Typeface valueFont;
    private Typeface unitFont;

    private final TextFormatter.SpanFactory valueSpanFactory = () -> {
        TextAppearanceSpan textAppearanceSpan = new TextAppearanceSpan(BaseRoutePlannerActivity.this, com.stt.android.R.style.WorkoutSummaryValue);
        int textSizeInPixels = getResources().getDimensionPixelSize(com.stt.android.R.dimen.text_size_large);
        AbsoluteSizeSpan absoluteSizeSpan = new AbsoluteSizeSpan(textSizeInPixels);
        if (valueFont != null) {
            return Arrays.asList(textAppearanceSpan, absoluteSizeSpan, new CustomFontStyleSpan(valueFont));
        } else {
            return Arrays.asList(textAppearanceSpan, absoluteSizeSpan);
        }
    };

    private final TextFormatter.SpanFactory unitSpanFactory = () -> {
        TextAppearanceSpan textAppearanceSpan = new TextAppearanceSpan(BaseRoutePlannerActivity.this, com.stt.android.R.style.WorkoutSummaryValue);
        int textSizeInPixels = getResources().getDimensionPixelSize(com.stt.android.R.dimen.text_size_small);
        AbsoluteSizeSpan absoluteSizeSpan = new AbsoluteSizeSpan(textSizeInPixels);
        if (unitFont != null) {
            return Arrays.asList(textAppearanceSpan, absoluteSizeSpan, new CustomFontStyleSpan(unitFont));
        } else {
            return Arrays.asList(textAppearanceSpan, absoluteSizeSpan);
        }
    };

    private final SuuntoMap.OnMap3dModeChangedListener onMap3dModeChangedWithTiltListener = enabled -> {
        mapSelectionModel.setMap3dEnabled(enabled);
        exploreAnalytics.trackMap3dModeChange(ROUTE_PLANNING_STARTED, TILTING_WITH_FINGERS);
    };

    private final SuuntoMap.OnMarkerClickListener onMarkerClickListener = marker -> {
        if (marker.isWaypoint() &&
            // Do not show waypoint details for t-b-t if it is not enabled
            (!marker.isTurnByTurnWaypoint() || mapSelectionModel.getTurnByTurnEnabled())) {
            getViewModel().findWaypointsOnRoute(marker.getPosition());
        } else {
            if (isMapClickable() && marker.getPosition() != null) {
                onMapClick(marker.getPosition(), null);
            }
        }
        return true;
    };

    private final SuuntoMap.OnScaleListener onScaleListener = new SuuntoMap.OnScaleListener() {
        @Override
        public void onScaleBegin() {
            showScaleBar(map);
        }

        @Override
        public void onScaleEnd() {
            map.removeScaleBar();
        }
    };

    /**
     * Hide the progress view if it is visible and show route info. The progress view will not be
     * hidden until it has been shown for at least a minimum show time. If the
     * progress view was not yet visible, cancels showing the progress view.
     */
    private void hideLoadingProgress() {
        progressDismissed = true;
        removeProgressDelayedShowCallback();
        long diff = SystemClock.elapsedRealtime() - progressStartTime;
        if (diff >= MIN_SHOW_TIME || progressStartTime == -1) {
            // The progress spinner has been shown long enough
            // OR was not shown yet. If it wasn't shown yet,
            // it will just never be shown.
            setRouteInfoVisibilityIfNeeded(VISIBLE);
        } else {
            // The progress spinner is shown, but not long enough,
            // so put a delayed message in to hide it when its been
            // shown long enough.
            if (!progressPostedHide) {
                loadingRouteProgress.postDelayed(progressDelayedHide, MIN_SHOW_TIME - diff);
                progressPostedHide = true;
            }
        }
    }

    /**
     * Show the progress view after waiting for a minimum delay and hide route info. If
     * during that time, {@link #hideLoadingProgress()} is called, the progress is never made
     * visible.
     */
    private void showLoadingProgress() {
        // Reset the start time.
        progressStartTime = -1;
        progressDismissed = false;
        removeProgressDelayedHideCallback();
        if (!progressPostedShow) {
            loadingRouteProgress.postDelayed(progressDelayedShow, MIN_DELAY);
            progressPostedShow = true;
        }
    }

    @SuppressWarnings("WeakerAccess")
        // Used in inner classes
    void setRouteInfoVisibilityIfNeeded(int visibility) {
        // Check if tip is visible using the tip manager instead of the old routeTip view
        if (routeTipBottomSheetManager == null || !routeTipBottomSheetManager.isTipVisible()) {
            routeInfoGroup.setVisibility(visibility);
            // We don't show ascent if the value is null. We need to verify this.
            getViewModel().onAscentInfoVisibilityRequested();
        }
        //hide progress if previous elements are shown and vice versa
        if (visibility == VISIBLE) {
            loadingRouteProgress.setVisibility(GONE);
        } else {
            loadingRouteProgress.setVisibility(VISIBLE);
        }
    }

    /**
     * Import route creates an Intent instance for importing route, provides as a uri, with RoutePlannerActivity.
     */
    private static Intent createImportRouteIntent(Context context, Uri uri) {
            // Include gpx file uri in the intent
            return new Intent(context, RoutePlannerActivity.class)
                .setAction(Intent.ACTION_VIEW)
                .setData(uri)
                .putExtra(ROUTE_BUTTON_IMPORT_URI, uri)
                .putExtra(ROUTE_BUTTON_IMPORT, true);
    }

    public static void startActivityForImportingOrRedirectToLogin(
        CurrentUserController currentUserController,
        SignInFlowHook signInFlowHook,
        final Activity activity,
        final Uri uri
    ) {
        if (currentUserController.isLoggedIn()) {
            final Intent intent = createImportRouteIntent(activity, uri);
            activity.startActivityForResult(intent, STTConstants.RequestCodes.MAPEXPLORE_ROUTE_SAVE);
        } else {
            displayAccountNeededDialog(signInFlowHook, activity);
        }
    }

    public static void startCreateRouteActivityOrRedirectToLogin(
        CurrentUserController currentUserController,
        SignInFlowHook signInFlowHook,
        final Context context
    ) {
        startCreateRouteActivityOrRedirectToLogin(
            currentUserController,
            signInFlowHook,
            context,
            null,
            false,
            null,
            null,
            null,
            null,
            false,
            null,
            null,
            false
        );
    }

    public static void startCreateRouteActivityOrRedirectToLogin(
        CurrentUserController currentUserController,
        SignInFlowHook signInFlowHook,
        final Context context,
        @Nullable SuuntoCameraOptions cameraPosition,
        boolean trackUserLocation,
        @Nullable Integer requestCodeForResult,
        @Nullable LatLng startPoint,
        @Nullable LatLng endPoint,
        @Nullable RoutingMode routingMode,
        boolean chooseRoutingMode,
        @Nullable String locationPinAnalyticsProperty,
        @Nullable String sourceAnalyticsProperty,
        boolean isNavigate
    ) {
        if (currentUserController.isLoggedIn()) {
            Intent intent = newStartIntentCreateRoute(
                context,
                cameraPosition,
                trackUserLocation,
                startPoint,
                endPoint,
                routingMode,
                chooseRoutingMode,
                locationPinAnalyticsProperty,
                sourceAnalyticsProperty,
                isNavigate
            );

            if (requestCodeForResult != null && context instanceof Activity) {
                ((Activity)context).startActivityForResult(intent, requestCodeForResult);
            } else {
                context.startActivity(intent);
            }
        } else {
            displayAccountNeededDialog(signInFlowHook, context);
        }
    }

    public static void startActivityForCreatingRouteFromWorkoutOrRedirectToLogin(
        CurrentUserController currentUserController,
        SignInFlowHook signInFlowHook,
        final Context context,
        WorkoutHeader workoutHeader) {
        if (currentUserController.isLoggedIn()) {
            context.startActivity(newStartIntentSaveRoute(context, workoutHeader));
        } else {
            displayAccountNeededDialog(signInFlowHook, context);
        }
    }

    public static void displayAccountNeededDialog(SignInFlowHook signInFlowHook, Context context) {
        DialogHelper.showDialog(context, com.stt.android.R.string.account_needed_title,
            com.stt.android.R.string.account_needed_desc, (dialogInterface, i) -> {
                context.startActivity(signInFlowHook.newStartIntent(context, null));
            }, (dialogInterface, i) -> dialogInterface.dismiss());
    }

    /**
     * Only for internal use. Use
     * {@link #startCreateRouteActivityOrRedirectToLogin(CurrentUserController, SignInFlowHook, Context)} instead.
     */
    @NonNull
    private static Intent newStartIntentCreateRoute(
        Context context,
        @Nullable SuuntoCameraOptions cameraPosition,
        boolean trackUserLocation,
        @Nullable LatLng startPoint,
        @Nullable LatLng endPoint,
        @Nullable RoutingMode routingMode,
        boolean chooseRoutingMode,
        @Nullable String locationPinAnalyticsProperty,
        @Nullable String sourceAnalyticsProperty,
        boolean isNavigate
    ) {
        Intent intent = new Intent(context, RoutePlannerActivity.class);
        intent.putExtra(STTConstants.ExtraKeys.CAMERA_POSITION, cameraPosition);
        intent.putExtra(STTConstants.ExtraKeys.TRACK_USER_LOCATION, trackUserLocation);
        intent.putExtra(STTConstants.ExtraKeys.ROUTE_START_POINT, startPoint);
        intent.putExtra(STTConstants.ExtraKeys.ROUTE_END_POINT, endPoint);
        intent.putExtra(STTConstants.ExtraKeys.ROUTING_MODE, (Parcelable)routingMode);
        intent.putExtra(STTConstants.ExtraKeys.CHOOSE_ROUTING_MODE, chooseRoutingMode);
        intent.putExtra(STTConstants.ExtraKeys.ANALYTICS_LOCATION_PIN, locationPinAnalyticsProperty);
        intent.putExtra(STTConstants.ExtraKeys.ANALYTICS_ROUTE_PLANNING_STARTED_SOURCE, sourceAnalyticsProperty);
        intent.putExtra(STTConstants.ExtraKeys.IS_NAVIGATE, isNavigate);
        return intent;
    }

    /**
     * In theory the user won't be able to call this unless the user is logged in, so no need to go
     * through {@link #startCreateRouteActivityOrRedirectToLogin(CurrentUserController, SignInFlowHook, Context)}.
     */
    public static Intent newStartIntentEditRoute(Context context, @NonNull String routeId) {
        Intent intent = new Intent(context, RoutePlannerActivity.class);
        intent.putExtra(STTConstants.ExtraKeys.ROUTE_ID, routeId);
        return intent;
    }

    public static Intent newStartIntentCopyRoute(Context context, @NonNull String routeId, boolean watchRouteListFull) {
        Intent intent = new Intent(context, RoutePlannerActivity.class);
        intent.putExtra(STTConstants.ExtraKeys.ROUTE_ID, routeId);
        intent.putExtra(STTConstants.ExtraKeys.ROUTE_ACTION_COPY, true);
        intent.putExtra(STTConstants.ExtraKeys.IS_WATCH_ROUTE_LIST_FULL, watchRouteListFull);
        return intent;
    }

    public static IntentResolver<IntentKey.SaveRoute> getSaveRouteIntentResolver() {
        return (context, saveRoute) -> newStartIntentSaveRoute(context,
            saveRoute.getWorkoutHeader());
    }

    /**
     * In theory the user won't be able to call this unless the user is logged in, so no need to go
     * through
     * {@link #startCreateRouteActivityOrRedirectToLogin(CurrentUserController, SignInFlowHook,
     * Context)}.
     */
    public static Intent newStartIntentEditTopRoute(
        Context context,
        @NonNull String routeId,
        int activityType) {
        Intent intent = new Intent(context, RoutePlannerActivity.class);
        intent.putExtra(STTConstants.ExtraKeys.TOP_ROUTE_ID, routeId);
        intent.putExtra(STTConstants.ExtraKeys.ROUTE_ACTIVITY_TYPE, activityType);
        intent.putExtra(KEY_SHOW_ADD_TO_WATCH, true);
        intent.putExtra(KEY_FROM_POPULAR_ROUTE_DETAIL, true);
        return intent;
    }

    /**
     * Only for internal use. Use #startActivityForCreatingRouteFromWorkoutOrRedirectToLogin(
     * CurrentUserController, Context, WorkoutHeader) instead.
     */
    private static Intent newStartIntentSaveRoute(Context context,
        @NonNull WorkoutHeader workoutHeader) {
        Intent intent = new Intent(context, RoutePlannerActivity.class);
        intent.putExtra(STTConstants.ExtraKeys.WORKOUT_HEADER, workoutHeader);
        return intent;
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.activity_route_planner;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (savedInstanceState != null) {
            cursorIndex = savedInstanceState.getInt(CURSOR_INDEX, 0);
            needRestore = true;
        }
        if (savedInstanceState == null) {
            // Initialize view model only when doing a fresh start, not when restoring
            // from a saved state. Pass the intent data to view model for importing via
            // intent filter.
            getViewModel().initializeViewModel(getIntent().getData());
        }
        needTips = getIntent().getParcelableExtra(STTConstants.ExtraKeys.ROUTE_START_POINT) == null || getIntent().getParcelableExtra(STTConstants.ExtraKeys.ROUTE_END_POINT) == null;
        isNavigate = getIntent().getBooleanExtra(STTConstants.ExtraKeys.IS_NAVIGATE, false);
        bindViews();

        // Set needTips to the tip manager after bindViews
        if (routeTipBottomSheetManager != null) {
            routeTipBottomSheetManager.setNeedTips(needTips);
        }

        setupRouteInputName(false, false);
        setupMapOptionsLayout();
        boolean shouldLoadRoute = getIntent().hasExtra(STTConstants.ExtraKeys.ROUTE_ID);
        loadingSpinner.setVisibility(shouldLoadRoute ? VISIBLE : GONE);

        valueFont = ResourcesCompat.getFont(this, FontRefs.WORKOUT_VALUE_FONT_REF);
        unitFont = ResourcesCompat.getFont(this, FontRefs.WORKOUT_UNIT_FONT_REF);

        setSupportActionBar(topBarView);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setDisplayShowTitleEnabled(false);
        }

        if (isNavigate) {
            setEditViewsVisible(false);
            buttonContainerEnd.setVisibility(GONE);
            hideAddToWatch();
        }

        setEditViewsVisible(true);
        editActivityTypesBt.setOnClickListener(new ThrottlingOnClickListener(this::editActivityTypes));
        undoBt.setOnClickListener(this::undo);
        undoBt.hide();
        routingOptionsBt.setOnClickListener(this::routingOptionsClick);
        routingOptionsBt.show();
        otherOptionsBt.setOnClickListener(this::otherOptionsClick);
        otherOptionsBt.hide();
        addWaypointBt.setOnClickListener(this::addWaypointClick);
        addWaypointBt.hide();
        routeDuration.setOnClickListener(new ThrottlingOnClickListener(this::routeSpeedClick));
        speedLabel.setOnClickListener(new ThrottlingOnClickListener(this::routeSpeedClick));
        cancel.setOnClickListener(this::cancel);
        createMapFragment();
        compassTopPadding = getResources().getDimensionPixelOffset(com.stt.android.R.dimen.size_spacing_medium);
        bottomSheetBehavior = BottomSheetBehavior.from(bottomSheetView);
        bottomSheetBehavior.setPeekHeight(getResources().getDimensionPixelSize(
            com.stt.android.R.dimen.route_planner_bottom_sheet_peek_height));
        bottomSheetBehavior.setState(BottomSheetBehavior.STATE_HALF_EXPANDED);
        bottomSheetBehavior.addBottomSheetCallback(bottomSheetCallback);

        activityTypeEditor.setAllSelectable(false);
        showSelectedActivities(getViewModel().getDefaultActivities());
        activityTypeEditor.setOnValueChangedListener(
            newActivities -> getViewModel().updateActivities(newActivities, true));
        bottomSheetView.getViewTreeObserver()
            .addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    //noinspection deprecation
                    bottomSheetView.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                    updateMapPadding();
                }
            });

        saveButton.setOnClickListener(new ThrottlingOnClickListener(this::saveRoute));

        initPoiManager();
        disableTurnByTurnIfNeeded();

        showPOIsLiveData.observe(this, (show) -> updatePOIMarkers(getViewModel().getCurrentPOIs()));
        Transformations.distinctUntilChanged(turnByTurnEnabledLiveData).observe(this,
            (enabled) -> {
                if (enabled && !skipWaypointNamesInfo && !supportsWaypointNames()) {
                    // Show waypoint name support info
                    onNoWaypointNameSupport();
                }
                skipWaypointNamesInfo = false;
                getViewModel().setTurnByTurnEnabled(enabled);
                updateTurnByTurnMarkerVisibility(enabled);
                turnByTurnSwitch.setChecked(enabled);
            });
        turnByTurnSwitch.setOnCheckedChangeListener((compoundButton, isChecked) -> {
                mapSelectionModel.setTurnByTurnEnabled(isChecked);
            }
        );
        turnByTurnSwitch.setOnClickListener(view -> skipShowRoutingButtons = true);
        if (savedInstanceState != null) {
            String tmpRouteId = savedInstanceState.getString(ROUTE_IN_PROGRESS_ID);
            getViewModel().setRouteInProgressId(tmpRouteId);
            LatLng startPoint = savedInstanceState.getParcelable(START_POINT);
            getViewModel().setRouteInProgressStartPoint(startPoint);
            getViewModel().setRestoredCameraPosition(savedInstanceState.getParcelable(CAMERA_POSITION));
        }
        handleTrackUserLocationExtra(savedInstanceState);

        if (getIntent().getBooleanExtra(STTConstants.ExtraKeys.CHOOSE_ROUTING_MODE, false)) {
            getIntent().removeExtra(STTConstants.ExtraKeys.CHOOSE_ROUTING_MODE);
            getViewModel().hasPremium().observe(this, new Observer<>() {
                @Override
                public void onChanged(Boolean hasPremium) {
                    if (hasPremium) {
                        showRoutingModeBottomSheet();
                    }
                    getViewModel().hasPremium().removeObserver(this);
                }
            });
        }
    }

    private void setBottomSheetScrollView() {
        ViewGroup.LayoutParams layoutParams = bottomSheetScrollView.getLayoutParams();
        layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        bottomSheetScrollView.setLayoutParams(layoutParams);
    }

    private void setEditViewsVisible(boolean isVisible) {
        if (isVisible) {
            animateElevation(bottomSheetFloatingLayer.getElevation(), getResources().getDimension(com.stt.android.R.dimen.bottom_sheet_elevation));
            bottomSheetFloatingLayer.postDelayed(() ->
                bottomSheetFloatingLayer.setVisibility(View.VISIBLE), 100);
            setBottomSheetPaddingAndCollapsed();
        } else {
            animateElevation(bottomSheetFloatingLayer.getElevation(), 0f);
            bottomSheetFloatingLayer.postDelayed(() ->
                bottomSheetFloatingLayer.setVisibility(View.GONE), 100);
            setBottomSheetHeight(topBarView);
        }
    }

    private void animateElevation(float fromElevation, float toElevation) {
        if (bottomSheetFloatingLayer.getElevation() != toElevation) {
            if (animator != null) {
                animator.cancel();
            }
            animator = ObjectAnimator.ofFloat(bottomSheetFloatingLayer, "elevation", fromElevation, toElevation);
            animator.setDuration(100);
            animator.start();
        }
    }

    private void setupRouteInputName(boolean isVisible, boolean isFocused) {
        getViewModel().getRouteName().observe(this, name ->
            InputRouteNameJavaInterop.setupComposeView(routeInputName, name, isVisible, isFocused,
                (routeName) -> {
                    getViewModel().setRouteName(routeName);
                    return Unit.INSTANCE;
                }
            ));
    }

    private void setupMapOptionsLayout(){
        MapFloatingActionButtonsState initialState = getViewModel().getMapFloatingActionButtonState().getValue();
        if (initialState != null) {
            updateComposeView(mapOptionsLayout, initialState);
        }
        getViewModel().getMapFloatingActionButtonState().observe(this, state -> {
               updateComposeView(mapOptionsLayout, state);
        });
    }

    private void updateComposeView(ComposeView composeView, MapFloatingActionButtonsState state) {
        MapFloatingActionButtonsJavaInterop.setupComposeView(
            composeView,
            state.getShowInfo(),
            state.getShowSearch(),
            state.getShow3D(),
            state.getEnable3D(),
            state.getShowMapLayers(),
            state.getShowLocation(),
            state.getLocationEnabled(),
            () -> {
                infoClick();
                return Unit.INSTANCE;
            },
            () -> {
                // Search click, not used
                return Unit.INSTANCE;
            },
            () -> {
                boolean enable3d = !mapSelectionModel.getMap3dEnabled();
                getViewModel().handle3dOptionToggled(enable3d);
                mapSelectionModel.setMap3dEnabled(enable3d);
                return Unit.INSTANCE;
            },
            () -> {
                if (map != null) {
                    LatLng mapCenter = null;
                    if (map.getCameraPosition() != null) {
                        mapCenter = map.getCameraPosition().getTarget();
                    }
                    MapSelectionDialogFragment.newInstance(
                            map.getProviderName(),
                            true,
                            true,
                            true,
                            true,
                            mapCenter,
                            ROUTE_PLANNING_STARTED,
                            MapOption.MAP_STYLE,
                            false
                        )
                            .show(getSupportFragmentManager(), MapSelectionDialogFragment.FRAGMENT_TAG);
                }
                return Unit.INSTANCE;
            },
            () -> {
                locationClick();
                return Unit.INSTANCE;
            }
        );
    }

    protected void bindViews() {
        // Use findViewById and local references instead of view binding here because:
        // - sometimes included layouts could have null references when using view binding
        // - using view binding directly everywhere would cause large changes to the rest of the
        //   code in this class
        // - view binding references would be very long when there are nested includes
        topBarView = findViewById(R.id.toolbarRoutePlanner);
        waypointList = findViewById(R.id.waypoint_list);
        if (bottomSheetFloatingLayer == null) {
            ViewStub bottomSheetFloatingLayerStub = findViewById(R.id.bottomSheetFloatingLayer);
            bottomSheetFloatingLayer = bottomSheetFloatingLayerStub.inflate();
        }
        waypointList.setVisibility(View.VISIBLE);

        routeInputName = findViewById(R.id.routeNameInput);
        cancel = findViewById(com.stt.android.R.id.cancel);
        rootView = findViewById(com.stt.android.R.id.root);
        bottomSheetView = findViewById(R.id.bottomSheet);
        bottomSheetScrollView = findViewById(R.id.bottomSheetScrollView);
        routeInfoGroup = findViewById(R.id.routeInfoGroup);
        routeDistance = findViewById(R.id.routeDistance);
        routeDuration = findViewById(R.id.routeDuration);
        speedLabel = findViewById(R.id.speedLabel);
        routeAscentValue = findViewById(R.id.routeAscentValue);
        ascentContainer = findViewById(R.id.ascentContainer);
        routeDescentValue = findViewById(R.id.routeDescentValue);
        descentContainer = findViewById(R.id.descentContainer);
        mapCredit = findViewById(com.stt.android.R.id.credit);
        loadingSpinner = findViewById(R.id.loading_spinner);
        savingProgress = findViewById(R.id.savingProgress);
        buttonContainerStart = findViewById(R.id.buttonContainerStart);
        buttonContainerEnd = findViewById(R.id.buttonContainerEnd);
        routingOptionsBt = findViewById(R.id.routingOptionsBt);
        otherOptionsBt = findViewById(R.id.otherOptionsBt);
        addWaypointBt = findViewById(R.id.addWaypointBt);
        activityTypeEditor = findViewById(R.id.activityTypeEditor);
        undoBt = findViewById(R.id.undo);
        emptyMapClickCatcherView = findViewById(R.id.emptyClickCatcherView);
        mapContainer = findViewById(R.id.route_planner_main_content_map_container);
        loadingRouteProgress = findViewById(R.id.loadingRouteProgress);
        saveButton = findViewById(R.id.save);
        routeSections = findViewById(R.id.route_sections);
        routeAltitudeChartWithAxis = findViewById(R.id.routeAltitudeChartWithAxis);
        tapTheMapTip = findViewById(R.id.tapTheMapTip);
        editActivityTypesBt = findViewById(R.id.editActivityTypes);
        turnByTurnSwitch = findViewById(R.id.turnByTurnSwitch);
        suitableFor = findViewById(R.id.suitableFor);
        topSummary = findViewById(R.id.include_top_summary);
        dragHandle = findViewById(R.id.drag_handle);
        addWaypointTooltipsView = findViewById(R.id.addWayPointTooltips);
        mapOptionsLayout = findViewById(R.id.mapFloatingActionButtonLayout);

        // Initialize route tip bottom sheet manager
        routeTipBottomSheetManager = new RouteTipBottomSheetManager(rootView);
    }

    private void setBottomSheetHeight(View topBar) {
        int screenHeight = Resources.getSystem().getDisplayMetrics().heightPixels;

        bottomSheetView.getViewTreeObserver().addOnGlobalLayoutListener(
            new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    bottomSheetView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    ViewGroup.LayoutParams params = bottomSheetView.getLayoutParams();
                    params.height = screenHeight - topBar.getHeight();
                    bottomSheetView.setLayoutParams(params);

                    bottomSheetView.setPadding(
                        bottomSheetView.getPaddingLeft(),
                        bottomSheetView.getPaddingTop(),
                        bottomSheetView.getPaddingRight(),
                        0
                    );
                    bottomSheetView.requestLayout();

                    bottomSheetBehavior.setPeekHeight(getResources().getDimensionPixelSize(
                        com.stt.android.R.dimen.route_planner_bottom_sheet_peek_height));
                    updateBottomSheetHalfExpandedRatio();
                    bottomSheetBehavior.setState(BottomSheetBehavior.STATE_HALF_EXPANDED);
                }
            }
        );
    }

    private void handleTrackUserLocationExtra(@Nullable Bundle savedInstanceState) {
        boolean trackUserLocation =
            getIntent().getBooleanExtra(STTConstants.ExtraKeys.TRACK_USER_LOCATION, false);

        if (savedInstanceState != null) {
            trackUserLocation =
                savedInstanceState.getBoolean(TRACK_USER_LOCATION, trackUserLocation);
        }
        if (trackUserLocation) {
            // Moving the camera after the Activity (re)start is handled by the viewModel
            trackUserLocation(false);
        }
    }

    @Override
    public void updateTurnByTurnMarkerVisibility(boolean visible) {
        if (map == null) return;
        int size = markers.size();
        map.batchUpdate(() -> {
            for (int i = 0; i < size; ++i) {
                SuuntoMarker marker = markers.valueAt(i);
                if (marker.isTurnByTurnWaypoint()) {
                    if (visible) {
                        int segmentHashCode = markers.keyAt(i);
                        RouteSegment segment =
                            getViewModel().routePlannerModel.findSegment(segmentHashCode);
                        if (segment != null) {
                            Integer type = segment.waypointType();
                            if (type != null) {
                                setWayPointMarker(marker, segment, type);
                            }
                        }
                    } else {
                        setTurnByTurnSegmentMarker(marker);
                    }
                }
            }
            return null;
        });
    }

    private void setPlanningPointMarker(SuuntoMarker marker) {
        marker.setZPriority(MarkerZPriority.PLANNING_POINT);
        marker.setIcon(new SuuntoBitmapDescriptorFactory(this)
            .fromResource(R.drawable.route_segment_marker));
    }

    private void setEndPointMarker(SuuntoMarker latestMarker) {
        latestMarker.setZPriority(MarkerZPriority.END_POINT);
        latestMarker.setIcon(new SuuntoBitmapDescriptorFactory(this)
            .fromResource(com.stt.android.R.drawable.route_end_point_b));
    }

    private void setTurnByTurnSegmentMarker(SuuntoMarker marker) {
        marker.setZPriority(MarkerZPriority.DEFAULT);
        marker.setIcon(new SuuntoBitmapDescriptorFactory(this)
            .fromVectorDrawableResource(com.stt.android.R.drawable.hidden_turn_by_turn_marker));
    }

    private void setWayPointMarker(SuuntoMarker marker, RouteSegment segment, Integer type) {
        int markerDrawable = WaypointType.from(type).getWaypointIconResId();
        boolean isTurnByTurnWaypoint = segment.isTurnByTurnWaypointSegment();
        marker.setZPriority(isTurnByTurnWaypoint
            ? MarkerZPriority.TURN_BY_TURN_WAYPOINT
            : MarkerZPriority.WAYPOINT);
        marker.setIcon(new SuuntoBitmapDescriptorFactory(this)
            .fromVectorDrawableResource(markerDrawable));
    }

    private void infoClick() {
        getViewModel().trackRoutePlanningInstructionsButtonTapped();
        RouteInfoBottomSheetDialogFragment.newInstance().show(getSupportFragmentManager(),
            RouteInfoBottomSheetDialogFragment.FRAGMENT_TAG);
    }

    private void disableTurnByTurnIfNeeded() {
        if (!supportsWaypointNames()) {
            // Device does not support waypoint name so set turn-by-turn off by default.
            mapSelectionModel.setTurnByTurnEnabled(false);
        }
    }

    abstract protected boolean supportsWaypointNames();

    private void initPoiManager() {
        poiMarkerManager = new POIMarkerManager(this);
        poiMarkerManager.setLifecycle(getLifecycle());
    }

    private void createMapFragment() {
        // Prevents the world map being briefly shown before we actually move the camera
        setMapVisibility(INVISIBLE);

        FragmentManager fm = getSupportFragmentManager();
        mapFragment = (SuuntoSupportMapFragment)fm.findFragmentByTag(MAP_FRAGMENT_TAG);

        if (mapFragment == null) {
            SuuntoMapOptions options = new SuuntoMapOptions()
                .compassEnabled(true)
                .mapType(mapPresenter.getCurrentMapType().getName())
                .rotateGesturesEnabled(true)
                .scrollGesturesEnabled(true)
                .tiltGesturesEnabled(true)
                .zoomControlsEnabled(false)
                .zoomGesturesEnabled(true)
                .logoEnabled(getResources().getBoolean(com.stt.android.R.bool.maps_logo_enabled))
                .attributionEnabled(getResources().getBoolean(com.stt.android.R.bool.maps_logo_enabled))
                .map3dMode(mapSelectionModel.getMap3dEnabled())
                .scaleBarAlwaysVisible(true)
                .showMyLocationMarker(true);

            mapFragment = SuuntoSupportMapFragment.newInstance(options);
            fm.beginTransaction()
                .add(
                    R.id.route_planner_main_content_map_container,
                    mapFragment,
                    MAP_FRAGMENT_TAG
                )
                .commitNow();
        }
    }

    @Override
    public void onAttachFragment(@NonNull Fragment fragment) {
        super.onAttachFragment(fragment);

        if (fragment instanceof SuuntoSupportMapFragment) {
            mapFragment = (SuuntoSupportMapFragment) fragment;
            mapFragment.getMapAsync(this);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_route_planner, menu);
        currentMenu = menu;
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        super.onPrepareOptionsMenu(menu);
        if (isSaveStateInNavigation()) {
            setTopRightText(menu, com.stt.android.R.string.save);
            setActionBarTitle(getString(com.stt.android.R.string.title_activity_new_route_to));
        } else if (isNavigate) {
            setTopRightText(menu, com.stt.android.R.string.start);
        } else {
            MenuItem acceptRoute = menu.findItem(R.id.acceptRoute);
            boolean editEnabled = bottomSheetFloatingLayer.getVisibility() == View.VISIBLE;

            boolean visible = isAtLeastOneSegmentAvailable() && !editEnabled;
            acceptRoute.setVisible(visible);
        }

        return true;
    }

    protected boolean isSaveStateInNavigation() {
        return (getViewModel().restoreToSaveRouteState() || navigateOngoing) && isNavigate;
    }

    private void setTopRightText(Menu menu, @StringRes int rightTextId) {
        MenuItem acceptRouteItem = menu.findItem(R.id.acceptRoute);
        acceptRouteItem.setTitle(getString(rightTextId));
        if (rightTextId == com.stt.android.R.string.start) {
            acceptRouteItem.setEnabled(!startDisabled);
        }
        acceptRouteItem.setVisible(true);
        acceptRouteItem.setIcon(null);
    }

    @Override
    protected void onDestroy() {
        bottomSheetBehavior.removeBottomSheetCallback(bottomSheetCallback);
        if (map != null) {
            map.removeOnMapClickListener(this);
            map.removeOnMapLongClickListener(this);
            map.removeOnMarkerDragListener(this);
            map.removeOnCameraIdleListener(this);
            map3dEnabledLiveData.removeObserver(map::setMap3dModeEnabled);
        }
        disposable.dispose();
        super.onDestroy();
    }

    protected boolean isAtLeastOneSegmentAvailable() {
        return !polylines.isEmpty();
    }

    protected void showSelectedActivities(List<ActivityType> newActivities) {
        activityTypeEditor.setValue(newActivities);
        activityTypeEditor.removeAllViews();
        RecyclerView activityTypeIcons = new RecyclerView(this);
        activityTypeIcons.setHasFixedSize(true);
        activityTypeIcons.setLayoutManager(new LinearLayoutManager(this,
            LinearLayoutManager.HORIZONTAL, false));
        activityTypeIcons.setAdapter(
            new ActivityTypeIconsAdapter(
                newActivities,
                ActivityTypeIconsAdapter.Background.COLORED,
                4,
                true,
                activityGroupMapper)
        );
        activityTypeEditor.addView(activityTypeIcons);
        activityTypeEditor.invalidate();
    }

    @Override
    protected void onStart() {
        super.onStart();
        getViewModel().takeView(this);
        mapPresenter.takeView(this);
        getViewModel().updateActivities(activityTypeEditor.getValue(), false);
        if (!isAtLeastOneSegmentAvailable()) {
            bottomSheetBehavior.setState(BottomSheetBehavior.STATE_HALF_EXPANDED);
            invalidateOptionsMenu();
        }
        mapFragment.getMapAsync(map -> {
            updatePOIMarkers(getViewModel().getCurrentPOIs());

            map.addOnScaleListener(onScaleListener);

        });
        String routeId = getIntent().getStringExtra(STTConstants.ExtraKeys.ROUTE_ID);
        if (routeId == null || routeId.isEmpty()) {
            // For starting to plan a new route
            trackRoutePlanningStarted(mapSelectionModel.getTurnByTurnEnabled());
            showRouteTipIfNeeded();
        }
    }

    @Override
    public void onEditRoutePlanningStarted() {
        // For starting to edit an existing route
        trackRoutePlanningStarted(turnByTurnEnabledLiveData.getValue());
    }

    private void trackRoutePlanningStarted(Boolean turnWaypointsEnabled) {
        String pinExtra = getIntent().getStringExtra(STTConstants.ExtraKeys.ANALYTICS_LOCATION_PIN);
        String locationPin = pinExtra != null ? pinExtra : NO_LOCATION_PIN;
        String source = getIntent().getStringExtra(STTConstants.ExtraKeys.ANALYTICS_ROUTE_PLANNING_STARTED_SOURCE);
        AnalyticsProperties analyticsProperties = new AnalyticsProperties()
            .put(LOCATION_PIN, locationPin)
            .putOnOff(TURN_WAYPOINTS, turnWaypointsEnabled != null ? turnWaypointsEnabled : false)
            .put(ROUTE_PLANNING_STARTED_SOURCE, source);
        amplitudeAnalyticsTracker.trackEvent(ROUTE_PLANNING_STARTED, analyticsProperties);
        firebaseAnalyticsTracker.trackEvent(ROUTE_PLANNING_STARTED, analyticsProperties);
    }

    @Override
    protected void onStop() {
        // we need to clear the google map here, as the presenter will restore the view state and
        // draw markers/polylines again
        if (map != null) {
            map.removeOnScaleListener(onScaleListener);
            map.clear();
            poiMarkerManager.clear(map);
        }
        //fetching routes is cancelled here so fetching is also completed
        onFetchingRouteCompleted();
        getViewModel().dropView();
        mapPresenter.dropView();
        super.onStop();
    }

    @Override
    protected void onResume() {
        super.onResume();

        if (map != null && hasAnyLocationPermission()) {
            map.setLocationSource(getViewModel().getLocationSource());
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

        if (map != null && hasAnyLocationPermission()) {
            map.setLocationSource(null);
        }
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt(CURSOR_INDEX, cursorIndex);
        outState.putString(ROUTE_IN_PROGRESS_ID, getViewModel().getRouteInProgressId());
        outState.putParcelable(START_POINT, getViewModel().getStartPoint());
        if (map != null) {
            outState.putParcelable(CAMERA_POSITION, map.getCameraPosition());
        }
        outState.putBoolean(
            TRACK_USER_LOCATION,
            getViewModel().isTrackUserLocation());
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == android.R.id.home) {
            onBackPressed();
        } else if (itemId == R.id.acceptRoute) {
            item.setVisible(false);
            if (isSaveState()) {
                setBottomSheetCollapsed();
                isEditState = true;
            } else if (isNavigate) {
                getViewModel().navigateByRoute(getString(R.string.route_navigation_name));
            } else {
                setRouteNamingState(true);
                isEditState = true;
            }
        }
        return true;
    }

    private void setBottomSheetCollapsed() {
        bottomSheetScrollView.post(() -> {
            bottomSheetScrollView.scrollTo(0,0);
            bottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
        });
    }

    protected void setNavigationEditState() {
        setRouteNamingState(true);
    }

    protected boolean isSaveState() {
        if (currentMenu != null) {
            MenuItem acceptRouteItem = currentMenu.findItem(R.id.acceptRoute);
            return getString(com.stt.android.R.string.save).equals(acceptRouteItem.getTitle());
        } else {
            return false;
        }
    }

    void editActivityTypes(@SuppressWarnings("unused") View view) {
        activityTypeEditor.callOnClick();
    }

    /** @noinspection deprecation*/
    @Override
    public void onBackPressed() {
        if (bottomSheetFloatingLayer.getVisibility() == View.VISIBLE) {
            if (bottomSheetBehavior.getState() != BottomSheetBehavior.STATE_COLLAPSED) {
                setBottomSheetCollapsed();
            } else {
                if (isSaveStateInNavigation()) {
                    super.onBackPressed();
                } else {
                    isEditState = false;
                    resetEditViewsVisibleForRouteCreation();
                }
            }

            return;
        }

        if (bottomSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
            bottomSheetBehavior.setState(BottomSheetBehavior.STATE_HALF_EXPANDED);
            return;
        }

        if (isRouteNeededtoSave() && !isNavigate) {
            if (isAtLeastOneSegmentAvailable()) {
                DialogHelper.showDialog(this, true, com.stt.android.R.string.cancel,
                    com.stt.android.R.string.route_plan_cancel_confirm, com.stt.android.R.string.discard,
                    (dialog, which) -> cancelRoutePlanning(), com.stt.android.R.string.cancel, null);
            } else {
                cancelRoutePlanning();
            }
        } else {
            super.onBackPressed();
        }
    }

    private void updateMapPadding() {
        int bottomSheetHeight = rootView.getBottom() - bottomSheetView.getTop();
        updateMapPadding(bottomSheetHeight);
    }

    private void setMarginBottomForButtonContainer(int bottomSheetHeight) {
        ViewGroup.MarginLayoutParams buttonContainerStartParams = (ViewGroup.MarginLayoutParams) buttonContainerStart.getLayoutParams();
        buttonContainerStartParams.bottomMargin = bottomSheetHeight;
        buttonContainerStart.setLayoutParams(buttonContainerStartParams);

        ViewGroup.MarginLayoutParams buttonContainerEndParams = (ViewGroup.MarginLayoutParams) buttonContainerEnd.getLayoutParams();
        buttonContainerEndParams.bottomMargin = bottomSheetHeight;
        buttonContainerEnd.setLayoutParams(buttonContainerEndParams);
    }

    private void updateMapPadding(int bottomSheetHeight) {
        mapPresenter.resetPadding();
        // left and right can be 0, map provider specific defaults values are used with
        // MapHelper.updateMapPaddingWithDefaults
        mapPresenter.addPadding(
            0,
            compassTopPadding,
            0,
            bottomSheetHeight
        );

        if (map != null) {
            showScaleBar(map);
        }
    }

    private boolean isRouteNeededtoSave() {
        return getViewModel().hasStartPoint() && getViewModel().isEdited()
            || getViewModel().hasStartPoint() && getViewModel().routePlannerModel.isImportedRoute();
    }

    private void cancelRoutePlanning() {
        trackPlanningCancelAnalytics();
        setResult(RESULT_CANCELED);
        BaseRoutePlannerActivity.super.onBackPressed();
    }

    protected void updateBottomSheetHalfExpandedRatio() {
        float oldHalfExpandedRatio = bottomSheetBehavior.getHalfExpandedRatio();
        int newHalfExpandedHeight = calculateHalfExpandedHeight();
        float newHalfExpandedRatio = calculateRatioFromHeight(rootView.getHeight(), newHalfExpandedHeight);

        if (oldHalfExpandedRatio != newHalfExpandedRatio) {
            bottomSheetBehavior.setHalfExpandedRatio(newHalfExpandedRatio);
            if (bottomSheetBehavior.getState() == BottomSheetBehavior.STATE_HALF_EXPANDED) {
                // The layout pass has not yet completed here after peek height was updated.
                // Calculate map padding based on new half expanded height anyway.
                updateMapPadding(newHalfExpandedHeight);
                setMarginBottomForButtonContainer(newHalfExpandedHeight);
            }
        }

        bottomSheetBehavior.setDraggable(hasAtLeastOneSegment());
        bottomSheetScrollView.setScrollable(hasAtLeastOneSegment());
    }

    private static float calculateRatioFromHeight(int rootHeight, int newHalfExpandedHeight) {
        float newHalfExpandedRatio;
        if (rootHeight == 0) {
            newHalfExpandedRatio = 0.5f;
        } else if (0 < newHalfExpandedHeight && newHalfExpandedHeight < rootHeight) {
            newHalfExpandedRatio = (float) newHalfExpandedHeight / (float) rootHeight;
        } else {
            newHalfExpandedRatio = 0.5f;
        }
        return newHalfExpandedRatio;
    }

    private int calculateHalfExpandedHeight() {
        @DimenRes int dimenResId;

        boolean chartVisible = routeAltitudeChartWithAxis.getVisibility() == VISIBLE;
        boolean sectionsVisible = routeSections.getVisibility() == VISIBLE;

        if (!chartVisible || !hasAtLeastOneSegment()) {
            dimenResId = com.stt.android.R.dimen.route_planner_bottom_sheet_half_expanded_height;

        } else if (!sectionsVisible) {
            dimenResId = com.stt.android.R.dimen.route_planner_bottom_sheet_half_expanded_height_with_graph;

        } else {
            dimenResId = com.stt.android.R.dimen.route_planner_bottom_sheet_half_expanded_height_with_graph_and_route_sections;
        }

        int baseHeight = getResources().getDimensionPixelSize(dimenResId);
        if (isNavigate) {
            int offset = getResources().getDimensionPixelSize(com.stt.android.R.dimen.size_spacing_large);
            return baseHeight - offset;
        }

        return baseHeight;
    }

    void trackPlanningCancelAnalytics() {
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.ROUTE_PLANNING_CANCELLED,
            getViewModel().getRouteAnalytics(true));
        emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.ROUTE_PLANNING_CANCELLED,
            getViewModel().getRouteAnalytics(true).getMap());
    }

    void undo(@SuppressWarnings("unused") View view) {
        getViewModel().undoLastAction();
    }

    @Override
    public void onFetchingRouteStarted() {
        disableClickHandlers();
        showLoadingProgress();
        getViewModel().setFetchingRoute(true);
    }

    @Override
    public void onFetchingRouteCompleted() {
        enableClickHandlers();
        hideLoadingProgress();
        getViewModel().setFetchingRoute(false);
    }

    @Override
    public void showWaypointsIgnoredIfNeeded(int ignoredWaypoints) {
        if (ignoredWaypoints > 0) {
            int meters = MAX_SNAP_TO_ROUTE_WAYPOINT_DISTANCE_IN_METERS;
            long feet = Math.round(meters * MeasurementUnitKt.METERS_TO_FEET);
            String message = getString(
                R.string.route_import_ignored_waypoints,
                ignoredWaypoints, meters, feet);

            new AlertDialog.Builder(this)
                .setMessage(message)
                .setPositiveButton(getString(com.stt.android.R.string.ok), (dialogInterface, i) -> {})
                .create().show();
        }
    }

    private void enableClickHandlers() {
        // enable buttons
        setButtonEnabled(otherOptionsBt, true);
        setButtonEnabled(addWaypointBt, true);
        setButtonEnabled(routingOptionsBt, true);
        setButtonEnabled(undoBt, true);

        // enable map clicks
        emptyMapClickCatcherView.setVisibility(GONE);
    }

    private static void setButtonEnabled(FloatingActionButton button, boolean enabled) {
        button.setEnabled(enabled);
        button.setAlpha(enabled ? 1.0F : 0.5F);
    }

    private void disableClickHandlers() {
        // enable buttons
        setButtonEnabled(otherOptionsBt, false);
        setButtonEnabled(addWaypointBt, false);
        setButtonEnabled(routingOptionsBt, false);
        setButtonEnabled(undoBt, false);

        // disable map clicks with empty view on top of googlemaps
        emptyMapClickCatcherView.setVisibility(VISIBLE);
    }

    @SuppressLint("MissingPermission")
    @Override
    public void onMapReady(@NonNull SuuntoMap map) {
        this.map = map;
        showScaleBar(map);
        map.addOnMapClickListener(this);
        map.addOnMapLongClickListener(this);
        map.addOnMarkerDragListener(this);

        if (hasAnyLocationPermission()) {
            map.setLocationSource(getViewModel().getLocationSource());
            map.setMyLocationEnabled(true);
        }

        map.getUiSettings().setMyLocationButtonEnabled(false);

        map.addOnMarkerClickListener(onMarkerClickListener);

        map.addOnCameraIdleListener(this);
        map.addOnMapMoveListener(this);

        SuuntoMapExtensionsKt.addScopedOnCameraMoveListener(
            map,
            LifecycleOwnerKt.getLifecycleScope(this),
            100,
            () -> {
                SuuntoCameraPosition camera = map.getCameraPosition();
                if (camera != null) {
                    poiMarkerManager.setZoomLevel(camera.getZoom(), map);
                }
            });

        map.getUiSettings().setTiltTo3dEnabled(true);
        map3dEnabledLiveData.observe(this, map::setMap3dModeEnabled);
        map.addOnMap3dModeChangedWithTiltListener(onMap3dModeChangedWithTiltListener);

        updatePOIMarkers(getViewModel().getCurrentPOIs());
    }

    @Override
    public void onCameraIdle() {
        setMaxDistanceFromRoute();
        disableTrackingUserLocationIfCameraTooFar();
    }

    @Override
    public void onMapMoveBegin() {
        setTrackingUserLocation(false);
        getViewModel().onCameraMove(true);
    }

    @Override
    public void onMapMoveEnd() {

    }

    private void setMaxDistanceFromRoute() {
        if (map == null || mapFragment == null) return;

        SuuntoMapView suuntoMapView = mapFragment.getMapView();
        SuuntoCameraPosition cameraPosition = map.getCameraPosition();
        if (cameraPosition == null || suuntoMapView == null) return;

        // Calculate the width of the displayed map in meters at the camera center.
        double groundResolution = CoordinateUtils.groundResolutionAtZoomLevel(
            cameraPosition.getTarget().latitude, cameraPosition.getZoom());
        double mapWidthPixels = suuntoMapView.getView().getWidth();
        double mapWidthDp = mapWidthPixels / getResources().getDisplayMetrics().density;
        double mapWidthMeters = mapWidthDp * groundResolution;

        // Set 10% of the map width in meters as a limit to our route planner.
        // The limit is used to decide if a straight line is drawn instead of the path returned
        // by the GraphHopper API. See TP#106262 for more details.
        getViewModel().routePlannerModel.setMaxDistanceFromRoute((long) (0.10 * mapWidthMeters));
    }

    /**
     * Clicking on marker triggers both drag and click events, and we only deal with drag.
     */
    private boolean isMapClickable() {
        return System.currentTimeMillis() - lastAnnotationDragEndMillis >
            INTERVAL_BETWEEN_CLICK_AND_DRAG;
    }

    @Override
    public void onMapClick(@NonNull LatLng latLng, @Nullable String placeName) {
        if (addWaypointMode) {
            addWaypointTooltipsView.setVisibility(GONE);
            int waypointCount = getViewModel().routePlannerModel.getWaypointCount(true);
            if (waypointCount < MAX_WAYPOINT_COUNT) {
                getViewModel().findNearestPointsOnRoute(
                    new LatLng(latLng.latitude, latLng.longitude),
                    AddToRouteType.WAYPOINT);
                waypointAnalytics.trackWaypointCreationStarted();
            } else {
                onMaxWaypointCount();
            }
        } else {
            getViewModel().addRoutePoint(latLng);
        }
    }

    @Override
    public void onMapLongClick(@NonNull LatLng latLng) {
        if (hasAtLeastOneSegment()) {
            if (map != null) {
                SuuntoMapExtensionsKt.isMarkerClickedAsync(map, LifecycleOwnerKt.getLifecycleScope(this), latLng,
                    (isClicked, throwable) -> {
                        if (!isClicked) {
                            showAddToRouteSelection(latLng);
                        }
                        return null;
                    });
            }
        }
    }

    @Override
    public void onAddToRouteSelection(
        @NonNull AddToRouteType type,
        double latitude,
        double longitude) {
        if (type == AddToRouteType.WAYPOINT) {
            int waypointCount = getViewModel().routePlannerModel.getWaypointCount(true);
            if (waypointCount < MAX_WAYPOINT_COUNT) {
                getViewModel().findNearestPointsOnRoute(
                    new LatLng(latitude, longitude),
                    AddToRouteType.WAYPOINT);
                waypointAnalytics.trackWaypointCreationStarted();
            } else {
                onMaxWaypointCount();
            }
        } else if (type == AddToRouteType.PLANNING_POINT) {
            getViewModel().findNearestPointsOnRoute(
                new LatLng(latitude, longitude),
                AddToRouteType.PLANNING_POINT);
        }
    }

    @Override
    public void onFindNearestPointsCompleted() {
        NearestPoints nearestPoints = getViewModel().pendingNearestPoints;
        if (nearestPoints == null) return;

        if (nearestPoints.getType() == AddToRouteType.WAYPOINT) {
            Point point = nearestPoints.getNearestPoint();
            getViewModel().addPendingWaypoint(PointExtKt.toLatLng(point));
            int waypointCount = getViewModel().routePlannerModel.getWaypointCount(false);
            String name = getString(R.string.waypoint_type_waypoint) + (waypointCount + 1);
            WaypointDetails waypointDetails = new WaypointDetails(
                PointExtKt.toLatLng(point),
                WaypointType.WAYPOINT,
                point.getAltitude(),
                nearestPoints.distancesFromRouteStart(),
                name,
                null
            );
            showWaypointDetails(waypointDetails, WaypointDetailsMode.ADD);
        } else if (nearestPoints.getType() == AddToRouteType.PLANNING_POINT) {
            getViewModel().addPlanningPoints();
        }
    }

    @Override
    public void onFindWaypointsCompleted() {
        PointsWithDistances waypoints = getViewModel().pendingWaypoints;
        if (!waypoints.getPoints().isEmpty()) {
            Point point = waypoints.getPoints().get(0);
            WaypointType type = point.getType() != null // should not ever be null at this point
                ? WaypointType.from(point.getType()) : WaypointType.WAYPOINT;
            WaypointDetails waypointDetails = new WaypointDetails(
                PointExtKt.toLatLng(point),
                type,
                point.getAltitude(),
                waypoints.getDistances(),
                point.getName(),
                point.getDescription()
            );
            showWaypointDetails(waypointDetails, WaypointDetailsMode.EDIT);
        }
    }

    @Override
    public void onMaxTurnByTurnWaypointCount() {
        if (!showTurnByTurnMaxCountDialog) return;
        showTurnByTurnMaxCountDialog = false;
        FragmentManager fragmentManager = getSupportFragmentManager();
        if (fragmentManager.findFragmentByTag(TURN_BY_TURN_MAX_COUNT_DIALOG_TAG) == null) {
            DialogFragment dialog  = SimpleDialogFragment.newInstance(
                getString(R.string.turn_by_turn_max_count_reached_message),
                getString(R.string.turn_by_turn_max_count_reached_title),
                getString(com.stt.android.R.string.ok)
            );
            dialog.show(fragmentManager, TURN_BY_TURN_MAX_COUNT_DIALOG_TAG);
        }
    }

    @Override
    public void onMaxWaypointCount() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        if (fragmentManager.findFragmentByTag(WAYPOINT_MAX_COUNT_DIALOG_TAG) == null) {
            DialogFragment dialog  = SimpleDialogFragment.newInstance(
                getString(R.string.waypoint_max_count_reached_message),
                getString(R.string.waypoint_max_count_reached_title),
                getString(com.stt.android.R.string.ok)
            );
            dialog.show(fragmentManager, WAYPOINT_MAX_COUNT_DIALOG_TAG);
        }
    }

    private void onNoWaypointNameSupport() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        if (fragmentManager.findFragmentByTag(WAYPOINT_LIMITED_SUPPORT_DIALOG_TAG) == null) {
            DialogFragment dialog = SimpleDialogFragment.newInstance(
                getString(R.string.turn_by_turn_limited_support_message),
                getString(R.string.turn_by_turn_limited_support_title),
                getString(com.stt.android.R.string.ok)
            );
            dialog.show(fragmentManager, WAYPOINT_LIMITED_SUPPORT_DIALOG_TAG);
        }
    }

    @Override
    public void showPOIs(List<POI> pois) {
        updatePOIMarkers(pois);
    }

    private void updatePOIMarkers(List<POI> pois) {
        if (map != null) {
            Boolean show = showPOIsLiveData.getValue();
            if (show != null && show) {
                poiMarkerManager.setPOIs(pois, map);
            } else {
                poiMarkerManager.setPOIs(new ArrayList<>(), map);
            }
        }
    }

    @Override
    public void showPendingWaypoint(LatLng latLng) {
        mapFragment.getMapAsync(map -> {
            if (pendingWaypointMarker != null) {
                pendingWaypointMarker.remove();
            }
            SuuntoMarkerOptions waypointMarker =
                new SuuntoMarkerOptions()
                    .position(latLng)
                    .icon(new SuuntoBitmapDescriptorFactory(this)
                        .fromVectorDrawableResource(WaypointType.WAYPOINT.getWaypointIconResId())
                    )
                    .zPriority(MarkerZPriority.WAYPOINT)
                    .anchor(0.5f, 0.5f);
            pendingWaypointMarker = map.addMarker(waypointMarker);
        });
    }

    private void showAddToRouteSelection(
        LatLng latLng
    ) {
        FragmentManager fm = getSupportFragmentManager();
        Fragment fragment = fm.findFragmentByTag(AddToRouteFragment.TAG);
        if (fragment == null) {
            AddToRouteFragment.newInstance(latLng.latitude, latLng.longitude)
                .show(fm, AddToRouteFragment.TAG);
        }
    }

    private void showWaypointDetails(
        WaypointDetails waypointDetails,
        WaypointDetailsMode mode
    ) {
        FragmentManager fm = getSupportFragmentManager();
        Fragment fragment = fm.findFragmentByTag(WaypointDetailsFragment.TAG);
        if (fragment == null) {
            WaypointDetailsFragment.newInstance(waypointDetails, mode)
                .show(fm, WaypointDetailsFragment.TAG);
        }
    }

    private void hideAddToRouteSelectionDialog() {
        FragmentManager fm = getSupportFragmentManager();
        Fragment fragment = fm.findFragmentByTag(AddToRouteFragment.TAG);
        if (fragment != null) {
            ((AddToRouteFragment) fragment).dismiss();
        }
    }

    @Override
    public void onDismiss() {
        SuuntoMarker marker = pendingWaypointMarker;
        if (marker != null) {
            marker.remove();
        }
        getViewModel().removePendingWaypoint();
    }

    @Override
    public void onSave(
        @NonNull String waypointName,
        @NonNull WaypointType waypointType,
        @Nullable String waypointDescription
    ) {
        getViewModel().addWaypoints(waypointName, waypointType, waypointDescription);
        trackWaypointCreated(waypointName, waypointType);
    }

    private void trackWaypointCreated(
        @NonNull String waypointName,
        @NonNull WaypointType waypointType
    ) {
        if (getViewModel().pendingNearestPoints != null) {
            waypointAnalytics.trackWaypointCreated(
                waypointType.getTypeForAnalytics(),
                !waypointName.equals(getString(waypointType.getNameResId())),
                getViewModel().pendingNearestPoints.getResults().size() > 1
            );
        }
    }

    @Override
    public void onEdit(
        @NonNull String waypointName,
        @NonNull WaypointType waypointType,
        @Nullable String waypointDescription
    ) {
        getViewModel().editWaypoints(waypointName, waypointType, waypointDescription);
        trackWaypointEdited(waypointName, waypointType);
    }

    private void trackWaypointEdited(
        @NonNull String waypointName,
        @NonNull WaypointType waypointType
    ) {
        if (getViewModel().pendingWaypoints != null && !getViewModel().pendingWaypoints.getPoints().isEmpty()) {
            Point point = getViewModel().pendingWaypoints.getPoints().get(0);
            int currentType = point.getType() != null ? point.getType() : -1;
            boolean typeEdited = waypointType.getTypeId() != currentType;
            waypointAnalytics.trackWaypointSaveEdited(
                !waypointName.equals(point.getName()),
                !waypointName.equals(getString(waypointType.getNameResId())),
                typeEdited,
                WaypointType.isTurnByTurnWaypointType(waypointType),
                WaypointType.from(currentType).getTypeForAnalytics(),
                typeEdited ? waypointType.getTypeForAnalytics() : null
            );
        }
    }

    @Override
    public void onDelete() {
        getViewModel().deleteWaypoints();
        trackWaypointDeleted();
    }

    private void trackWaypointDeleted() {
        if (getViewModel().pendingWaypoints != null && !getViewModel().pendingWaypoints.getPoints().isEmpty()) {
            Point point = getViewModel().pendingWaypoints.getPoints().get(0);
            Integer currentType = point.getType();
            if(currentType != null) {
                WaypointType waypointType = WaypointType.from(currentType);
                waypointAnalytics.trackWaypointDelete(
                    waypointType,
                    !getString(waypointType.getNameResId()).equals(point.getName()),
                    getViewModel().pendingWaypoints.getPoints().size() > 1
                );
            }
        }
    }

    @Override
    public void onPointTypeChanged(int pointTypeId) {
        WaypointType waypointType = WaypointType.from(pointTypeId);
        Fragment fragment =
            getSupportFragmentManager().findFragmentByTag(WaypointDetailsFragment.TAG);
        if (fragment != null) {
            ((WaypointDetailsFragment) fragment).updateWaypointType(waypointType);
        }
    }

    void routeSpeedClick(@SuppressWarnings("unused") View view) {
        DialogFragment fragment =
            SpeedDialogFragment.newAvgSpeedInstance(
                getViewModel().getFormattedAvgSpeed(),
                getString(getViewModel().getSpeedUnitRes())
            );
        fragment.show(getSupportFragmentManager(), "dialog");
    }

    private void animateVisibility(View view, int visibility) {
        view.animate().cancel();
        if (visibility == View.VISIBLE) {
            view.setAlpha(0f);
            view.setVisibility(View.VISIBLE);
            view.animate().alpha(1f).setDuration(200).withEndAction(null).start();
        } else {
            view.animate().alpha(0f).setDuration(200).withEndAction(() -> view.setVisibility(View.GONE)).start();
        }
    }

    private void showMapOptionsLayout() {
        if (mapOptionsLayout.getVisibility() != View.VISIBLE) {
            animateVisibility(mapOptionsLayout, View.VISIBLE);
        }
    }

    private void hideMapOptionsLayout() {
        if (mapOptionsLayout.getVisibility() != View.GONE) {
            animateVisibility(mapOptionsLayout, View.GONE);
        }
    }

    void askRouteName(@SuppressWarnings("unused") View view) {
        if (bottomSheetBehavior.getState() == BottomSheetBehavior.STATE_COLLAPSED) {
            ignoreHalfExpanded = true;
        }
        bottomSheetBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
        AnalyticsProperties analyticsProperties = getViewModel().getRouteAnalytics(false);
        analyticsProperties.put(AnalyticsEventProperty.ELEVATION_GRAPH_SCRUBBING, routeAltitudeChartWithAxis.getScrubbingCount());
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.ROUTE_PLANNING_FINISHED,
            analyticsProperties);
        emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.ROUTE_PLANNING_FINISHED,
            analyticsProperties.getMap());
    }

    private void setCompassEnabled(boolean enabled) {
        mapFragment.getMapAsync(map -> map.setCompassEnabled(enabled));
    }

    private void showScaleBar(@NonNull final SuuntoMap map) {
        Resources res = getResources();
        int padding = res.getDimensionPixelOffset(com.stt.android.R.dimen.map_scale_bar_margin_top);
        int mediumPadding = res.getDimensionPixelOffset(com.stt.android.R.dimen.size_spacing_medium);
        int topPadding = MapTypeExtKt.isAvalancheMap(getCurrentMapType())
            ? res.getDimensionPixelOffset(com.stt.android.R.dimen.map_compass_padding_top) + mediumPadding
            : padding;

        map.showScaleBar(scaleBarOptionsFactory
            .createWithTopMarginRes(
                topPadding,
                com.stt.android.R.dimen.map_scale_bar_margin_left
            ));
    }

    private void saveRoute(View view) {
        getViewModel().saveOrUpdateRoute(routeAltitudeChartWithAxis.getScrubbingCount(), isNavigate);
    }

    private boolean hasStartPoint() {
        return getViewModel().routePlannerModel.hasStartPoint();
    }

    protected boolean hasAtLeastOneSegment() {
        return !getViewModel().routePlannerModel.getSegments().isEmpty();
    }

    void cancel(@SuppressWarnings("unused") View view) {
        hideKeyboard(view);
        if (bottomSheetBehavior.getState() != BottomSheetBehavior.STATE_COLLAPSED) {
            setBottomSheetCollapsed();
        } else {
            isEditState = false;
            resetEditViewsVisibleForRouteCreation();
        }
    }

    void routingOptionsClick(@SuppressWarnings("unused") View view) {
        showRoutingModeBottomSheet();
    }

    private void showRoutingModeBottomSheet() {
        FragmentManager fm = getSupportFragmentManager();
        Fragment fragment = fm.findFragmentByTag(RoutingModeDialogFragment.FRAGMENT_TAG);
        if (fragment == null) {
            RoutingModeDialogFragment dialog = RoutingModeDialogFragment.newInstance(getViewModel().getRoutingMode(), false);
            dialog.show(fm, RoutingModeDialogFragment.FRAGMENT_TAG);
        }
    }

    void otherOptionsClick(@SuppressWarnings("unused") View view) {
        FragmentManager fm = getSupportFragmentManager();
        Fragment fragment = fm.findFragmentByTag(PlannerOtherOptionsDialogFragment.FRAGMENT_TAG);
        if (fragment == null) {
            PlannerOtherOptionsDialogFragment dialog = PlannerOtherOptionsDialogFragment.newInstance();
            dialog.setIsRouteClosed(getViewModel().isRouteClosed());
            dialog.show(fm, PlannerOtherOptionsDialogFragment.FRAGMENT_TAG);
        }
    }

    void addWaypointClick(@SuppressWarnings("unused") View view) {
        updateAddWaypointMode(!addWaypointMode);
    }

    public void locationClick() {
        trackUserLocation(true);
    }

    private void trackUserLocation(boolean moveCamera) {
        if (!hasLocationPermissions()) {
            if (EasyPermissions.somePermissionPermanentlyDenied(
                this, Arrays.asList(PermissionUtils.LOCATION_PERMISSIONS))) {
                showLocationPermanentlyDeniedDialog();
            } else {
                PermissionUtils.requestPermissionsIfNeeded(this,
                    PermissionUtils.LOCATION_PERMISSIONS,
                    getString(com.stt.android.R.string.location_permission_rationale_for_location));
            }
        } else {
            getViewModel().onMyLocationClicked(moveCamera);
            setTrackingUserLocation(true);
        }
    }

    private void showLocationPermanentlyDeniedDialog() {
        SimpleDialogFragment sdf = SimpleDialogFragment.newInstance(
            getString(R.string.open_settings_to_enable_location),
            getString(R.string.location_permission_required_title),
            getString(R.string.open_settings_to_enable_location_button),
            getString(com.stt.android.R.string.cancel)
        );

        sdf.show(getSupportFragmentManager(), LOCATION_PERMISSION_DIALOG_TAG);
    }

    @Override
    public void onRoutingChanged(@NonNull RoutingMode routingMode) {
        @DrawableRes int imageResource = switch (routingMode) {
            case STRAIGHT -> R.drawable.icon_routes_free;
            case BIKE -> R.drawable.icon_routes_bike;
            case FOOT -> R.drawable.icon_routes_foot;
            case MTB -> R.drawable.icon_routes_mountainbike;
            case RACING_BIKE -> R.drawable.icon_routes_roadbike;
            case BEARING_NAVIGATION -> R.drawable.icon_bearing_navigation;
        };

        routingOptionsBt.setImageResource(imageResource);
        routingOptionsBt.hide(); // TODO remove hide() call once material library bug is fixed
        routingOptionsBt.show();
    }

    @Override
    public void onTurnByTurnWaypointsEnabledChanged(boolean enabled, boolean byTheUser) {
        skipWaypointNamesInfo = enabled && !byTheUser;
        mapSelectionModel.setTurnByTurnEnabled(enabled);
    }

    @Override
    public void showStartPoint(final double latitude, final double longitude) {
        mapFragment.getMapAsync(map -> {
            SuuntoMarkerOptions startMarker =
                new SuuntoMarkerOptions()
                    .position(new LatLng(latitude, longitude))
                    .icon(new SuuntoBitmapDescriptorFactory(this)
                        .fromResource(com.stt.android.R.drawable.route_start_point_a))
                    .zPriority(MarkerZPriority.START_POINT)
                    .draggable(false)
                    .anchor(0.5f, 0.5f);
            SuuntoMarker marker = map.addMarker(startMarker);
            if (marker != null) {
                addMarker(START_POINT_SEGMENT, marker);
                segmentsByEndMarker.append(marker.hashCode(), START_POINT_SEGMENT);
            }
        });
        if (!skipShowRoutingButtons) {
            undoBt.show();
        }
        showRouteTipIfNeeded();
    }

    private void addMarker(int key, @NonNull SuuntoMarker marker) {
        markers.append(key, marker);
        markerOriginalPositions.append(marker.hashCode(), marker.getPosition());
    }

    @Override
    public void onRouteUpdatedFully() {
        skipShowRoutingButtons = false;
    }

    @Override
    public void removeStartPoint() {
        removeMarker(START_POINT_SEGMENT);
        undoBt.hide();
        showRouteTipIfNeeded();
    }

    @Override
    public void moveMapTo(double latitude, double longitude) {
        mapFragment.getMapAsync(map -> {
                map.animateCamera(SuuntoCameraUpdateFactory.newLatLng(
                    new LatLng(latitude, longitude)));
                setMapVisibility(VISIBLE);
            }
        );
    }

    @Override
    public void moveMapTo(double latitude, double longitude, final float zoom) {
        final LatLng lastLocationLatLng = new LatLng(latitude, longitude);
        mapFragment.getMapAsync(map -> {
            SuuntoCameraOptions cameraOptions =
                new SuuntoCameraOptions.Builder()
                    .target(lastLocationLatLng)
                    .zoom(zoom)
                    .build();
            SuuntoCameraUpdate cameraUpdate =
                SuuntoCameraUpdateFactory.newCameraPosition(cameraOptions);
            map.animateCamera(cameraUpdate);
            setMapVisibility(VISIBLE);
        });
    }

    @Override
    public void moveMapTo(SuuntoCameraOptions cameraOptions, int delayMs) {
        mapFragment.getMapAsync(map -> {
            SuuntoCameraUpdate cameraUpdate =
                SuuntoCameraUpdateFactory.newCameraPosition(cameraOptions);
            if (rootView != null) {
                // Delay can be used to let the view be fully constructed to let the map get
                // correct paddings.
                rootView.postDelayed(() -> {
                    map.moveCamera(cameraUpdate);
                    setMapVisibility(VISIBLE); // Prevents the world map being briefly shown
                }, delayMs);
            }
        });
    }

    @Override
    public void moveMapTo(LatLngBounds latLngBounds, int delayMs) {
        mapFragment.getMapAsync(map -> {
            if (rootView != null) {
                // Delay can be used to let the view be fully constructed to let the map get
                // correct paddings.
                rootView.postDelayed(() -> {
                    SuuntoCameraUpdate cameraUpdate = SuuntoCameraUpdateFactory
                        .newLatLngBounds(latLngBounds, getPaddingForMapZoom());
                    map.moveCamera(cameraUpdate);
                    setMapVisibility(VISIBLE); // Prevents the world map being briefly shown
                }, delayMs);
            }
        });
    }

    private int getPaddingForMapZoom() {
        SuuntoMapView mapView = mapFragment.getMapView();
        if (mapView != null) {
            int padding = getResources().getDimensionPixelSize(com.stt.android.R.dimen.size_spacing_xxlarge);
            return mapView.getPaddingBottom() + padding;
        } else {
            return 0;
        }
    }

    @Override
    public void moveStartPoint(LatLng point) {
        SuuntoMarker marker = markers.get(START_POINT_SEGMENT);
        if (marker != null) {
            marker.setPosition(point);
        }
    }

    @Override
    public void myLocationNotAvailable() {
        showMessage(R.string.no_current_location);
        setTrackingUserLocation(false);
    }

    private void setTrackingUserLocation(Boolean enabled) {
        getViewModel().setTrackUserLocation(enabled);
    }

    private void disableTrackingUserLocationIfCameraTooFar() {
        ExploreUtils.shouldDisableUserLocationTrackingDueToCameraDistance(map, disable -> {
            if (disable) {
                setTrackingUserLocation(false);
            }
            return Unit.INSTANCE;
        });
    }

    protected void showMessage(@StringRes int messageId, @Nullable Object... formatArgs) {
        String message = getString(messageId, formatArgs);
        Snackbar snackbar = Snackbar.make(rootView, message, Snackbar.LENGTH_LONG);
        // Set same elevation as for bottom sheet to make sure snackbar is not hidden behind it
        snackbar.getView().setElevation(bottomSheetView.getElevation());
        snackbar.show();
    }

    @Override
    public void errorImportingRoute() {
        final DialogFragment dialog =
            SimpleDialogFragment.newInstance(getString(com.stt.android.R.string.error_importing_route), null,
                getString(com.stt.android.R.string.ok));
        dialog.show(getSupportFragmentManager(), ROUTE_IMPORT_ERROR_DIALOG);
    }

    @Override
    public void errorSavingRoute() {
        showMessage(com.stt.android.R.string.error_saving_data);
        saveButton.setEnabled(true);
    }

    @Override
    public void showSegments(final List<RouteSegment> segments, boolean updatePreviousSegment) {
        if (map != null) {
            showSegments(map, segments, updatePreviousSegment);
        } else {
            mapFragment.getMapAsync(map -> {
                showSegments(map, segments, updatePreviousSegment);
            });
        }
    }

    protected void showSegments(SuuntoMap map, List<RouteSegment> segments,
        boolean updatePreviousSegment) {
        map.batchUpdate(() -> {
            for (RouteSegment segment : segments) {
                showSegment(this.map, segment, updatePreviousSegment);
            }
            return null;
        });
    }

    private void showSegment(SuuntoMap map, RouteSegment segment, boolean updatePreviousSegment) {
        if (map == null) return;

        // Climb guidance is not supported in ST
        if (FlavorUtils.INSTANCE.isSportsTracker()) {
            SuuntoPolyline polyline = addPolyline(map, segment.getRoutePoints());
            polylines.put(segment.hashCodeIgnorePosition(), polyline);
        }

        SuuntoMarkerOptions endSegmentMarker = getSuuntoMarkerOptions(segment);

        if (updatePreviousSegment && segment.getPosition() > 0 && markers.size() > 1) {
            int latestSegmentHashCode = segmentHashCodes.get(segment.getPosition() - 1);
            RouteSegment latestSegment =
                getViewModel().routePlannerModel.findSegment(latestSegmentHashCode);
            if (latestSegment != null) {
                SuuntoMarker marker = markers.get(latestSegmentHashCode);
                Integer type = latestSegment.waypointType();
                boolean turnByTurnEnabled = turnByTurnEnabledLiveData.getValue() != null
                    ? turnByTurnEnabledLiveData.getValue()
                    : false;
                if (type != null &&
                    (!WaypointType.isTurnByTurnWaypointType(type) || turnByTurnEnabled)) {
                    // Use waypoint marker
                    setWayPointMarker(marker, segment, type);
                } else if (WaypointType.isTurnByTurnWaypointType(type)) {
                    setTurnByTurnSegmentMarker(marker);
                } else {
                    setPlanningPointMarker(marker);
                }
            }
        }
        SuuntoMarker endPointMarker = map.addMarker(endSegmentMarker);
        if (endPointMarker != null) {
            addMarker(segment.hashCodeIgnorePosition(), endPointMarker);
            segmentsByEndMarker.append(endPointMarker.hashCode(), segment.hashCodeIgnorePosition());
        }
        segmentHashCodes.add(segment.getPosition(), segment.hashCodeIgnorePosition());
        if (hasAtLeastOneSegment()) {
            hideRouteTipIfNeeded();
        } else {
            showRouteTipIfNeeded();
        }

        updateOtherOptionButtonVisibility();
        updateAddWaypointButtonVisibility();
        invalidateOptionsMenu();
    }

    private SuuntoPolyline addPolyline(SuuntoMap map, List<Point> points) {
        int color = ContextCompat.getColor(BaseRoutePlannerActivity.this,
            com.stt.android.core.R.color.route_primary);

        float lineWidth = Math.max(
            getResources().getDimensionPixelSize(com.stt.android.R.dimen.route_map_thick_stroke_width),
            4.0f
        );

        SuuntoPolylineOptions options = new SuuntoPolylineOptions(
            RouteUtils.routePointsToLatLngList(points))
            .width(lineWidth)
            .color(color)
            .zIndex(SuuntoMap.ROUTE_POLYLINE_Z_INDEX);

         return map.addPolyline(options);
    }

    @NonNull
    private SuuntoMarkerOptions getSuuntoMarkerOptions(RouteSegment segment) {
        boolean isWaypointSegment = segment.isWaypointSegment();
        boolean isTurnByTurnWaypoint = segment.isTurnByTurnWaypointSegment();
        float rotation = isTurnByTurnWaypoint
            ? RouteUtils.calculateWaypointHeading(segment.getRoutePoints())
            : 0f;

        SuuntoMarkerOptions endSegmentMarker = new SuuntoMarkerOptions()
            .position(PointExtKt.toLatLng(segment.getEndPoint()))
            .isWaypoint(isWaypointSegment)
            .isTurnByTurnWaypoint(isTurnByTurnWaypoint)
            .rotation(rotation)
            .draggable(false)
            .flat(isTurnByTurnWaypoint)
            .zPriority(MarkerZPriority.DEFAULT)
            .anchor(0.5f, 0.5f);

        Integer type = segment.waypointType();
        boolean turnByTurnEnabled = turnByTurnEnabledLiveData.getValue() != null
            ? turnByTurnEnabledLiveData.getValue()
            : false;

        if (type != null && (!isTurnByTurnWaypoint || turnByTurnEnabled)) {
            // Use waypoint marker
            int markerDrawable = WaypointType.from(type).getWaypointIconResId();
            endSegmentMarker.setZPriority(isTurnByTurnWaypoint
                ? MarkerZPriority.TURN_BY_TURN_WAYPOINT
                : MarkerZPriority.WAYPOINT);
            endSegmentMarker.setIconDescriptor(new SuuntoBitmapDescriptorFactory(this)
                .fromVectorDrawableResource(markerDrawable));
        }
        else if (isTurnByTurnWaypoint) {
            endSegmentMarker.setZPriority(MarkerZPriority.DEFAULT);
            endSegmentMarker.setIconDescriptor(new SuuntoBitmapDescriptorFactory(this)
                .fromVectorDrawableResource(com.stt.android.R.drawable.hidden_turn_by_turn_marker));
        } else {
            boolean isEndPointB =
                segment.getPosition() == getViewModel().routePlannerModel.getSegments().size() - 1;
            endSegmentMarker.setZPriority(isEndPointB
                ? MarkerZPriority.END_POINT
                : MarkerZPriority.PLANNING_POINT);
            endSegmentMarker.setIconDescriptor(new SuuntoBitmapDescriptorFactory(this)
                .fromResource(
                    isEndPointB
                    ? com.stt.android.R.drawable.route_end_point_b
                    : R.drawable.route_segment_marker
                ));
        }
        return endSegmentMarker;
    }

    @Override
    public void setRouteDistance(String formattedDistance, int distanceUnitRes) {
        routeDistance.setText(TextFormatter.formatValueAndUnit(
            formattedDistance,
            getString(distanceUnitRes),
            valueSpanFactory,
            unitSpanFactory
        ));
    }

    @Override
    public void setRouteAscent(String formattedAscent, int altitudeUnitRes) {
        ascentContainer.setVisibility(VISIBLE);
        routeAscentValue.setText(TextFormatter.formatValueAndUnit(
            formattedAscent,
            getString(altitudeUnitRes),
            valueSpanFactory,
            unitSpanFactory
        ));
    }

    @Override
    public void setRouteDescent(String formattedDescent, int altitudeUnitRes) {
        descentContainer.setVisibility(VISIBLE);

        routeDescentValue.setText(TextFormatter.formatValueAndUnit(
            formattedDescent,
            getString(altitudeUnitRes),
            valueSpanFactory,
            unitSpanFactory
        ));
    }

    @Override
    public void hideAscentDescentInfo() {
        ascentContainer.setVisibility(GONE);
        descentContainer.setVisibility(GONE);
    }

    @Override
    public void setRouteDurationAndSpeed(String formattedDuration, String avgSpeed,
        int speedUnitRes) {
        String formattedAvgSpeed = avgSpeed + " " + getString(speedUnitRes);
        speedLabel.setText(formattedAvgSpeed);

        routeDuration.setText(TextFormatter.formatValueAndUnit(
            formattedDuration,
            null,
            valueSpanFactory,
            null
        ));
    }

    @Override
    public void removeSegments(List<RouteSegment> removedSegments, boolean updateLatestSegment) {
        if (map == null) return;

        map.batchUpdate(() -> {
            for (RouteSegment removedSegment : removedSegments) {
                removeSegment(removedSegment);
            }
            if (updateLatestSegment && segmentHashCodes.size() > 0) {
                int latestSegmentHashCode = segmentHashCodes.get(segmentHashCodes.size() - 1);
                SuuntoMarker latestMarker = markers.get(latestSegmentHashCode);
                if (latestMarker != null) {
                    setEndPointMarker(latestMarker);
                }
            }
            return null;
        });
        updateBottomSheetHalfExpandedRatio();
        if (!isAtLeastOneSegmentAvailable()) {
            showRouteTipIfNeeded();
        }

        updateOtherOptionButtonVisibility();
        updateAddWaypointButtonVisibility();
        invalidateOptionsMenu();
    }

    private void removeMarker(int key) {
        SuuntoMarker marker = markers.get(key);
        if (marker != null) {
            markerOriginalPositions.remove(marker.hashCode());
            marker.remove();
            markers.remove(key);
        }
    }

    void updateOtherOptionButtonVisibility() {
        if (isAtLeastOneSegmentAvailable()) {
            otherOptionsBt.show();
        } else {
            otherOptionsBt.hide();
        }
    }

    void updateAddWaypointButtonVisibility() {
        boolean atLeastOneSegmentAvailable = isAtLeastOneSegmentAvailable();
        if (addWaypointMode && !atLeastOneSegmentAvailable) {
            updateAddWaypointMode(false);
        }
        if (atLeastOneSegmentAvailable) {
            addWaypointBt.show();
        } else {
            addWaypointBt.hide();
        }
    }

    private void updateAddWaypointMode(Boolean enabled) {
        addWaypointMode = enabled;
        addWaypointBt.setActivated(enabled);
        if (enabled) {
            bottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
            addWaypointTooltipsView.setVisibility(VISIBLE);
            tapTheMapTip.setText(R.string.tap_the_route_add_waypoint);
        } else {
            addWaypointTooltipsView.setVisibility(GONE);
            tapTheMapTip.setText(com.stt.android.R.string.tap_the_map_altitude_graph);
        }
    }

    private void removeSegment(RouteSegment removedSegment) {
        int key = removedSegment.hashCodeIgnorePosition();
        removePolyline(key);
        removeMarker(key);
        removeSegmentHashCodes(key);
    }

    private void removePolyline(int key) {
        SuuntoPolyline polyline = polylines.get(key);
        if (polyline != null) {
            polyline.remove();
            polylines.remove(key);
        }
    }

    @Override
    public void addWaypoint(@NonNull AddWaypointsActionResult result) {
        if (map == null) return;
        map.batchUpdate(() -> {
                for (AddWaypointAction action : result.getActions()) {
                    removeSegment(action.getOriginalSegment());
                    showSegment(map, action.getFirstSegment(), false);
                    showSegment(map, action.getSecondSegment(), false);
                }
                return null;
            }
        );
    }

    @Override
    public void editWaypoint(@NonNull EditWaypointActionResult result) {
        if (map == null) return;
        map.batchUpdate(() -> {
                for (EditWaypointAction action : result.getActions()) {
                    removeSegment(action.getOriginal());
                    showSegment(map, action.getUpdated(), false);
                }
                return null;
            }
        );
    }

    @Override
    public void removeWaypoint(@NonNull AddWaypointsActionResult result) {
        if (map == null) return;
        map.batchUpdate(() -> {
                for (AddWaypointAction action : result.getActions()) {
                    removeSegment(action.getFirstSegment());
                    removeSegment(action.getSecondSegment());
                    showSegment(map, action.getOriginalSegment(), false);

                    if (segmentHashCodes.size() > 0) {
                        int latestSegmentHashCode =
                            segmentHashCodes.get(segmentHashCodes.size() - 1);
                        SuuntoMarker latestMarker = markers.get(latestSegmentHashCode);
                        if (latestMarker != null) {
                            setEndPointMarker(latestMarker);
                        }
                    }
                }
                return null;
            }
        );
        updateBottomSheetHalfExpandedRatio();
        if (!isAtLeastOneSegmentAvailable()) {
            showRouteTipIfNeeded();
        }
        invalidateOptionsMenu();
    }

    @Override
    public void undoEditWaypoint(@NonNull EditWaypointActionResult result) {
        if (map == null) return;
        map.batchUpdate(() -> {
                for (EditWaypointAction action : result.getActions()) {
                    removeSegment(action.getUpdated());
                    showSegment(map, action.getOriginal(), false);

                    if (segmentHashCodes.size() > 0) {
                        int latestSegmentHashCode =
                            segmentHashCodes.get(segmentHashCodes.size() - 1);
                        SuuntoMarker latestMarker = markers.get(latestSegmentHashCode);
                        if (latestMarker != null) {
                            setEndPointMarker(latestMarker);
                        }
                    }
                }
                return null;
            }
        );
        updateBottomSheetHalfExpandedRatio();
        if (!isAtLeastOneSegmentAvailable()) {
            showRouteTipIfNeeded();
        }
        invalidateOptionsMenu();
    }

    private void removeSegmentHashCodes(int key) {
        for (int i = segmentHashCodes.size() - 1; i >= 0; i--) {
            if (segmentHashCodes.get(i) == key) {
                segmentHashCodes.remove(i);
                break;
            }
        }
    }

    @Override
    public void setMapType(final MapType type) {
        mapFragment.getMapAsync(map ->
            map.setMapType(type.getName(), null)
        );
    }

    @Override
    public void setMapCreditLink(String creditText) {
        mapCredit.setMovementMethod(LinkMovementMethod.getInstance());
        mapCredit.setText(Html.fromHtml(creditText, Html.FROM_HTML_MODE_LEGACY));
        mapCredit.setVisibility(VISIBLE);
        updateMapPadding();
    }

    @Override
    public void hideMapCreditLink() {
        mapCredit.setVisibility(GONE);
        updateMapPadding();
    }

    @Override
    public void setPadding(final int left, final int top, final int right, final int bottom) {
        mapFragment.getMapAsync(
            map -> {
                // left and right get ignored as the values always just match defaults
                MapHelper.updateMapPaddingWithDefaults(
                    getResources(),
                    map,
                    top, // top for paddingTop
                    bottom,
                    top, // top for compassTopMargin
                    0,
                    mapCredit,
                    true
                );
            }
        );
    }

    @Override
    public boolean hasMapSelectionControls() {
        return true;
    }

    @Override
    public boolean heatmapsEnabled() {
        return true;
    }

    @Override
    public boolean myTracksEnabled() {
        return true;
    }

    @Override
    public boolean roadSurfaceEnabled() {
        return true;
    }

    @Override
    public void removeHeatmapOverlay() {
        if (heatmapOverlay != null) {
            heatmapOverlay.remove();
            heatmapOverlay = null;
        }
    }

    @Override
    public void drawMyTracks(
        @NonNull List<RouteAndActivityType> myTracks,
        @NonNull List<LocationWithActivityType> myTrackMarkers) {
        if (map == null) return;
        disposable.add(
            myTracksUtils.drawMyTracksRx(this, map, myTracks, false)
                .andThen(myTracksUtils.drawMyTracksMarkersRx(myTrackMarkers, map, bitmapDescriptorFactory))
                .subscribe(() -> {},
                    e -> Timber.w(e, "Error while drawing my tracks"))
        );
    }

    @Override
    public void removeMyTracks() {
        if (map == null) return;
        myTracksUtils.removePolylines(map);
        myTracksUtils.removeMarkers(map);
    }

    @Override
    public void removeRoadSurfaceOverlay() {
        if (roadSurfaceOverlay != null) {
            roadSurfaceOverlay.remove();
            roadSurfaceOverlay = null;
        }
    }

    @Override
    public void removeStartingPointsOverlay() {
        if (startingPointsOverlay != null) {
            startingPointsOverlay.remove();
            startingPointsOverlay = null;
        }
    }

    @Override
    public void addHeatmapOverlay(SuuntoTileOverlayOptions options) {
        mapFragment.getMapAsync(map -> heatmapOverlay = map.addTileOverlay(options));
    }

    @Override
    public void addRoadSurfaceOverlay(List<SuuntoTileOverlayOptions> options) {
        mapFragment.getMapAsync(map -> roadSurfaceOverlay = map.addTileOverlayGroup(options));
    }

    @Override
    public void addStartingPointsOverlay(SuuntoTileOverlayOptions options) {
        mapFragment.getMapAsync(map -> startingPointsOverlay = map.addTileOverlay(options));
    }

    @Override
    public void onSpeedEntered(double speedInMetersPerSecond) {
        getViewModel().setAvgSpeed(speedInMetersPerSecond);
    }

    @Override
    public void invalidRouteName() {
        showMessage(com.stt.android.R.string.invalid_route_name);
        saveButton.setEnabled(true);
    }

    @Override
    public void savingRoute() {
        // Close the keyboard if there is focus on view
        View view = this.getCurrentFocus();
        hideKeyboard(view);
        savingProgress.setVisibility(VISIBLE);
        saveButton.setEnabled(false);
    }

    private void hideKeyboard(View view) {
        if (view != null) {
            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            inputMethodManager.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    @Override
    public void routeSaved(String name) {
        saveButton.setEnabled(true);
        boolean fromPopularRouteDetail =
            getIntent().getBooleanExtra(KEY_FROM_POPULAR_ROUTE_DETAIL, false);
        if (fromPopularRouteDetail) {
            startActivity(
                LibraryActivity.Companion.newStartIntent(this, LibraryTab.ROUTES, null,
                    TopRouteAction.EDIT)
            );
        } else {
            Toast.makeText(getApplicationContext(), com.stt.android.R.string.route_saved, Toast.LENGTH_LONG).show();
            setResult(RESULT_OK);
        }
        finish();
        startActivity(LibraryActivity.newStartIntent(this, LibraryTab.ROUTES, null, null));
    }

    @Override
    public void addPointFailed(boolean internetConnectionNeeded) {
        showMessage(internetConnectionNeeded ? com.stt.android.R.string.network_disabled_enable : com.stt.android.R.string.error_point_not_added);
    }

    @Override
    public void failedToAddInitialSegment(boolean internetConnectionNeeded) {
        FragmentManager fm = getSupportFragmentManager();

        SimpleDialogFragment sdf = SimpleDialogFragment.newInstance(
            getString(internetConnectionNeeded ?
                com.stt.android.R.string.network_disabled_enable : com.stt.android.R.string.cannot_find_route_message),
            getString(com.stt.android.R.string.cannot_find_route_title),
            getString(com.stt.android.R.string.close)
        );

        sdf.show(fm, ERROR_ADDING_INITIAL_SEGMENT_DIALOG_TAG);
    }

    @Override
    public void movePointFailed() {
        //todo maybe show different error message for move failed
        showMessage(com.stt.android.R.string.error_point_not_added);
        mapFragment.getMapAsync(googleMap -> undoMarkerDrag());
    }

    @SuppressWarnings("WeakerAccess")
    // Used in inner classes
    void undoMarkerDrag() {
        SuuntoMarker marker = markers.get(movedMarkerSegmentHash);
        if (marker == null) {
            return;
        }
        //move marker back to its original position if marker is still available
        LatLng originalPosition = markerOriginalPositions.get(marker.hashCode());
        if (originalPosition != null) {
            marker.setPosition(originalPosition);
        }

        //if moved marker was start point we need to set original position in model also
        if (movedMarkerSegmentHash == START_POINT_SEGMENT) {
            getViewModel().setStartingPointLocation(originalPosition);
        }
    }

    @Override
    public void onAnnotationDragStart(@NonNull SuuntoMarker marker) {
        disableClickHandlers();
        // cache moved marker segment hash in case of moving error
        movedMarkerSegmentHash = segmentsByEndMarker.get(marker.hashCode());
    }

    @Override
    public void onAnnotationDrag(@NonNull SuuntoMarker marker) {
        // With Mapbox, long tapping on top of an existing marker also cause dragging events for
        // that marker. Let's hide Add to route selection dialog if it is actually dragged.
        hideAddToRouteSelectionDialog();
        wasAnnotationDragged = true;
    }

    @Override
    public void onAnnotationDragEnd(@NonNull SuuntoMarker marker) {
        if (wasAnnotationDragged) {
            onFetchingRouteStarted();
            int segmentHash = segmentsByEndMarker.get(marker.hashCode());
            LatLng position = marker.getPosition();
            if (position != null) {
                if (segmentHash == START_POINT_SEGMENT) {
                    getViewModel().moveStartPointTo(position.latitude, position.longitude);
                } else {
                    getViewModel().moveSegmentEndPoint(segmentHash, position.latitude, position.longitude, marker.isWaypoint());
                }
            }
        } else {
            // Restore marker position from before drag started
            LatLng origPos = markerOriginalPositions.get(marker.hashCode());
            if (origPos != null) {
                marker.setPosition(origPos);
            }

            enableClickHandlers(); // Re-enable map interactions
        }
        wasAnnotationDragged = false;
        lastAnnotationDragEndMillis = System.currentTimeMillis();
    }

    @Override
    public void routingModeSelected(@NonNull RoutingMode mode, Boolean isNavigate) {
        getViewModel().routingMode(mode);
    }

    void showRouteTipIfNeeded() {
        if (routeTipBottomSheetManager != null) {
            routeTipBottomSheetManager.showRouteTipIfNeeded(hasAtLeastOneSegment(), hasStartPoint());
        }

        boolean routeTipVisible = routeTipBottomSheetManager != null && routeTipBottomSheetManager.isTipVisible();
        if (needTips && !routeTipVisible && !hasAtLeastOneSegment()) {
            routeInfoGroup.setVisibility(GONE);
            loadingRouteProgress.setVisibility(GONE);
            routeAltitudeChartWithAxis.setVisibility(GONE);
            tapTheMapTip.setVisibility(GONE);
        }
    }

    void hideRouteTipIfNeeded() {
        routeInfoGroup.setVisibility(VISIBLE);
        if (routeTipBottomSheetManager != null) {
            routeTipBottomSheetManager.hideRouteTipIfNeeded();
            getViewModel().onAscentInfoVisibilityRequested();
        }
    }

    @Override
    public void activitiesChanged(List<ActivityType> newActivities) {
        showSelectedActivities(newActivities);
        saveButton.setEnabled(!newActivities.isEmpty());
    }

    @Override
    public void resetState() {
        polylines.clear();
        markers.clear();
        markerOriginalPositions.clear();
        segmentsByEndMarker.clear();
    }

    private void setActionBarTitle(String title) {
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setTitle(title);
            actionBar.setDisplayShowTitleEnabled(true);
        }
    }

    protected void setActionBarSubtitle(String subtitle) {
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            SpannableString spannableTitle = new SpannableString(subtitle);
            spannableTitle.setSpan(
                new ForegroundColorSpan(getColor(com.stt.android.core.R.color.near_black)),
                0,
                spannableTitle.length(),
                Spannable.SPAN_INCLUSIVE_INCLUSIVE
            );

            spannableTitle.setSpan(new RelativeSizeSpan(0.8f), 0, spannableTitle.length(),
                Spannable.SPAN_INCLUSIVE_INCLUSIVE);
            actionBar.setSubtitle(spannableTitle);
        }
    }

    @Override
    public void creatingNewRoute() {
        if (isNavigate) {
            setActionBarTitle(getString(com.stt.android.R.string.title_activity_navigate_on_watch));
        } else {
            setActionBarTitle(getString(com.stt.android.R.string.title_activity_new_route));
        }
    }

    @Override
    public void creatingCopyRoute() {
        setActionBarTitle(getString(com.stt.android.R.string.title_activity_create_a_copy));
    }

    @Override
    public void editingRoute(String routeName) {
        setActionBarTitle(routeName);
    }

    @Override
    public void topRouteEditing() {
        setActionBarTitle(getString(com.stt.android.R.string.title_activity_edit_top_route));
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
        @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        // Forward results to EasyPermissions
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this);
    }

    @Override
    public void onPermissionsGranted(int requestCode, @NonNull List<String> perms) {
        mapFragment.getMapAsync(map -> {
            map.setLocationSource(getViewModel().getLocationSource());
            map.setMyLocationEnabled(true);
        });
        getViewModel().requestLocationUpdates();
    }

    @Override
    public void onPermissionsDenied(int requestCode, @NonNull List<String> perms) {
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        removeProgressCallbacks();
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        removeProgressCallbacks();
    }

    private void removeProgressCallbacks() {
        removeProgressDelayedHideCallback();
        removeProgressDelayedShowCallback();
    }

    private void removeProgressDelayedHideCallback() {
        loadingRouteProgress.removeCallbacks(progressDelayedHide);
        progressPostedHide = false;
    }

    private void removeProgressDelayedShowCallback() {
        loadingRouteProgress.removeCallbacks(progressDelayedShow);
        progressPostedShow = false;
    }

    private void setMapVisibility(int visible) {
        if (!isFinishing() && mapContainer != null) {
            mapContainer.setVisibility(visible);
        }
    }

    @Override
    public boolean hasAnyLocationPermission() {
        boolean fineLocationPermission =
            ContextExtensionsKt.hasPermission(this, Manifest.permission.ACCESS_FINE_LOCATION);
        boolean coarseLocationPermission =
            ContextExtensionsKt.hasPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION);
        return fineLocationPermission || coarseLocationPermission;
    }

    @Override
    public boolean hasLocationPermissions() {
        return EasyPermissions.hasPermissions(this, PermissionUtils.LOCATION_PERMISSIONS);
    }

    @Override
    public MapType getCurrentMapType() {
        return mapPresenter.getCurrentMapType();
    }

    @Override
    public HeatmapType getCurrentHeatmapType() {
        return mapPresenter.getCurrentHeatmapType();
    }

    @Override
    public List<RoadSurfaceType> getCurrentRoadSurfaceTypes() {
        return mapPresenter.getCurrentRoadSurfaceTypes();
    }

    @Override
    public boolean getHideCyclingForbiddenRoads() {
        return mapPresenter.getHideCyclingForbiddenRoads();
    }

    @Override
    public void resetEditViewsVisibleForRouteCreation() {
        if (isSaveStateInNavigation()) {
            routeAltitudeChartWithAxis.getViewTreeObserver().addOnGlobalLayoutListener(
                new ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        routeAltitudeChartWithAxis.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                        setNavigationEditState();
                    }
                }
            );
        } else {
            setRouteNamingState(isEditState);
        }
    }

    private void setRouteNamingState(boolean isVisible) {
        setupRouteInputName(isVisible, true);
        setEditViewsVisible(isVisible);
    }

    private void setBottomSheetPaddingAndCollapsed() {
        OneShotPreDrawListener.add(bottomSheetView, () -> {
            int floatingLayerHeight = bottomSheetFloatingLayer.getHeight();
            bottomSheetView.setPadding(
                bottomSheetView.getPaddingLeft(),
                bottomSheetView.getPaddingTop(),
                bottomSheetView.getPaddingRight(),
                floatingLayerHeight
            );
            bottomSheetView.requestLayout();

            int peekHeight = floatingLayerHeight + topSummary.getHeight() + dragHandle.getHeight();
            bottomSheetBehavior.setPeekHeight(peekHeight);
            float newHalfExpandedRatio = calculateRatioFromHeight(rootView.getHeight(), peekHeight);
            bottomSheetBehavior.setHalfExpandedRatio(newHalfExpandedRatio);
            bottomSheetBehavior.setHideable(false);
            bottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
        });
    }

    protected String getMapsProviderName() {
        String mapsProviderName = "";

        if (map != null) {
            mapsProviderName = map.getProviderName();
        } else if (mapFragment != null && mapFragment.getMapView() != null) {
            mapsProviderName = mapFragment.getMapView().getProviderName();
        } else if (SuuntoMaps.INSTANCE.getDefaultProvider() != null) {
            mapsProviderName = SuuntoMaps.INSTANCE.getDefaultProvider().getName();
        }

        return mapsProviderName;
    }

    //region SimpleDialogFragment.Callback
    @Override
    public void onDialogButtonPressed(@Nullable String tag, int which) {
        if (LOCATION_PERMISSION_DIALOG_TAG.equals(tag)) {
            if (which == DialogInterface.BUTTON_POSITIVE) {
                ContextExtensionsKt.openAppSettings(this);
            }
        } else if (ROUTE_IMPORT_ERROR_DIALOG.equals(tag)) {
            finish();
        }
    }

    @Override
    public void onDialogDismissed(@Nullable String tag) {
        if (!WAYPOINT_MAX_COUNT_DIALOG_TAG.equals(tag) &&
            !WAYPOINT_LIMITED_SUPPORT_DIALOG_TAG.equals(tag) &&
            !TURN_BY_TURN_MAX_COUNT_DIALOG_TAG.equals(tag) &&
            !LOCATION_PERMISSION_DIALOG_TAG.equals(tag) &&
            !WITHOUT_GPS_PROMPT_DIALOG.equals(tag)
            ) {
            finish();
        }
    }

    @Override
    public void onReverseRouteClicked() {
        getViewModel().reverseRoute();
    }

    @Override
    public void onCloseRouteClicked() {
        getViewModel().closeRoute();
    }

    @Override
    public void onBackTraceClicked() {
        getViewModel().backTraceToStart();
    }

    //endregion
    @Override
    public void highlightRouteByIndex(ClimbGuidance climbGuidance, int segmentIndex,
        int pointIndex) {
    }

    @Override
    public void highlightRouteByIndex(List<RouteSegment> routeSegments, int segmentIndex,
        int pointIndex) {
        clearHighlightPolylines();
        for (int i = 0; i < routeSegments.size(); i++) {
            RouteSegment segment = routeSegments.get(i);
            SuuntoPolyline polyline = polylines.get(segment.hashCodeIgnorePosition());
            if (polyline != null) {
                if (i == segmentIndex) {
                    polyline.setVisible(false);
                    mapFragment.getMapAsync(map -> {
                        List<Point> points = segment.getRoutePoints();
                        List<Point> highlightPoints = points.subList(0, pointIndex + 1);
                        if (highlightPoints.size() >= 2) {
                            SuuntoPolyline polyline1 = addPolyline(map, highlightPoints);
                            highlightPolylines.add(polyline1);
                        }
                        List<Point> translucentPoints = points.subList(pointIndex, points.size());
                        if (translucentPoints.size() >= 2) {
                            SuuntoPolyline polyline2 = addPolyline(map, translucentPoints);
                            highlightPolylines.add(polyline2);
                            polyline2.setColor(translucentColor(polyline2.getColor()));
                        }
                    });
                } else if (i > segmentIndex) {
                    polyline.setVisible(true);
                    polyline.setColor(translucentColor(polyline.getColor()));
                } else {
                    polyline.setVisible(true);
                    polyline.setColor(solidColor(polyline.getColor()));
                }
            }
        }
    }

    @Override
    public void resetRouteToDefault(List<RouteSegment> routeSegments) {
        clearHighlightPolylines();
        for (int i = 0; i < routeSegments.size(); i++) {
            RouteSegment segment = routeSegments.get(i);
            SuuntoPolyline polyline = polylines.get(segment.hashCodeIgnorePosition());
            if (polyline != null) {
                polyline.setVisible(true);
                polyline.setColor(solidColor(polyline.getColor()));
            }
        }
    }

    private void clearHighlightPolylines() {
        CollectionsKt.forEach(highlightPolylines, polyline -> {
            polyline.remove();
            return null;
        });
        highlightPolylines.clear();
    }

    private int solidColor(int color) {
        return Color.argb(
            255,
            Color.red(color),
            Color.green(color),
            Color.blue(color)
        );
    }

    private int translucentColor(int color) {
        return Color.argb(
            (int) (255 * 0.3f),
            Color.red(color),
            Color.green(color),
            Color.blue(color)
        );
    }
}
