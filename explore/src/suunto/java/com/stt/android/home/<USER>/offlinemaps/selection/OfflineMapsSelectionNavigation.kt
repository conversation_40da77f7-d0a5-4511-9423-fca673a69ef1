package com.stt.android.home.explore.offlinemaps.selection

import androidx.activity.ComponentActivity
import androidx.activity.compose.LocalActivity
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.stt.android.home.explore.offlinemaps.selection.search.OfflineMapsSearchViewModel
import com.stt.android.home.explore.offlinemaps.selection.ui.LinkTarget
import com.stt.android.home.explore.offlinemaps.selection.ui.OfflineRegionPreviewScreen
import com.stt.android.home.explore.offlinemaps.selection.ui.OfflineRegionsScreen
import com.stt.android.home.explore.offlinemaps.selection.watchstatus.WatchDownloadStatusViewModel
import com.stt.android.maps.SuuntoChinaOfflineRegion
import com.stt.android.maps.SuuntoMarker
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import kotlinx.collections.immutable.toImmutableList

internal object OfflineMapsSelectionNavigation {
    const val OFFLINE_REGION_GROUPS_ROUTE: String = "OFFLINE_REGION_GROUPS"
    const val OFFLINE_REGIONS_ROUTE: String = "OFFLINE_REGIONS"
    const val OFFLINE_REGION_PREVIEW_ROUTE: String = "OFFLINE_REGION_PREVIEW"
}

internal fun NavGraphBuilder.buildNavigationGraph(
    navController: NavController,

    onClose: (userStartedRegionDownload: Boolean) -> Unit,
    openLibrary: (downloadedRegionCount: Int, scrollToRegionId: String?) -> Unit,
    openWifiSetup: (toAddNetwork: Boolean) -> Unit,
    openOsmDisclaimer: () -> Unit,
    onDownloadInfoLink: (LinkTarget) -> Unit,
) {
    composable(
        route = OfflineMapsSelectionNavigation.OFFLINE_REGION_GROUPS_ROUTE,
    ) {
        val offlineMapsSelectionViewModel = hiltViewModel<OfflineMapsSelectionViewModel>(
            viewModelStoreOwner = LocalActivity.current as ComponentActivity,
        )
        val offlineMapsSelectionViewState by offlineMapsSelectionViewModel.viewState.collectAsState()
        offlineMapsSelectionViewModel.navAction
            .navigateOnResumed { action -> with(action) { navController.navigate() } }

        val searchViewModel = hiltViewModel<OfflineMapsSearchViewModel>()
        val searchViewState by searchViewModel.viewState.collectAsState()

        val watchDownloadStatusViewModel = hiltViewModel<WatchDownloadStatusViewModel>(
            viewModelStoreOwner = LocalActivity.current as ComponentActivity,
        )
        val watchDownloadStatus by watchDownloadStatusViewModel.watchDownloadStatus.collectAsState()

        OfflineRegionsScreen(
            searchViewState = searchViewState,
            searchEventHandler = searchViewModel::handleEvent,
            offlineMapsSelectionViewState = offlineMapsSelectionViewState,
            offlineMapsSelectionEventHandler = offlineMapsSelectionViewModel::handleEvent,
            listData = offlineMapsSelectionViewModel.offlineRegionCatalogue,
            watchDownloadStatus = watchDownloadStatus,
            onViewInLibrary = { region ->
                openLibrary(
                    offlineMapsSelectionViewModel.offlineRegionCatalogue.groups
                        .flatMap { it.regions }
                        .count { it.downloaded },
                    region?.id,
                )
            },
            onEnableWifi = { openWifiSetup(false) },
            onWifiSetup = { openWifiSetup(true) },
            onDownloadInfoLink = onDownloadInfoLink,
            navigateUp = { onClose(offlineMapsSelectionViewModel.userStartedRegionDownload) },
        )
    }

    composable(OfflineMapsSelectionNavigation.OFFLINE_REGIONS_ROUTE) {
        val offlineMapsSelectionViewModel = hiltViewModel<OfflineMapsSelectionViewModel>(
            viewModelStoreOwner = LocalActivity.current as ComponentActivity,
        )

        val offlineMapsSelectionViewState by offlineMapsSelectionViewModel.viewState.collectAsState()
        val selectedRegionGroup = (offlineMapsSelectionViewState as? OfflineMapsSelectionViewState.Loaded)
            ?.selectedRegionGroup

        offlineMapsSelectionViewModel.navAction
            .navigateOnResumed { action -> with(action) { navController.navigate() } }

        val searchViewModel = hiltViewModel<OfflineMapsSearchViewModel>()
        val searchViewState by searchViewModel.viewState.collectAsState()

        val watchDownloadStatusViewModel = hiltViewModel<WatchDownloadStatusViewModel>(
            viewModelStoreOwner = LocalActivity.current as ComponentActivity,
        )
        val watchDownloadStatus by watchDownloadStatusViewModel.watchDownloadStatus.collectAsState()

        OfflineRegionsScreen(
            searchViewState = searchViewState,
            searchEventHandler = searchViewModel::handleEvent,
            offlineMapsSelectionViewState = offlineMapsSelectionViewState,
            offlineMapsSelectionEventHandler = offlineMapsSelectionViewModel::handleEvent,
            listData = OfflineRegionListData.Group(requireNotNull(selectedRegionGroup)),
            watchDownloadStatus = watchDownloadStatus,
            onViewInLibrary = { region ->
                openLibrary(
                    offlineMapsSelectionViewModel.offlineRegionCatalogue.groups
                        .flatMap { it.regions }
                        .count { it.downloaded },
                    region?.id,
                )
            },
            onEnableWifi = { openWifiSetup(false) },
            onWifiSetup = { openWifiSetup(true) },
            onDownloadInfoLink = onDownloadInfoLink,
            navigateUp = { offlineMapsSelectionViewModel.handleEvent(OfflineMapsSelectionViewEvent.NavigateUp) }
        )
    }

    composable(OfflineMapsSelectionNavigation.OFFLINE_REGION_PREVIEW_ROUTE) {
        val offlineMapsSelectionViewModel = hiltViewModel<OfflineMapsSelectionViewModel>(
            viewModelStoreOwner = LocalActivity.current as ComponentActivity,
        )

        val offlineMapsSelectionViewState by offlineMapsSelectionViewModel.viewState.collectAsState()
        val loadedState = (offlineMapsSelectionViewState as? OfflineMapsSelectionViewState.Loaded)
            ?: throw IllegalStateException("Offline region preview should only be shown when regions are loaded")

        offlineMapsSelectionViewModel.navAction
            .navigateOnResumed { action -> with(action) { navController.navigate() } }

        OfflineRegionPreviewScreen(
            region = requireNotNull(loadedState.selectedRegion),
            supportsOfflineMapsOnMobile = loadedState.supportsOfflineMapsOnMobile,
            selectedDownloadTargets = loadedState.selectedDownloadTargets,
            onDownloadTargetSelectionChanged = { target, selected ->
                val event = if (selected) {
                    OfflineMapsSelectionViewEvent.SelectDownloadTarget(target)
                } else {
                    OfflineMapsSelectionViewEvent.DeselectDownloadTarget(target)
                }
                offlineMapsSelectionViewModel.handleEvent(event)
            },
            onDownloadRegion = {
                offlineMapsSelectionViewModel.handleEvent(
                    event = OfflineMapsSelectionViewEvent.DownloadOfflineRegion(it.id),
                )
                offlineMapsSelectionViewModel.handleEvent(OfflineMapsSelectionViewEvent.NavigateUp)
            },
            onOsmDisclaimer = openOsmDisclaimer,
            onDeleteRegion = { /* Not supported here */ },
            navigateUp = { offlineMapsSelectionViewModel.handleEvent(OfflineMapsSelectionViewEvent.NavigateUp) },
            onSelectRegion = { id ->
                offlineMapsSelectionViewModel.selectChinaRegion(navController.context, id)
            },
            allChinaRegionsAndSelectState = offlineMapsSelectionViewModel.allChinaRegionsAndSelectState.toImmutableList(),
            selectedChinaRegions = offlineMapsSelectionViewModel.selectedChinaDownloadRegions.toImmutableList(),
            onLoadAllChinaOfflineRegionSuccess = { suuntoChinaOfflineRegions: List<SuuntoChinaOfflineRegion>, suuntoMarkers: List<SuuntoMarker> ->
                offlineMapsSelectionViewModel.chinaOfflineRegionOverlays = suuntoChinaOfflineRegions
                offlineMapsSelectionViewModel.chinaRegionDownloadStateMarkers = suuntoMarkers
            },
            watchStorageFull = offlineMapsSelectionViewModel.watchStorageFullState.value,
            onWatchStorageFullDialogDismissRequest = {
                offlineMapsSelectionViewModel.watchStorageFullState.value = false
            },
            onMapScaleListener = {
                offlineMapsSelectionViewModel.scaleMarkers(it)
            }
        )
    }
}
