package com.stt.android.home.explore.offlinemaps.datasource

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.offlinemaps.entity.DownloadOrder
import com.stt.android.offlinemaps.entity.OfflineArea
import com.stt.android.offlinemaps.entity.OfflineAreaSource
import com.stt.android.offlinemaps.entity.OfflineAreaSourceTileType
import com.stt.android.offlinemaps.entity.OfflineAreaType
import com.stt.android.offlinemaps.entity.OfflineMapDownloadTarget
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.entity.OfflineRegionSearchResult
import com.stt.android.offlinemaps.entity.OfflineRegionSearchResultType
import com.stt.android.offlinemaps.entity.OfflineRegionStatus
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.collections.immutable.toPersistentList
import com.stt.android.core.R as CR

@JsonClass(generateAdapter = true)
internal data class RemoteDownloadOrder(
    @field:Json(name = "deviceSerialNumber")
    val deviceSerialNumber: String,
    @field:Json(name = "downloadCompletedAt")
    val downloadCompletedAt: Long?,
    @field:Json(name = "status")
    val status: RemoteOfflineRegionStatus,
    @field:Json(name = "downloadedSize")
    val downloadedSize: Long?,
    @field:Json(name = "sourceTypeUsed")
    val sourceTileType: RemoteOfflineAreaSourceTileType?,
) {
    fun toDownloaderOrder(): DownloadOrder = DownloadOrder(
        deviceSerialNumber = deviceSerialNumber,
        downloadCompletedAt = downloadCompletedAt,
        status = status.toOfflineRegionStatus(),
        downloadedSize = downloadedSize,
        sourceTileType = when (sourceTileType) {
            RemoteOfflineAreaSourceTileType.SMTF_V1_GZ -> OfflineAreaSourceTileType.SMTF_V1_GZ
            RemoteOfflineAreaSourceTileType.SMTF_V1 -> OfflineAreaSourceTileType.SMTF_V1
            null -> null
        },
    )
}

@JsonClass(generateAdapter = false)
internal enum class RemoteOfflineAreaSourceTileType {
    @field:Json(name = "SMTF_V1_GZ")
    SMTF_V1_GZ,

    @field:Json(name = "SMTF_V1")
    SMTF_V1,
}

@JsonClass(generateAdapter = false)
internal enum class RemoteOfflineRegionStatus {
    @field:Json(name = "REQUESTED")
    REQUESTED,

    @field:Json(name = "IN_PROGRESS")
    IN_PROGRESS,

    @field:Json(name = "FINISHED")
    FINISHED,

    @field:Json(name = "DELETE_REQUESTED")
    DELETE_REQUESTED,

    @field:Json(name = "DELETE_FINISHED")
    DELETE_FINISHED,

    @field:Json(name = "UPDATE_AVAILABLE")
    UPDATE_AVAILABLE,

    @field:Json(name = "UPDATE_IN_PROGRESS")
    UPDATE_IN_PROGRESS,

    @field:Json(name = "FAILED")
    FAILED,
    ;

    fun toOfflineRegionStatus(): OfflineRegionStatus = when (this) {
        REQUESTED -> OfflineRegionStatus.REQUESTED
        IN_PROGRESS -> OfflineRegionStatus.IN_PROGRESS
        FINISHED -> OfflineRegionStatus.FINISHED
        DELETE_REQUESTED -> OfflineRegionStatus.DELETE_REQUESTED
        DELETE_FINISHED -> OfflineRegionStatus.DELETE_FINISHED
        UPDATE_AVAILABLE -> OfflineRegionStatus.UPDATE_AVAILABLE
        UPDATE_IN_PROGRESS -> OfflineRegionStatus.UPDATE_IN_PROGRESS
        FAILED -> OfflineRegionStatus.FAILED
    }

    companion object {
        fun from(status: OfflineRegionStatus): RemoteOfflineRegionStatus = when (status) {
            OfflineRegionStatus.REQUESTED -> REQUESTED
            OfflineRegionStatus.IN_PROGRESS -> IN_PROGRESS
            OfflineRegionStatus.FINISHED -> FINISHED
            OfflineRegionStatus.DELETE_REQUESTED -> DELETE_REQUESTED
            OfflineRegionStatus.DELETE_FINISHED -> DELETE_FINISHED
            OfflineRegionStatus.UPDATE_AVAILABLE -> UPDATE_AVAILABLE
            OfflineRegionStatus.UPDATE_IN_PROGRESS -> UPDATE_IN_PROGRESS
            OfflineRegionStatus.FAILED -> FAILED
        }
    }
}

@JsonClass(generateAdapter = true)
internal data class RemoteOfflineRegionCatalogue(
    @field:Json(name = "nearbyRegions")
    val nearbyRegions: List<RemoteNearby>,
    @field:Json(name = "groups")
    val groups: List<RemoteOfflineRegionGroup>,
    @field:Json(name = "regions")
    val regions: List<RemoteOfflineRegion>,
)

@JsonClass(generateAdapter = true)
internal data class RemoteOfflineRegionGroup(
    @field:Json(name = "id")
    val id: String,
    @field:Json(name = "name")
    val name: String,
    @field:Json(name = "regions")
    val regions: List<RemoteId>,
    @field:Json(name = "maskUrl")
    val maskUrl: String?,
    @field:Json(name = "batchDownloadAllowed")
    val batchDownloadAllowed: Boolean?,
)

@JsonClass(generateAdapter = true)
internal data class RemoteNearby(
    @field:Json(name = "id")
    val id: String,
    @field:Json(name = "type")
    val type: RemoteSearchResultType?,
)

@JsonClass(generateAdapter = true)
internal data class RemoteId(
    @field:Json(name = "id")
    val id: String,
)

@JsonClass(generateAdapter = true)
internal data class RemoteOfflineRegion(
    @field:Json(name = "id")
    val id: String,
    @field:Json(name = "name")
    val name: String,
    @field:Json(name = "area")
    val area: Double?,
    @field:Json(name = "sources")
    val sources: List<RemoteOfflineRegionSource>,
    @field:Json(name = "description")
    val description: String?,
    @field:Json(name = "boundaryUrl")
    val boundaryUrl: String?,
    @field:Json(name = "maskUrl")
    val maskUrl: String?,
    @field:Json(name = "bbox")
    val bounds: RemoteBounds?,
    @field:Json(name = "downloadOrders")
    val downloadOrders: List<RemoteDownloadOrder>?,
    @field:Json(name = "styles")
    val styles: List<RemoteId> = emptyList(),
    @field:Json(name = "adjacentRegions")
    val adjacentRegions: List<RemoteId> = emptyList(),
    @field:Json(name = "adjacentMaskUrl")
    val adjacentMaskUrl: String?,
    @field:Json(name = "centerPoint")
    val centerPoint: RemoteLatLng?,
) {
    val storageSizeForWatch: Long
        get() = sources.firstOrNull { it.watchPreferred == true }?.storageSize
            ?: sources.firstOrNull { isSourceTileTypeForWatch(it.type) }?.storageSize
            ?: sources.firstOrNull()?.storageSize
            ?: 0L

    val transferSizeForWatch: Long
        get() = sources.firstOrNull { it.watchPreferred == true }?.transferSize
            ?: sources.firstOrNull { isSourceTileTypeForWatch(it.type) }?.transferSize
            ?: sources.firstOrNull()?.transferSize
            ?: 0L

    val storageSizeForMobile: Long
        get() = sources.firstOrNull { isSourceTileTypeForMobile(it.type) }?.storageSize
            ?: 0L

    val transferSizeForMobile: Long
        get() = sources.firstOrNull { isSourceTileTypeForMobile(it.type) }?.transferSize
            ?: 0L

    fun toOfflineRegion(
        deviceSerials: Set<String>,
        unit: MeasurementUnit,
        groupName: String? = null,
        batchDownloadAllowed: Boolean? = null,
        groupMaskUrl: String? = null
    ): OfflineRegionResult.OfflineRegion {
        val (value, unitRes) = if (unit == MeasurementUnit.IMPERIAL) {
            squareKmToSquareMi(area ?: 0.0) to CR.string.square_miles
        } else {
            area to CR.string.square_kilometers
        }
        return OfflineRegionResult.OfflineRegion(
            id = id,
            name = name,
            sizes = persistentMapOf(
                OfflineMapDownloadTarget.WATCH to OfflineRegionResult.OfflineRegion.Size(
                    storageSizeInBytes = storageSizeForWatch,
                    transferSizeInBytes = transferSizeForWatch,
                ),
                OfflineMapDownloadTarget.MOBILE to OfflineRegionResult.OfflineRegion.Size(
                    storageSizeInBytes = storageSizeForMobile,
                    transferSizeInBytes = transferSizeForMobile,
                ),
            ),
            area = value,
            areaUnitRes = unitRes,
            description = description,
            bounds = bounds?.let {
                LatLngBounds(
                    LatLng(bounds.southwest.latitude, bounds.southwest.longitude),
                    LatLng(bounds.northeast.latitude, bounds.northeast.longitude),
                )
            },
            boundaryUrl = boundaryUrl,
            maskUrl = if (batchDownloadAllowed == true) {
                groupMaskUrl
            } else {
                maskUrl
            },
            downloadOrders = downloadOrders
                ?.mapNotNull { downloadOrder ->
                    if (downloadOrder.deviceSerialNumber in deviceSerials) {
                        downloadOrder.toDownloaderOrder()
                    } else {
                        null
                    }
                }
                ?.toPersistentList()
                ?: persistentListOf(),
            styleIds = styles.map { it.id }.toPersistentList(),
            groupName = groupName,
            adjacentRegions = adjacentRegions.map(RemoteId::id).toImmutableList(),
            adjacentMaskUrl = adjacentMaskUrl,
            centerPoint = if (centerPoint != null) {
                LatLng(centerPoint.latitude, centerPoint.longitude)
            } else {
                null
            },
            batchDownloadAllowed = batchDownloadAllowed
        )
    }
}

private const val SMTF_V1_GZ: String = "SMTF_V1_GZ"
private const val SMTF_V1: String = "SMTF_V1"

private fun isSourceTileTypeForMobile(type: String?): Boolean = type == SMTF_V1_GZ

private fun isSourceTileTypeForWatch(type: String?): Boolean = type == SMTF_V1

@JsonClass(generateAdapter = true)
internal data class RemoteOfflineAreaSource(
    @field:Json(name = "type") val type: String,
    @field:Json(name = "storageSize") val storageSize: Long,
    @field:Json(name = "transferSize") val transferSize: Long,
    @field:Json(name = "baseUrl") val baseUrl: String,
    @field:Json(name = "paths") val paths: List<String>,
) {
    fun toOfflineAreaSource(): OfflineAreaSource? {
        return OfflineAreaSource(
            type = when (type) {
                SMTF_V1_GZ -> OfflineAreaSourceTileType.SMTF_V1_GZ
                SMTF_V1 -> OfflineAreaSourceTileType.SMTF_V1
                else -> return null
            },
            storageSize = storageSize,
            transferSize = transferSize,
            baseUrl = baseUrl,
            paths = paths,
        )
    }
}

internal fun OfflineAreaSourceTileType.toRemoteValue(): String = when (this) {
    OfflineAreaSourceTileType.SMTF_V1_GZ -> SMTF_V1_GZ
    OfflineAreaSourceTileType.SMTF_V1 -> SMTF_V1
}

@JsonClass(generateAdapter = false)
internal enum class RemoteOfflineAreaType {
    @field:Json(name = "BASELAYER")
    BASELAYER,

    @field:Json(name = "REGION")
    REGION,
}

@JsonClass(generateAdapter = true)
internal data class RemoteOfflineArea(
    @field:Json(name = "id") val id: String,
    @field:Json(name = "name") val name: String,
    @field:Json(name = "status") val status: RemoteOfflineRegionStatus,
    @field:Json(name = "lastModifiedDate") val lastModifiedDate: Long,
    @field:Json(name = "sources") val sources: List<RemoteOfflineAreaSource>,
    @field:Json(name = "type") val type: RemoteOfflineAreaType,
) {
    fun toOfflineArea(): OfflineArea = OfflineArea(
        id = id,
        name = name,
        status = status.toOfflineRegionStatus(),
        lastModifiedDate = lastModifiedDate,
        sources = sources.mapNotNull { it.toOfflineAreaSource() },
        type = when (type) {
            RemoteOfflineAreaType.BASELAYER -> OfflineAreaType.BASELAYER
            RemoteOfflineAreaType.REGION -> OfflineAreaType.REGION
        },
    )
}

@JsonClass(generateAdapter = true)
internal data class RemoteBounds(
    @field:Json(name = "southwest")
    val southwest: RemoteLatLng,
    @field:Json(name = "northeast")
    val northeast: RemoteLatLng,
)

@JsonClass(generateAdapter = true)
internal data class RemoteLatLng(
    @field:Json(name = "latitude")
    val latitude: Double,
    @field:Json(name = "longitude")
    val longitude: Double,
)

@JsonClass(generateAdapter = true)
internal data class RemoteOfflineRegionSource(
    @field:Json(name = "type")
    val type: String?,
    @field:Json(name = "storageSize")
    val storageSize: Long?,
    @field:Json(name = "transferSize")
    val transferSize: Long?,
    @field:Json(name = "watchPreferred")
    val watchPreferred: Boolean?,
)

@JsonClass(generateAdapter = true)
internal data class RemoteLibraryOfflineRegion(
    @field:Json(name = "id")
    val id: String,
    @field:Json(name = "name")
    val name: String,
    @field:Json(name = "description")
    val description: String?,
    @field:Json(name = "area")
    val area: Double?,
    @field:Json(name = "bbox")
    val bounds: RemoteBounds?,
    @field:Json(name = "boundaryUrl")
    val boundaryUrl: String?,
    @field:Json(name = "downloadedSize")
    val downloadedSize: Long?,
    @field:Json(name = "downloadCompletedAt")
    val downloadCompletedAt: Long?,
    @field:Json(name = "lastModifiedDate")
    val lastModifiedDate: Long,
    @field:Json(name = "maskUrl")
    val maskUrl: String?,
    @field:Json(name = "storageSize")
    val storageSize: Long?,
    @field:Json(name = "transferSize")
    val transferSize: Long?,
    @field:Json(name = "status")
    val status: RemoteOfflineRegionStatus,
    @field:Json(name = "styles")
    val styles: List<RemoteId> = emptyList(),
) {
    fun toOfflineRegion(
        downloadTarget: OfflineMapDownloadTarget,
        deviceSerial: String,
        unit: MeasurementUnit
    ): OfflineRegionResult.OfflineRegion {
        val (value, unitRes) = if (unit == MeasurementUnit.IMPERIAL) {
            squareKmToSquareMi(area ?: 0.0) to CR.string.square_miles
        } else {
            area to CR.string.square_kilometers
        }
        return OfflineRegionResult.OfflineRegion(
            id = id,
            name = name,
            sizes = persistentMapOf(
                downloadTarget to OfflineRegionResult.OfflineRegion.Size(
                    storageSizeInBytes = storageSize,
                    transferSizeInBytes = transferSize,
                ),
            ),
            area = value,
            areaUnitRes = unitRes,
            description = description,
            bounds = if (bounds != null) {
                LatLngBounds(
                    LatLng(bounds.southwest.latitude, bounds.southwest.longitude),
                    LatLng(bounds.northeast.latitude, bounds.northeast.longitude)
                )
            } else {
                null
            },
            boundaryUrl = boundaryUrl,
            maskUrl = maskUrl,
            downloadOrders = persistentListOf(
                DownloadOrder(
                    deviceSerialNumber = deviceSerial,
                    downloadCompletedAt = downloadCompletedAt,
                    status = status.toOfflineRegionStatus(),
                    downloadedSize = downloadedSize,
                    sourceTileType = when (downloadTarget) {
                        OfflineMapDownloadTarget.MOBILE -> OfflineAreaSourceTileType.SMTF_V1_GZ
                        OfflineMapDownloadTarget.WATCH -> OfflineAreaSourceTileType.SMTF_V1
                    },
                ),
            ),
            styleIds = styles.map { it.id }.toPersistentList(),
        )
    }
}

private fun squareKmToSquareMi(squareKm: Double): Double = squareKm / 2.589988

@JsonClass(generateAdapter = true)
internal data class DownloadOrderRequest(
    @field:Json(name = "deviceSerialNumber")
    val deviceSerialNumber: String,
    @field:Json(name = "regionId")
    val regionId: String,
    @field:Json(name = "watchCapabilities")
    val capabilities: String?,
)

@JsonClass(generateAdapter = true)
internal data class RemoteSearchResults(
    @field:Json(name = "results")
    val results: List<RemoteSearchResult>,
)

@JsonClass(generateAdapter = true)
internal data class RemoteSearchResult(
    @field:Json(name = "highlightResult")
    val highlightResult: String,
    @field:Json(name = "id")
    val id: String,
    @field:Json(name = "name")
    val name: String,
    @field:Json(name = "type")
    val type: RemoteSearchResultType,
) {
    fun toOfflineRegionSearchResult(): OfflineRegionSearchResult = OfflineRegionSearchResult(
        id = id,
        name = name,
        highlightResult = highlightResult,
        type = when (type) {
            RemoteSearchResultType.REGION -> OfflineRegionSearchResultType.REGION
            RemoteSearchResultType.GROUP -> OfflineRegionSearchResultType.GROUP
        },
    )
}

@JsonClass(generateAdapter = false)
internal enum class RemoteSearchResultType {
    @field:Json(name = "REGION")
    REGION,

    @field:Json(name = "GROUP")
    GROUP,
}

@JsonClass(generateAdapter = true)
internal data class LibraryResetBody(
    @field:Json(name = "deviceSerialNumber")
    val deviceSerialNumber: String,
)

@JsonClass(generateAdapter = true)
internal data class RemoteOfflineAreaDownloadProgress(
    @field:Json(name = "deviceSerialNumber") val deviceSerialNumber: String,
    @field:Json(name = "sourceTypeUsed") val sourceType: String,
    @field:Json(name = "status") val status: RemoteOfflineRegionStatus,
    @field:Json(name = "downloadedSize") val downloadedSize: Long,
)
