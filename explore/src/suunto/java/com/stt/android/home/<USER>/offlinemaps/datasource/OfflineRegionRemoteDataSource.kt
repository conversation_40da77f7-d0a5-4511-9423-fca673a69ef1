package com.stt.android.home.explore.offlinemaps.datasource

import com.google.android.gms.maps.model.LatLng
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.exceptions.remote.ClientError
import com.stt.android.offlinemaps.datasource.OfflineRegionDataSource
import com.stt.android.offlinemaps.entity.OfflineArea
import com.stt.android.offlinemaps.entity.OfflineAreaSourceTileType
import com.stt.android.offlinemaps.entity.OfflineMapDownloadTarget
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.entity.OfflineRegionSearchResult
import com.stt.android.offlinemaps.entity.OfflineRegionStatus
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.withContext
import java.text.Collator
import javax.inject.Inject

internal class OfflineRegionRemoteDataSource @Inject constructor(
    private val offlineRegionRestApi: OfflineRegionRestApi,
    private val unit: MeasurementUnit,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : OfflineRegionDataSource {
    override suspend fun getCatalogue(
        deviceSerials: Set<String>,
        latLng: LatLng?,
        capabilities: String?,
        includeNearbyGroups: Boolean,
    ): OfflineRegionListData.Catalogue = withContext(coroutinesDispatchers.computation) {
        val catalogue = offlineRegionRestApi.getCatalogue(
            latitude = latLng?.latitude,
            longitude = latLng?.longitude,
            includeNearbyGroups = includeNearbyGroups,
            capabilities = capabilities,
        ).payloadOrThrow()
        val groups = catalogue.offlineRegionGroups(deviceSerials)
        OfflineRegionListData.Catalogue(
            nearby = catalogue.nearbyRegions(deviceSerials, groups),
            groups = groups
        )
    }

    private fun RemoteOfflineRegionCatalogue.offlineRegionGroups(
        deviceSerials: Set<String>,
    ) = groups.map { group ->
        val remoteRegions = group.regions.mapNotNull { regionId ->
            regions.firstOrNull { it.id == regionId.id }
        }
        OfflineRegionResult.OfflineRegionGroup(
            id = group.id,
            name = group.name,
            size = remoteRegions.sumOf { it.storageSizeForWatch },
            regions = remoteRegions
                .map { it.toOfflineRegion(deviceSerials, unit, group.name, group.batchDownloadAllowed, group.maskUrl) }
                .sortedWith { region1, region2 ->
                    Collator.getInstance().compare(region1.name, region2.name)
                }
                .toPersistentList(),
            batchDownloadAllowed = group.batchDownloadAllowed
        )
    }
        .sortedWith { group1, group2 ->
            Collator.getInstance().compare(group1.name, group2.name)
        }
        .toPersistentList()

    private fun RemoteOfflineRegionCatalogue.nearbyRegions(
        deviceSerials: Set<String>,
        localGroups: PersistentList<OfflineRegionResult.OfflineRegionGroup>
    ) =
        nearbyRegions
            .mapNotNull { remoteNearby ->
                if (remoteNearby.type == RemoteSearchResultType.GROUP) {
                    localGroups.firstOrNull { it.id == remoteNearby.id }
                } else {
                    regions.firstOrNull { it.id == remoteNearby.id }?.let { region ->
                        val currentGroup = groups
                            .firstOrNull { group ->
                                group.regions.any { it.id == region.id }
                            }
                        region.toOfflineRegion(deviceSerials, unit, currentGroup?.name, currentGroup?.batchDownloadAllowed, currentGroup?.maskUrl)
                    }
                }
            }
            .toPersistentList()

    override suspend fun putDownloadOrder(
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
        groupName: String?,
    ): OfflineRegionResult.OfflineRegion = offlineRegionRestApi.putDownloadOrder(
        downloadOrderRequest = DownloadOrderRequest(
            deviceSerialNumber = deviceSerial,
            regionId = regionId,
            capabilities = capabilities,
        ),
    )
        .payloadOrThrow()
        .run { toOfflineRegion(setOf(deviceSerial), unit, groupName) }

    override suspend fun search(searchTerm: String): ImmutableList<OfflineRegionSearchResult> =
        offlineRegionRestApi.search(searchTerm = searchTerm.trim(), scopes = "REGIONS,GROUPS")
            .payloadOrThrow().results
            .map { it.toOfflineRegionSearchResult() }
            .toPersistentList()

    override suspend fun getLibrary(
        downloadTarget: OfflineMapDownloadTarget,
        deviceSerial: String,
        capabilities: String?,
    ): ImmutableList<OfflineRegionResult.OfflineRegion> =
        offlineRegionRestApi.getLibrary(
            deviceSerial = deviceSerial,
            capabilities = capabilities,
        )
            .payloadOrThrow()
            .map { it.toOfflineRegion(downloadTarget, deviceSerial, unit) }
            .sortedWith { region1, region2 ->
                Collator.getInstance().compare(region1.name, region2.name)
            }
            .toPersistentList()

    override suspend fun getLibraryRegion(
        downloadTarget: OfflineMapDownloadTarget,
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
    ): OfflineRegionResult.OfflineRegion? = try {
        offlineRegionRestApi.getLibraryRegion(
            regionId = regionId,
            deviceSerial = deviceSerial,
            capabilities = capabilities,
        ).payloadOrThrow()
            .toOfflineRegion(downloadTarget, deviceSerial, unit)
    } catch (_: ClientError.NotFound) {
        null
    }

    override suspend fun deleteRegion(
        deviceSerial: String,
        region: OfflineRegionResult.OfflineRegion,
        capabilities: String?,
    ): OfflineRegionResult.OfflineRegion {
        return if (region.deleteRequested) {
            offlineRegionRestApi.cancelDeleteRequest(
                regionId = region.id,
                deviceSerial = deviceSerial,
                capabilities = capabilities
            )
        } else {
            offlineRegionRestApi.deleteRegion(
                regionId = region.id,
                deviceSerial = deviceSerial,
                capabilities = capabilities
            )
        }
            .payloadOrThrow()
            .toOfflineRegion(setOf(deviceSerial), unit, region.groupName)
    }

    override suspend fun resetLibrary(
        downloadTarget: OfflineMapDownloadTarget,
        deviceSerial: String,
        capabilities: String?,
    ): ImmutableList<OfflineRegionResult.OfflineRegion> {
        return offlineRegionRestApi.resetLibrary(
            body = LibraryResetBody(deviceSerialNumber = deviceSerial),
            capabilities = capabilities
        )
            .payloadOrThrow()
            .map { it.toOfflineRegion(downloadTarget, deviceSerial, unit) }
            .toPersistentList()
    }

    override suspend fun getOfflineAreas(
        deviceSerial: String,
        sourceType: OfflineAreaSourceTileType,
        hidePaths: Boolean,
    ): List<OfflineArea> = offlineRegionRestApi.getOfflineAreas(
        deviceSerial = deviceSerial,
        sourceType = sourceType.toRemoteValue(),
        hidePaths = hidePaths,
    ).payloadOrThrow()
        .map { it.toOfflineArea() }

    override suspend fun reportDownloadProgress(
        regionId: String,
        deviceSerial: String,
        sourceType: OfflineAreaSourceTileType,
        status: OfflineRegionStatus,
        downloadedSize: Long
    ) {
        offlineRegionRestApi.reportDownloadProgress(
            regionId = regionId,
            downloadProgress = RemoteOfflineAreaDownloadProgress(
                deviceSerialNumber = deviceSerial,
                sourceType = sourceType.toRemoteValue(),
                status = RemoteOfflineRegionStatus.from(status),
                downloadedSize = downloadedSize,
            ),
        )
    }
}
