package com.stt.android.home.explore.offlinemaps.selection.search

import com.stt.android.offlinemaps.entity.OfflineRegionListData

sealed interface OfflineMapsSearchViewEvent {
    data object ResetSearchTerm : OfflineMapsSearchViewEvent

    data class UpdateCatalogue(
        val catalogue: OfflineRegionListData.Catalogue,
    ) : OfflineMapsSearchViewEvent

    data class UpdateSearchTerm(
        val searchTerm: String,
    ) : OfflineMapsSearchViewEvent
}

typealias OfflineMapsSearchEventHandler = (OfflineMapsSearchViewEvent) -> Unit
