package com.stt.android.home.explore.offlinemaps

import android.app.Application
import com.squareup.moshi.Moshi
import com.stt.android.home.explore.offlinemaps.datasource.OfflineRegionRemoteDataSource
import com.stt.android.home.explore.offlinemaps.datasource.OfflineRegionRepository
import com.stt.android.home.explore.offlinemaps.datasource.OfflineRegionRepositoryImpl
import com.stt.android.home.explore.offlinemaps.datasource.OfflineRegionRestApi
import com.stt.android.home.explore.offlinemaps.domain.DownloadOfflineMapUseCaseImpl
import com.stt.android.offlinemaps.datasource.OfflineRegionDataSource
import com.stt.android.offlinemaps.domain.DownloadOfflineMapUseCase
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.Cache
import okhttp3.OkHttpClient
import java.io.File

@Module
@InstallIn(SingletonComponent::class)
abstract class OfflineMapsModule {
    @Binds
    internal abstract fun bindDownloadOfflineMapUseCase(impl: DownloadOfflineMapUseCaseImpl): DownloadOfflineMapUseCase

    @Binds
    internal abstract fun bindOfflineRegionRepository(impl: OfflineRegionRepositoryImpl): OfflineRegionRepository

    @Binds
    internal abstract fun bindOfflineRegionDataSource(impl: OfflineRegionRemoteDataSource): OfflineRegionDataSource

    companion object {
        @Provides
        internal fun provideOfflineRegionRestApi(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi,
            application: Application,
        ): OfflineRegionRestApi = RestApiFactory.buildRestApi(
            sharedClient,
            baseUrl,
            OfflineRegionRestApi::class.java,
            BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
            moshi,
            Cache(File(application.cacheDir, "offline-region-cache"), 1024 * 1024 * 2)
        )
    }
}
