package com.stt.android.home.explore.offlinemaps.selection.ui

import android.text.format.Formatter
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.header
import com.stt.android.compose.theme.spacing
import com.stt.android.home.explore.offlinemaps.selection.OfflineMapsSelectionEventHandler
import com.stt.android.home.explore.offlinemaps.selection.OfflineMapsSelectionViewEvent
import com.stt.android.home.explore.offlinemaps.selection.OfflineMapsSelectionViewState
import com.stt.android.home.explore.offlinemaps.ui.preview.FakeOfflineRegionGroupData
import com.stt.android.offlinemaps.entity.FreeSpaceAvailable
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentSetOf
import java.util.Locale

@Composable
internal fun OfflineRegionList(
    offlineRegions: ImmutableList<OfflineRegionResult>,
    nearbyOfflineRegions: ImmutableList<OfflineRegionResult>,
    showDownloadsSection: Boolean,
    offlineMapsSelectionViewState: OfflineMapsSelectionViewState.Loaded,
    offlineMapsSelectionEventHandler: OfflineMapsSelectionEventHandler,
    onViewInLibrary: (OfflineRegionResult.OfflineRegion?) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier,
    ) {
        if (showDownloadsSection) {
            item(key = "Downloads section") {
                Downloads(
                    freeSpaceAvailable = offlineMapsSelectionViewState.freeSpaceAvailable,
                )

                ViewMapLibrary(
                    updateAvailableCount = offlineMapsSelectionViewState.updateAvailableCount,
                    onClick = { onViewInLibrary(null) }
                )

                if (nearbyOfflineRegions.isEmpty()) {
                    HorizontalDivider(
                        modifier = Modifier
                            .padding(start = MaterialTheme.spacing.medium),
                    )
                }
            }
        }

        if (nearbyOfflineRegions.isNotEmpty()) {
            item(key = "Nearby header item") {
                SectionHeader(title = R.string.offline_maps_nearby_title)
            }

            itemsIndexed(
                items = nearbyOfflineRegions,
                key = { _, region -> "${region.id}_nearby" },
            ) { index, region ->
                RegionListItem(
                    offlineRegion = region,
                    offlineMapsSelectionViewState = offlineMapsSelectionViewState,
                    offlineMapsSelectionEventHandler = offlineMapsSelectionEventHandler,
                    onViewInLibrary = onViewInLibrary,
                    highlight = null,
                    showDivider = index > 0,
                )
            }

            item(key = "Countries header item") {
                SectionHeader(title = R.string.offline_maps_countries_title)
            }
        }

        itemsIndexed(
            items = offlineRegions,
            key = { _, region -> region.id },
        ) { index, region ->
            RegionListItem(
                offlineRegion = region,
                offlineMapsSelectionViewState = offlineMapsSelectionViewState,
                offlineMapsSelectionEventHandler = offlineMapsSelectionEventHandler,
                onViewInLibrary = onViewInLibrary,
                highlight = null,
                showDivider = index > 0,
            )
        }
    }
}

@Composable
internal fun RegionListItem(
    offlineRegion: OfflineRegionResult,
    offlineMapsSelectionViewState: OfflineMapsSelectionViewState.Loaded,
    offlineMapsSelectionEventHandler: OfflineMapsSelectionEventHandler,
    onViewInLibrary: (OfflineRegionResult.OfflineRegion) -> Unit,
    highlight: String?,
    showDivider: Boolean,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier,
    ) {
        when (offlineRegion) {
            is OfflineRegionResult.OfflineRegion -> {
                OfflineRegionItem(
                    region = offlineRegion,
                    cancellingDownload = offlineMapsSelectionViewState.cancellingDownload,
                    requestingDownload = offlineMapsSelectionViewState.requestingDownload,
                    onClick = { offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.SelectOfflineRegion(offlineRegion)) },
                    onViewInLibrary = onViewInLibrary,
                    onCancel = { offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.CancelDownloadingOfflineRegion(it.id)) },
                    onRetry = { offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.DownloadOfflineRegion(it.id)) },
                    modifier = Modifier.fillMaxWidth(),
                    highlight = highlight,
                )
            }

            is OfflineRegionResult.OfflineRegionGroup -> {
                OfflineRegionGroupItem(
                    group = offlineRegion,
                    highlight = highlight,
                    onClick = { offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.SelectOfflineRegionGroup(offlineRegion)) },
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }

        if (showDivider) {
            HorizontalDivider(
                modifier = Modifier
                    .padding(start = MaterialTheme.spacing.medium),
            )
        }
    }
}

@Composable
private fun Downloads(
    freeSpaceAvailable: FreeSpaceAvailable?,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.lightGrey)
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = stringResource(id = R.string.offline_maps_downloads)
                .uppercase(Locale.getDefault()),
            style = MaterialTheme.typography.header,
        )

        if (freeSpaceAvailable == null) {
            return
        }

        Spacer(Modifier.weight(1.0F))

        Text(
            text = stringResource(
                id = R.string.memory_usage_free_space,
                Formatter.formatShortFileSize(
                    LocalContext.current,
                    freeSpaceAvailable.freeSpace,
                )
            ),
            style = MaterialTheme.typography.body,
            color = if (freeSpaceAvailable.freeSpacePercentage <= 10) {
                MaterialTheme.colorScheme.error
            } else {
                MaterialTheme.colorScheme.secondary
            }
        )
    }
}

@Composable
private fun ViewMapLibrary(
    updateAvailableCount: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(R.drawable.save_bookmark_outline),
            contentDescription = null,
            tint = MaterialTheme.colorScheme.secondary,
            modifier = Modifier.size(MaterialTheme.iconSizes.medium),
        )

        Spacer(Modifier.width(MaterialTheme.spacing.medium))

        Column {
            Text(
                text = stringResource(id = R.string.offline_maps_view_map_library),
                style = MaterialTheme.typography.bodyLarge,
            )

            if (updateAvailableCount > 0) {
                Text(
                    text = pluralStringResource(
                        id = R.plurals.offline_maps_available_map_updates,
                        count = updateAvailableCount,
                        updateAvailableCount,
                    ),
                    style = MaterialTheme.typography.bodyMedium,
                )
            }
        }

        Spacer(Modifier.weight(1.0F))

        Icon(
            painter = SuuntoIcons.ActionRight.asPainter(),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.medium),
        )
    }
}

@Composable
private fun SectionHeader(
    @StringRes title: Int,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.lightGrey)
            .padding(MaterialTheme.spacing.medium),
    ) {
        Text(
            text = stringResource(id = title)
                .uppercase(Locale.getDefault()),
            style = MaterialTheme.typography.header,
        )
    }
}

@Preview
@Composable
private fun DownloadsPreview() {
    M3AppTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        ) {
            Downloads(
                freeSpaceAvailable = null,
            )

            Downloads(
                freeSpaceAvailable = FreeSpaceAvailable(
                    freeSpace = 1_000_000L,
                    freeSpacePercentage = 90,
                ),
            )

            Downloads(
                freeSpaceAvailable = FreeSpaceAvailable(
                    freeSpace = 50_000L,
                    freeSpacePercentage = 5,
                ),
            )
        }
    }
}

@Preview
@Composable
private fun ViewMapLibraryPreview() {
    M3AppTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        ) {
            ViewMapLibrary(
                updateAvailableCount = 0,
                onClick = {},
            )

            ViewMapLibrary(
                updateAvailableCount = 1,
                onClick = {},
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun OfflineRegionListPreview() {
    M3AppTheme {
        OfflineRegionList(
            offlineRegions = FakeOfflineRegionGroupData.finland.regions,
            nearbyOfflineRegions = persistentListOf(),
            showDownloadsSection = true,
            offlineMapsSelectionViewState = OfflineMapsSelectionViewState.Loaded(
                supportsOfflineMapsOnMobile = true,
                selectedDownloadTargets = persistentSetOf(),
                showBatteryInfoWithPendingDownload = false,
                showWifiDisabledInfoWithPendingDownload = false,
                showWifiSetupInfoWithPendingDownload = false,
                catalogue = OfflineRegionListData.Catalogue(),
                selectedRegion = null,
                selectedRegionGroup = null,
                showDownloadInfo = false,
                downloadError = null,
                cancellingDownload = false,
                requestingDownload = false,
                updateAvailableCount = 0,
                freeSpaceAvailable = null,
            ),
            offlineMapsSelectionEventHandler = {},
            onViewInLibrary = {},
        )
    }
}
