package com.stt.android.home.explore.offlinemaps.datasource

import com.google.android.gms.maps.model.LatLng
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.await
import com.stt.android.data.source.local.suuntoplusguide.WatchCapabilitiesDao
import com.stt.android.offlinemaps.datasource.OfflineRegionDataSource
import com.stt.android.offlinemaps.entity.OfflineArea
import com.stt.android.offlinemaps.entity.OfflineAreaSourceTileType
import com.stt.android.offlinemaps.entity.OfflineMapDownloadTarget
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.entity.OfflineRegionSearchResult
import com.stt.android.offlinemaps.entity.OfflineRegionStatus
import com.stt.android.watch.SuuntoWatchModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import javax.inject.Inject

internal interface OfflineRegionRepository {
    suspend fun getOfflineRegionCatalogue(
        latLng: LatLng? = null
    ): OfflineRegionListData.Catalogue

    suspend fun getLibrary(): ImmutableList<OfflineRegionResult.OfflineRegion>

    suspend fun getLibraryRegion(
        regionId: String,
    ): OfflineRegionResult.OfflineRegion

    suspend fun search(searchTerm: String): ImmutableList<OfflineRegionSearchResult>

    suspend fun getOfflineAreas(target: OfflineMapDownloadTarget): List<OfflineArea>

    suspend fun reportDownloadProgress(
        regionId: String,
        target: OfflineMapDownloadTarget,
        sourceType: OfflineAreaSourceTileType,
        status: OfflineRegionStatus,
        downloadedSize: Long,
    )

    suspend fun downloadOfflineRegion(
        downloadTarget: OfflineMapDownloadTarget,
        regionId: String,
        groupName: String? = null,
    ): OfflineRegionResult.OfflineRegion

    suspend fun deleteDownload(
        deleteFrom: OfflineMapDownloadTarget,
        region: OfflineRegionResult.OfflineRegion,
    ): OfflineRegionResult.OfflineRegion

    suspend fun getMapStorageSize(): Long?
}

internal class OfflineRegionRepositoryImpl @Inject constructor(
    private val offlineRegionDataSource: OfflineRegionDataSource,
    private val watchCapabilitiesDao: WatchCapabilitiesDao,
    private val suuntoWatchModel: SuuntoWatchModel,
    private val userSettingsController: UserSettingsController,
    private val dispatchers: CoroutinesDispatchers,
) : OfflineRegionRepository {

    private lateinit var _watchCapabilities: List<String>
    private lateinit var _watchCapabilitiesAsString: String

    private suspend fun getWatchCapabilities(): String =
        if (::_watchCapabilitiesAsString.isInitialized) {
            _watchCapabilitiesAsString
        } else {
            val serial = suuntoWatchModel.currentWatch.await().serial
            _watchCapabilities = watchCapabilitiesDao.findForSerial(serial)?.capabilities
                ?: emptyList()
            _watchCapabilities.joinToString(separator = ",")
                .also { _watchCapabilitiesAsString = it }
        }

    private suspend fun getWatchCapabilities(
        downloadTarget: OfflineMapDownloadTarget,
    ): String = when (downloadTarget) {
        OfflineMapDownloadTarget.WATCH -> getWatchCapabilities()
        OfflineMapDownloadTarget.MOBILE -> ""
    }

    override suspend fun getOfflineRegionCatalogue(
        latLng: LatLng?,
    ): OfflineRegionListData.Catalogue = offlineRegionDataSource.getCatalogue(
        deviceSerials = OfflineMapDownloadTarget.entries.map { getDeviceUuid(it) }.toSet(),
        capabilities = getWatchCapabilities(),
        latLng = latLng,
        includeNearbyGroups = true,
    )

    override suspend fun getLibrary(): ImmutableList<OfflineRegionResult.OfflineRegion> = withContext(dispatchers.computation) {
        val watchLibraryAsync = async {
            offlineRegionDataSource.getLibrary(
                downloadTarget = OfflineMapDownloadTarget.WATCH,
                deviceSerial = getDeviceUuid(OfflineMapDownloadTarget.WATCH),
                capabilities = getWatchCapabilities(),
            )
        }

        val mobileLibrary = offlineRegionDataSource.getLibrary(
            downloadTarget = OfflineMapDownloadTarget.MOBILE,
            deviceSerial = getDeviceUuid(OfflineMapDownloadTarget.MOBILE),
            capabilities = "",
        )
            .associateBy(OfflineRegionResult.OfflineRegion::id)
            .toMutableMap()

        val watchLibrary = watchLibraryAsync.await()
            .map { offlineRegionOnWatch ->
                mobileLibrary[offlineRegionOnWatch.id]
                    ?.let { offlineRegionOnMobile ->
                        mobileLibrary.remove(offlineRegionOnWatch.id)

                        offlineRegionOnWatch.copy(
                            downloadOrders = (offlineRegionOnWatch.downloadOrders + offlineRegionOnMobile.downloadOrders).toPersistentList(),
                        )
                    }
                    ?: offlineRegionOnWatch
            }
        (watchLibrary + mobileLibrary.values).toPersistentList()
    }

    override suspend fun getLibraryRegion(
        regionId: String,
    ): OfflineRegionResult.OfflineRegion = withContext(dispatchers.computation) {
        val watchLibraryAsync = async {
            offlineRegionDataSource.getLibraryRegion(
                downloadTarget = OfflineMapDownloadTarget.WATCH,
                deviceSerial = getDeviceUuid(OfflineMapDownloadTarget.WATCH),
                regionId = regionId,
                capabilities = getWatchCapabilities(),
            )
        }

        val mobileLibrary = offlineRegionDataSource.getLibraryRegion(
            downloadTarget = OfflineMapDownloadTarget.MOBILE,
            deviceSerial = getDeviceUuid(OfflineMapDownloadTarget.MOBILE),
            regionId = regionId,
            capabilities = "",
        )

        val watchLibrary = watchLibraryAsync.await()
        when {
            watchLibrary != null && mobileLibrary != null -> if (mobileLibrary.downloadOrders.isEmpty()) {
                watchLibrary
            } else {
                watchLibrary.copy(
                    downloadOrders = (watchLibrary.downloadOrders + mobileLibrary.downloadOrders).toPersistentList(),
                )
            }
            watchLibrary == null && mobileLibrary != null -> mobileLibrary
            watchLibrary != null && mobileLibrary == null -> watchLibrary
            else -> error("No region found for regionId: $regionId")
        }
    }

    override suspend fun search(searchTerm: String): ImmutableList<OfflineRegionSearchResult> =
        offlineRegionDataSource.search(searchTerm)

    override suspend fun downloadOfflineRegion(
        downloadTarget: OfflineMapDownloadTarget,
        regionId: String,
        groupName: String?
    ): OfflineRegionResult.OfflineRegion = offlineRegionDataSource.putDownloadOrder(
        deviceSerial = getDeviceUuid(downloadTarget),
        regionId = regionId,
        capabilities = getWatchCapabilities(downloadTarget),
        groupName = groupName,
    )

    // Can be used to delete downloaded maps from the watch or to cancel ongoing download
    override suspend fun deleteDownload(
        deleteFrom: OfflineMapDownloadTarget,
        region: OfflineRegionResult.OfflineRegion,
    ): OfflineRegionResult.OfflineRegion = offlineRegionDataSource.deleteRegion(
        deviceSerial = getDeviceUuid(deleteFrom),
        region = region,
        capabilities = getWatchCapabilities(deleteFrom),
    )

    override suspend fun getMapStorageSize(): Long? = withContext(dispatchers.computation) {
        val prefix = "feat_mapstorage_"
        getWatchCapabilities()
        _watchCapabilities.firstOrNull { it.startsWith(prefix) }
            ?.let { capability ->
                capability.substringAfter(prefix).toLong() * 1000_000L
            }
    }

    override suspend fun getOfflineAreas(
        target: OfflineMapDownloadTarget,
    ): List<OfflineArea> = offlineRegionDataSource.getOfflineAreas(
        deviceSerial = getDeviceUuid(target),
        sourceType = when (target) {
            OfflineMapDownloadTarget.MOBILE -> OfflineAreaSourceTileType.SMTF_V1_GZ
            OfflineMapDownloadTarget.WATCH -> OfflineAreaSourceTileType.SMTF_V1
        },
    )

    private suspend fun getDeviceUuid(
        target: OfflineMapDownloadTarget,
    ): String = withContext(dispatchers.io) {
        when (target) {
            OfflineMapDownloadTarget.MOBILE -> userSettingsController.settings.analyticsUUID
            OfflineMapDownloadTarget.WATCH -> suuntoWatchModel.currentWatch.await().serial
        }
    }

    override suspend fun reportDownloadProgress(
        regionId: String,
        target: OfflineMapDownloadTarget,
        sourceType: OfflineAreaSourceTileType,
        status: OfflineRegionStatus,
        downloadedSize: Long,
    ) {
        offlineRegionDataSource.reportDownloadProgress(
            regionId = regionId,
            deviceSerial = getDeviceUuid(target),
            sourceType = sourceType,
            status = status,
            downloadedSize = downloadedSize,
        )
    }
}
