package com.stt.android.home.explore.offlinemaps.datasource

import com.google.android.gms.maps.model.LatLng
import com.stt.android.home.explore.offlinemaps.ui.preview.FakeOfflineRegionGroupData
import com.stt.android.offlinemaps.datasource.OfflineRegionDataSource
import com.stt.android.offlinemaps.entity.DownloadOrder
import com.stt.android.offlinemaps.entity.OfflineArea
import com.stt.android.offlinemaps.entity.OfflineAreaSourceTileType
import com.stt.android.offlinemaps.entity.OfflineMapDownloadTarget
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.entity.OfflineRegionSearchResult
import com.stt.android.offlinemaps.entity.OfflineRegionSearchResultType
import com.stt.android.offlinemaps.entity.OfflineRegionStatus
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.delay

class FakeOfflineRegionDataSource : OfflineRegionDataSource {
    override suspend fun getCatalogue(
        deviceSerials: Set<String>,
        latLng: LatLng?,
        capabilities: String?,
        includeNearbyGroups: Boolean
    ): OfflineRegionListData.Catalogue {
        delay(1000)
        return OfflineRegionListData.Catalogue(
            nearby = persistentListOf(),
            groups = FakeOfflineRegionGroupData.regionGroups,
        )
    }

    override suspend fun putDownloadOrder(
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
        groupName: String?
    ): OfflineRegionResult.OfflineRegion {
        delay(1000)
        return FakeOfflineRegionGroupData.regionGroups.map { it.regions }
            .flatten()
            .first { it.id == regionId }
            .copy(
                downloadOrders = persistentListOf(
                    DownloadOrder(
                        deviceSerialNumber = deviceSerial,
                        downloadCompletedAt = null,
                        status = OfflineRegionStatus.REQUESTED,
                        downloadedSize = 0,
                        sourceTileType = OfflineAreaSourceTileType.SMTF_V1,
                    )
                ),
            )
    }

    override suspend fun deleteRegion(
        deviceSerial: String,
        region: OfflineRegionResult.OfflineRegion,
        capabilities: String?,
    ): OfflineRegionResult.OfflineRegion {
        delay(1000)
        return FakeOfflineRegionGroupData.regionGroups.flatMap { it.regions }
            .first { it.id == region.id }.run {
                if (region.downloading) {
                    copy(downloadOrders = persistentListOf())
                } else {
                    copy(
                        downloadOrders = downloadOrders.map { downloadOrder ->
                            downloadOrder.copy(status = OfflineRegionStatus.DELETE_REQUESTED)
                        }.toPersistentList()
                    )
                }
            }
    }

    override suspend fun search(searchTerm: String): ImmutableList<OfflineRegionSearchResult> {
        delay(1000)
        return if (searchTerm.isBlank()) {
            persistentListOf()
        } else {
            FakeOfflineRegionGroupData.regionGroups.map { offlineRegionGroup ->
                offlineRegionGroup.regions
                    .filter {
                        it.name.contains(searchTerm, ignoreCase = true)
                    }
                    .map {
                        OfflineRegionSearchResult(
                            id = it.id,
                            name = it.name,
                            highlightResult = searchTerm,
                            type = OfflineRegionSearchResultType.REGION,
                        )
                    }
            }.flatten().toPersistentList()
        }
    }

    override suspend fun getLibrary(
        downloadTarget: OfflineMapDownloadTarget,
        deviceSerial: String,
        capabilities: String?,
    ): ImmutableList<OfflineRegionResult.OfflineRegion> {
        delay(1000)
        return FakeOfflineRegionGroupData.finland.regions.toPersistentList()
    }

    override suspend fun getLibraryRegion(
        downloadTarget: OfflineMapDownloadTarget,
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
    ): OfflineRegionResult.OfflineRegion {
        delay(1000)
        return FakeOfflineRegionGroupData.regionGroups.flatMap { it.regions }.first { it.id == regionId }
    }

    override suspend fun resetLibrary(
        downloadTarget: OfflineMapDownloadTarget,
        deviceSerial: String,
        capabilities: String?,
    ): ImmutableList<OfflineRegionResult.OfflineRegion> {
        return FakeOfflineRegionGroupData.finland.regions.toPersistentList()
    }

    override suspend fun getOfflineAreas(
        deviceSerial: String,
        sourceType: OfflineAreaSourceTileType,
        hidePaths: Boolean
    ): List<OfflineArea> = emptyList()

    override suspend fun reportDownloadProgress(
        regionId: String,
        deviceSerial: String,
        sourceType: OfflineAreaSourceTileType,
        status: OfflineRegionStatus,
        downloadedSize: Long
    ) = Unit
}
