package com.stt.android.home.explore.offlinemaps.selection

import android.content.SharedPreferences
import androidx.lifecycle.SavedStateHandle
import com.google.android.gms.maps.model.LatLng
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.user.UserSettings
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.home.explore.offlinemaps.analytics.OfflineMapsAnalytics
import com.stt.android.home.explore.offlinemaps.datasource.FakeOfflineRegionDataSource
import com.stt.android.home.explore.offlinemaps.datasource.OfflineRegionRepository
import com.stt.android.home.explore.offlinemaps.domain.OfflineRegionDownloadOperators
import com.stt.android.home.explore.offlinemaps.selection.search.OfflineMapsSearchViewEvent
import com.stt.android.home.explore.offlinemaps.selection.search.OfflineMapsSearchViewModel
import com.stt.android.home.explore.offlinemaps.selection.search.OfflineMapsSearchViewState
import com.stt.android.home.explore.offlinemaps.ui.preview.FakeOfflineRegionGroupData.finland
import com.stt.android.offlinemaps.domain.SetWatchKeyUseCase
import com.stt.android.offlinemaps.entity.DownloadOrder
import com.stt.android.offlinemaps.entity.OfflineArea
import com.stt.android.offlinemaps.entity.OfflineAreaSourceTileType
import com.stt.android.offlinemaps.entity.OfflineMapDownloadTarget
import com.stt.android.offlinemaps.entity.OfflineRegionDownloadError
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.entity.OfflineRegionSearchResult
import com.stt.android.offlinemaps.entity.OfflineRegionStatus
import com.stt.android.testutils.NewCoroutinesTestRule
import com.stt.android.usecases.location.LastKnownLocationUseCase
import com.stt.android.watch.wifi.domain.GetSavedWifiNetworksCountUseCase
import com.stt.android.watch.wifi.domain.GetWifiEnabledUseCase
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals

@RunWith(MockitoJUnitRunner::class)
class OfflineMapsSelectionViewModelTest {
    @Mock
    private lateinit var lastKnownLocationUseCase: LastKnownLocationUseCase

    @Mock
    private lateinit var savedWifiNetworksCountUseCase: GetSavedWifiNetworksCountUseCase

    @Mock
    private lateinit var isWatchConnectedUseCase: IsWatchConnectedUseCase

    @Mock
    private lateinit var getWifiEnabledUseCase: GetWifiEnabledUseCase

    @Mock
    private lateinit var setWatchKeyUseCase: SetWatchKeyUseCase

    @Mock
    private lateinit var downloadOperators: OfflineRegionDownloadOperators

    @Mock
    private lateinit var mockSharedPrefs: SharedPreferences

    @Mock
    private lateinit var sharedPreferencesEditor: SharedPreferences.Editor

    @Mock
    private lateinit var offlineMapsAnalytics: OfflineMapsAnalytics

    @Mock
    private lateinit var userSettingsController: UserSettingsController

    @Mock
    private lateinit var featureTogglePreferences: SharedPreferences

    @Mock
    private lateinit var savedStateHandle: SavedStateHandle

    private val dummyDataSource = FakeOfflineRegionDataSource()

    private val offlineRegionRepository: OfflineRegionRepository =
        object : OfflineRegionRepository {
            override suspend fun getOfflineRegionCatalogue(latLng: LatLng?): OfflineRegionListData.Catalogue {
                return dummyDataSource.getCatalogue(setOf(SERIAL), LAT_LNG, CAPABILITIES, false)
            }

            override suspend fun getLibrary(): ImmutableList<OfflineRegionResult.OfflineRegion> {
                val uusimaa = finland.regions.first { it.name == "Uusimaa" }
                return persistentListOf(
                    uusimaa.copy(
                        downloadOrders = persistentListOf(
                            DownloadOrder(
                                deviceSerialNumber = SERIAL,
                                downloadCompletedAt = null,
                                status = OfflineRegionStatus.IN_PROGRESS,
                                downloadedSize = (uusimaa.sizeForWatch?.transferSizeInBytes
                                    ?: 0L) / 2,
                                sourceTileType = null
                            )
                        ),
                    )
                )
            }

            override suspend fun getLibraryRegion(regionId: String): OfflineRegionResult.OfflineRegion =
                dummyDataSource.getLibraryRegion(OfflineMapDownloadTarget.WATCH, SERIAL, regionId, CAPABILITIES)

            override suspend fun search(searchTerm: String): ImmutableList<OfflineRegionSearchResult> =
                dummyDataSource.search(searchTerm)

            override suspend fun getOfflineAreas(target: OfflineMapDownloadTarget): List<OfflineArea> =
                dummyDataSource.getOfflineAreas(SERIAL, SOURCE_TYPE)

            override suspend fun reportDownloadProgress(
                regionId: String,
                target: OfflineMapDownloadTarget,
                sourceType: OfflineAreaSourceTileType,
                status: OfflineRegionStatus,
                downloadedSize: Long
            ) {
                dummyDataSource.reportDownloadProgress(
                    regionId = regionId,
                    deviceSerial = SERIAL,
                    sourceType = sourceType,
                    status = status,
                    downloadedSize = downloadedSize,
                )
            }

            override suspend fun downloadOfflineRegion(
                downloadTarget: OfflineMapDownloadTarget,
                regionId: String,
                groupName: String?,
            ): OfflineRegionResult.OfflineRegion = TODO("Not yet implemented")

            override suspend fun deleteDownload(
                deleteFrom: OfflineMapDownloadTarget,
                region: OfflineRegionResult.OfflineRegion,
            ): OfflineRegionResult.OfflineRegion = TODO("Not yet implemented")

            override suspend fun getMapStorageSize(): Long? = null
        }

    private val testDispatchers = object : CoroutinesDispatchers {
        override val main = Dispatchers.Unconfined
        override val computation = Dispatchers.Unconfined
        override val io = Dispatchers.Unconfined
    }

    @Rule
    @JvmField
    val coroutinesTestRule = NewCoroutinesTestRule()

    private val viewModel: OfflineMapsSelectionViewModel by lazy {
        OfflineMapsSelectionViewModel(
            savedStateHandle = savedStateHandle,
            offlineRegionRepository = offlineRegionRepository,
            lastKnownLocationUseCase = lastKnownLocationUseCase,
            savedWifiNetworksCountUseCase = savedWifiNetworksCountUseCase,
            isWatchConnectedUseCase = isWatchConnectedUseCase,
            getWifiEnabledUseCase = getWifiEnabledUseCase,
            setWatchKeyUseCase = setWatchKeyUseCase,
            downloadOperators = downloadOperators,
            sharedPreferences = mockSharedPrefs,
            offlineMapsAnalytics = offlineMapsAnalytics,
            userSettingsController = userSettingsController,
            dispatchers = testDispatchers,
            featureTogglePreferences = featureTogglePreferences,
        )
    }

    private val searchViewModel: OfflineMapsSearchViewModel by lazy {
        OfflineMapsSearchViewModel(
            offlineRegionRepository = offlineRegionRepository,
        )
    }

    @BeforeTest
    fun setup() {
        whenever(mockSharedPrefs.edit()).thenReturn(sharedPreferencesEditor)
    }

    @Test
    fun `test download request`() = runTest {
        val region = finland.regions.first { it.name == "Uusimaa" }
        val expected = region.copy(
            downloadOrders = persistentListOf(
                DownloadOrder(
                    deviceSerialNumber = SERIAL,
                    downloadCompletedAt = null,
                    status = OfflineRegionStatus.REQUESTED,
                    downloadedSize = null,
                    sourceTileType = null,
                )
            ),
        )
        whenever(lastKnownLocationUseCase.getLastKnownLocation(any(), any())).thenReturn(LAT_LNG)
        whenever(
            downloadOperators.downloadOfflineRegion(
                setOf(OfflineMapDownloadTarget.WATCH),
                region.id,
                region.groupName
            )
        ).thenReturn(expected)

        val viewModel = viewModel // Creates the view model and calls its init {}
        advanceUntilIdle() // Wait for tasks started by init block to complete
        viewModel.handleEvent(OfflineMapsSelectionViewEvent.DownloadOfflineRegion(region.id))

        val uusimaa = viewModel.offlineRegionCatalogue
            .groups.filter { it.regions.isNotEmpty() }.first { it.name == "Finland" }
            .regions.first { it.name == "Uusimaa" }
        verify(downloadOperators, times(1)).downloadOfflineRegion(
            setOf(OfflineMapDownloadTarget.WATCH),
            region.id,
            region.groupName
        )
        assertEquals(expected, uusimaa)
    }

    @Test
    fun `test download request fail`() = runTest {
        val region = finland.regions.first { it.name == "Uusimaa" }
        val expectedError = OfflineRegionDownloadError(
            regionName = region.name,
            description = null
        )
        whenever(
            downloadOperators.downloadOfflineRegion(
                setOf(OfflineMapDownloadTarget.WATCH),
                region.id,
                region.groupName
            )
        ).thenThrow(RuntimeException())

        val viewModel = viewModel // Creates the view model and calls its init {}
        advanceUntilIdle() // Wait for tasks started by init block to complete
        viewModel.handleEvent(OfflineMapsSelectionViewEvent.DownloadOfflineRegion(region.id))

        verify(downloadOperators, times(1)).downloadOfflineRegion(
            setOf(OfflineMapDownloadTarget.WATCH),
            region.id,
            region.groupName
        )
        assertEquals(
            expectedError,
            (viewModel.viewState.value as? OfflineMapsSelectionViewState.Loaded)?.downloadError,
        )
    }

    @Test
    fun `test download cancel`() = runTest {
        val settings = mock<UserSettings>().apply {
            whenever(analyticsUUID).thenReturn("random analytics uuid")
        }
        whenever(userSettingsController.settings).thenReturn(settings)

        val region = finland.regions.first { it.name == "Pohjanmaa" }
        whenever(lastKnownLocationUseCase.getLastKnownLocation(any(), any())).thenReturn(LAT_LNG)

        val viewModel = viewModel // Creates the view model and calls its init {}
        advanceUntilIdle() // Wait for tasks started by init block to complete
        viewModel.handleEvent(OfflineMapsSelectionViewEvent.CancelDownloadingOfflineRegion(region.id))
        verify(downloadOperators, times(1))
            .deleteDownload(
                region = region,
                deleteFrom = setOf(OfflineMapDownloadTarget.WATCH),
            )
    }

    @Test
    fun `test download progress updates`() = runTest {
        whenever(lastKnownLocationUseCase.getLastKnownLocation(any(), any())).thenReturn(LAT_LNG)

        val viewModel = viewModel // Creates the view model and calls its init {}
        advanceUntilIdle() // Wait for tasks started by init block to complete
        viewModel.updateDownloadOrderStatuses()
        advanceUntilIdle()

        val uusimaa = viewModel.offlineRegionCatalogue
            .groups.filter { it.regions.isNotEmpty() }.first { it.name == "Finland" }
            .regions.first { it.name == "Uusimaa" }
        assertEquals(OfflineRegionStatus.IN_PROGRESS, uusimaa.downloadOrders.firstOrNull()?.status)
        assertEquals(0.5f, uusimaa.downloadProgress, 0.1f)
    }

    @Test
    fun `test search`() = runTest {
        val expected = finland.regions.first { it.name == "Uusimaa" }

        val viewModel = viewModel
        val searchViewModel = searchViewModel // Creates the view model and calls its init {}
        advanceUntilIdle() // Wait for tasks started by init block to complete
        searchViewModel.handleEvent(OfflineMapsSearchViewEvent.UpdateCatalogue(viewModel.offlineRegionCatalogue))
        searchViewModel.handleEvent(OfflineMapsSearchViewEvent.UpdateSearchTerm("Uusimaa"))
        advanceUntilIdle()
        assertEquals(
            OfflineMapsSearchViewState.MatchesFound(
                searchTerm = "Uusimaa",
                searchResult = persistentListOf(expected),
            ),
            searchViewModel.viewState.value,
        )
    }

    companion object {
        private val LAT_LNG: LatLng = LatLng(61.498056, 23.760833)
        private const val CAPABILITIES =
            "feat_devices_1,feat_guides_v5,feat_guidetemplate_v8,feat_mapstorage_28000,feat_offlinemaps_1,feat_plugins_1,feat_simvariant_Rostock,feat_voicefeedback_1,feat_wbstorage_v1,feat_weather_1,feat_widgets_v1,feat_zappcount_100,hw_accelerometerrate_50,hw_buttoncount_3,hw_gps_1,hw_magnetometer_1,hw_pressure_1,hw_solar_1,hw_temperature_1,hw_touch_1,hw_wifi_1,plugin_maxzappsize_2,plugin_settings_v1,ui_screensize_mediumplus,ui_version_2"
        private const val SERIAL = "20429029042903"
        private val SOURCE_TYPE = OfflineAreaSourceTileType.SMTF_V1
    }
}
