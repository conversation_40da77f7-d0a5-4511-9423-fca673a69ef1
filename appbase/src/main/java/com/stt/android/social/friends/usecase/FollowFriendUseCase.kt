package com.stt.android.social.friends.usecase

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.await
import com.stt.android.home.people.PeopleController
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendStatus
import com.stt.android.social.friends.utils.toFriend
import kotlinx.coroutines.withContext
import javax.inject.Inject

class FollowFriendUseCase @Inject constructor(
    private val peopleController: PeopleController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    suspend operator fun invoke(friend: Friend): Friend =
        withContext(coroutinesDispatchers.io) {
            val ufs = peopleController.loadUfsFromDbForFollowUser(friend)
            if (ufs.toFriend().friendStatus == FriendStatus.FOLLOW) {
                peopleController.followSimple(ufs).await().toFriend()
            } else {
                peopleController.unfollowSimple(ufs).await().toFriend()
            }
        }
}
