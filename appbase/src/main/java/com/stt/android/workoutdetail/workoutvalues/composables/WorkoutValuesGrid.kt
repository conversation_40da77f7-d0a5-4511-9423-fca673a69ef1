package com.stt.android.workoutdetail.workoutvalues.composables

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.spacing
import com.stt.android.workoutdetail.workoutvalues.WorkoutValuesGridItemData
import com.stt.android.workouts.details.values.WorkoutValue

@Composable
fun WorkoutValuesGrid(
    items: List<WorkoutValuesGridItemData>,
    rows: Int,
    onValueClicked: (WorkoutValue) -> Unit,
    modifier: Modifier = Modifier,
    valueTextStyle: TextStyle = MaterialTheme.typography.bodyLargeBold,
    labelTextStyle: TextStyle = MaterialTheme.typography.bodySmall,
    useNewStyle: Boolean = false, // todo remove when new style is taken in use in workout details
) {
    Column(
        modifier = modifier
            .animateContentSize()
            .then(
                if (useNewStyle) {
                    Modifier
                        .padding(MaterialTheme.spacing.medium)
                        .clip(RoundedCornerShape(androidx.compose.material3.MaterialTheme.spacing.medium))
                        .border(
                            width = 1.dp,
                            color = MaterialTheme.colors.background,
                            shape = RoundedCornerShape(16.dp)
                        )
                } else {
                    Modifier
                }
            )
            .background(color = MaterialTheme.colors.surface)
    ) {
        for (row in 0 until rows) {
            if (!useNewStyle || row > 0) {
                Divider(
                    modifier = Modifier.fillMaxWidth(),
                    color = MaterialTheme.colors.background
                )
            }
            Row(
                modifier = Modifier
                    .height(IntrinsicSize.Min)
                    .fillMaxWidth()
            ) {
                repeat(2) { column ->
                    val index = row * 2 + column
                    if (index < items.size) {
                        WorkoutValuesGridItem(
                            workoutValueGridItem = items[index],
                            onValueClicked = onValueClicked,
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight(),
                            valueTextStyle,
                            labelTextStyle,
                        )
                    } else {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                    if (column == 0) {
                        Divider(
                            modifier = Modifier
                                .fillMaxHeight()
                                .width(1.dp),
                            color = MaterialTheme.colors.background
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun WorkoutValuesGridPreview() {
    AppTheme {
        WorkoutValuesGrid(
            items = WorkoutValueGridDummyData.gridItems,
            rows = 4,
            onValueClicked = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun WorkoutValuesGridNewStylePreview() {
    AppTheme {
        WorkoutValuesGrid(
            items = WorkoutValueGridDummyData.gridItems,
            rows = 4,
            onValueClicked = {},
            useNewStyle = true,
        )
    }
}
