package com.stt.android.home.diary.diarycalendar.planner.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.ListItem
import androidx.compose.material3.ListItemDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.data.TimeUtils
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.diary.diarycalendar.planner.models.DistanceMeasurement
import com.stt.android.home.diary.diarycalendar.planner.models.PaceMeasurement
import com.stt.android.home.diary.diarycalendar.planner.models.Question
import com.stt.android.home.diary.diarycalendar.planner.models.QuestionId
import com.stt.android.home.diary.diarycalendar.planner.models.fakeMultiChoiceQuestion1
import com.stt.android.home.diary.diarycalendar.planner.models.fakeSingleChoiceQuestion1
import com.stt.android.home.diary.diarycalendar.planner.models.fakeSingleChoiceQuestion2
import com.stt.android.home.diary.diarycalendar.planner.models.fakeSportsQuestion
import com.stt.android.home.diary.diarycalendar.planner.models.fakeValueInputDateQuestion
import com.stt.android.home.diary.diarycalendar.planner.models.fakeValueInputDistanceQuestion
import com.stt.android.home.diary.diarycalendar.planner.models.fakeValueInputDurationQuestion
import com.stt.android.home.diary.diarycalendar.planner.models.fakeValueInputNumberQuestion
import com.stt.android.home.diary.diarycalendar.planner.models.fakeValueInputPaceQuestion
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.util.Locale

@Composable
fun TrainingPlannerSummary(
    questions: ImmutableList<Question>,
    onSummaryItemClick: (questionId: QuestionId) -> Unit,
    onStartThisProgramClick: () -> Unit,
    onCancelClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .background(MaterialTheme.colorScheme.surface)
    ) {
        Header()
        Text(
            text = stringResource(R.string.workout_planner_summary_description),
            style = MaterialTheme.typography.body,
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        )
        ProgramInformation(
            questions = questions,
            onSummaryItemClick = { questionId ->
                onSummaryItemClick(questionId)
            }
        )
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Button(
                onClick = onStartThisProgramClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = MaterialTheme.spacing.medium,
                        end = MaterialTheme.spacing.medium,
                        top = MaterialTheme.spacing.medium
                    ),
                shape = MaterialTheme.shapes.small,
            ) {
                Text(
                    text = stringResource(R.string.workout_planner_summary_start_program)
                        .uppercase(Locale.getDefault())
                )
            }
            TextButton(
                onClick = onCancelClick,
                modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
                shape = MaterialTheme.shapes.small,
            ) {
                Text(
                    text = stringResource(R.string.cancel).uppercase(Locale.getDefault()),
                )
            }
        }
    }
}

@Composable
private fun Header(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(56.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = stringResource(R.string.workout_planner_summary_title).uppercase(Locale.getDefault()),
            style = MaterialTheme.typography.bodyBold
        )
    }
}

@Composable
private fun ProgramInformation(
    questions: ImmutableList<Question>,
    onSummaryItemClick: (questionId: QuestionId) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    Column(modifier = modifier.fillMaxSize()) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(
                horizontal = MaterialTheme.spacing.medium,
                vertical = MaterialTheme.spacing.medium
            )
        ) {
            Text(
                text = stringResource(R.string.workout_planner_program_information).uppercase(),
                style = MaterialTheme.typography.bodyBold,
                modifier = Modifier.weight(1f)
            )
        }
        HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        questions.forEach { question ->
            ListItem(
                headlineContent = {
                    Text(
                        text = question.summaryTitle,
                        style = MaterialTheme.typography.body
                    )
                },
                supportingContent = {
                    Text(
                        text = when (question) {
                            is Question.MultiChoice -> question.answers.joinToString(", ") { it.text }

                            is Question.SingleChoice -> question.answer?.text
                            is Question.Sports -> question.selectedSports.map { context.getString(it.nameRes) }
                                .sorted().joinToString(", ")

                            is Question.ValueInput.Date -> {
                                if (question.valueInMillis != null) {
                                    TimeUtils.epochToZonedDateTime(question.valueInMillis)
                                        .toLocalDate().format(
                                            DateTimeFormatter.ofLocalizedDate(FormatStyle.LONG)
                                        )
                                } else {
                                    ""
                                }
                            }

                            is Question.ValueInput.Distance -> "${question.convertedValue} ${
                                context.getString(
                                    question.unitRes
                                )
                            }"

                            is Question.ValueInput.Duration -> "${question.hours?.toString()} ${
                                context.getString(
                                    com.stt.android.core.R.string.hour
                                )
                            } ${question.minutes?.toString()} ${context.getString(com.stt.android.core.R.string.minute)}"

                            is Question.ValueInput.Number -> question.value?.toString()
                            is Question.ValueInput.Pace -> "${question.convertedValue} ${
                                context.getString(
                                    question.unitRes
                                )
                            }"

                            is Question.ValueInput.Speed -> "${question.convertedValue} ${
                                context.getString(
                                    question.unitRes
                                )
                            }"
                        }.orEmpty(),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.primary
                    )
                },
                modifier = Modifier.clickable { onSummaryItemClick(question.id) },
                colors = ListItemDefaults.colors(containerColor = MaterialTheme.colorScheme.surface)
            )
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        }
    }
}

@Preview(showBackground = true, heightDp = 1000)
@Composable
private fun TrainingPlannerSummaryPreview() {
    M3AppTheme {
        TrainingPlannerSummary(
            questions = persistentListOf(
                fakeSingleChoiceQuestion1.copy(answer = fakeSingleChoiceQuestion1.options.first()),
                fakeSingleChoiceQuestion2.copy(answer = fakeSingleChoiceQuestion2.options.first()),
                fakeMultiChoiceQuestion1.copy(
                    answers = listOf(
                        fakeMultiChoiceQuestion1.options.first(),
                        fakeMultiChoiceQuestion1.options.last()
                    )
                ),
                fakeValueInputNumberQuestion.copy(value = 20),
                fakeValueInputDistanceQuestion.copy(
                    value = DistanceMeasurement(meters = 20),
                    measurementUnit = MeasurementUnit.METRIC
                ),
                fakeValueInputDurationQuestion.copy(hours = 10, minutes = 15),
                fakeValueInputPaceQuestion.copy(
                    value = PaceMeasurement(secondsPerKm = 20.0),
                    measurementUnit = MeasurementUnit.METRIC
                ),
                fakeValueInputDateQuestion,
                fakeSportsQuestion
            ),
            onSummaryItemClick = {},
            onStartThisProgramClick = {},
            onCancelClick = {}
        )
    }
}
