package com.stt.android.home.dashboardv2.ui

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.ScrollableState
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.PullToRefreshDefaults.Indicator
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.appversion.AppVersionViewModel
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.widgets.ExpandingFloatingActionButton
import com.stt.android.home.dashboardv2.ActivitiesTabViewModel
import com.stt.android.home.dashboardv2.BaseDashboardViewModel
import com.stt.android.home.dashboardv2.DashboardTabViewModel
import com.stt.android.home.dashboardv2.DashboardViewModel
import com.stt.android.home.dashboardv2.ToolbarViewModel
import com.stt.android.home.dashboardv2.ui.notifications.InlineAppViewState
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Suppress("ktlint:compose:vm-forwarding-check")
@Composable
internal fun DashboardScreen(
    viewModel: DashboardViewModel,
    toolbarViewModel: ToolbarViewModel,
    dashboardTabViewModel: DashboardTabViewModel,
    activitiesTabViewModel: ActivitiesTabViewModel,
    appVersionViewModel: AppVersionViewModel,
    viewEvent: (DashboardScreenViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    val viewData by viewModel.viewData.collectAsState()
    val isLoading = viewData.isRefreshing
    val pullToRefreshState = rememberPullToRefreshState()

    PullToRefreshBox(
        isRefreshing = isLoading,
        onRefresh = { viewEvent(DashboardScreenViewEvent.Refresh) },
        modifier = modifier,
        state = pullToRefreshState,
        indicator = {
            Indicator(
                state = pullToRefreshState,
                isRefreshing = isLoading,
                modifier = Modifier.align(Alignment.TopCenter),
                containerColor = MaterialTheme.colorScheme.surface,
                color = MaterialTheme.colorScheme.primary,
            )
        },
    ) {
        DashboardContent(
            viewData = viewData,
            toolbarViewModel = toolbarViewModel,
            dashboardTabViewModel = dashboardTabViewModel,
            activitiesTabViewModel = activitiesTabViewModel,
            appVersionViewModel = appVersionViewModel,
            viewEvent = viewEvent,
            onTabIndexUpdated = viewModel::updateTabIndexUpdated,
        )
    }
}

@Suppress("ktlint:compose:vm-forwarding-check")
@Composable
private fun DashboardContent(
    viewData: BaseDashboardViewModel.ViewData,
    toolbarViewModel: ToolbarViewModel,
    dashboardTabViewModel: DashboardTabViewModel,
    activitiesTabViewModel: ActivitiesTabViewModel,
    appVersionViewModel: AppVersionViewModel,
    viewEvent: (DashboardScreenViewEvent) -> Unit,
    onTabIndexUpdated: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()
    val pagerState = rememberPagerState(
        initialPage = viewData.tabIndex,
        pageCount = { DashboardTab.entries.size },
    )
    val fabExpanded = remember { mutableStateOf(true) }
    val inlineAppViewState = remember { InlineAppViewState() }

    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }
            .collect { pageIndex ->
                onTabIndexUpdated(pageIndex)
            }
    }

    Scaffold(
        modifier = modifier,
        snackbarHost = { SnackbarHost(hostState = snackbarHostState) },
        floatingActionButton = {
            if (viewData.showStartWorkoutFab) {
                ExpandingFloatingActionButton(
                    drawableResource = R.drawable.fab_start_workout,
                    text = stringResource(R.string.dashboard_start_workout),
                    expanded = fabExpanded.value,
                    enabled = true,
                    onClick = { viewEvent(DashboardScreenViewEvent.RecordWorkout) },
                    contentColor = MaterialTheme.colorScheme.surface,
                    backgroundColor = MaterialTheme.colorScheme.primaryContainer,
                    iconSize = MaterialTheme.iconSizes.small,
                )
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .padding(innerPadding),
        ) {
            FlavorDashboardToolbar(
                toolbarViewModel = toolbarViewModel,
                appVersionViewModel = appVersionViewModel,
                viewEvent = viewEvent,
                onUserClick = { username -> viewEvent(DashboardScreenViewEvent.OpenUser(username)) },
            )

            ScrollableTabRow(
                selectedTabIndex = pagerState.currentPage,
                modifier = Modifier.height(48.dp),
                containerColor = Color.Transparent,
                contentColor = MaterialTheme.colorScheme.primary,
                edgePadding = 0.dp,
                divider = { },
            ) {
                DashboardTab.entries.forEachIndexed { index, tab ->
                    Tab(
                        selected = pagerState.currentPage == index,
                        onClick = {
                            scope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        text = {
                            Text(
                                text = stringResource(tab.title),
                                style = MaterialTheme.typography.bodyLargeBold,
                            )
                        },
                        selectedContentColor = MaterialTheme.colorScheme.primary,
                        unselectedContentColor = MaterialTheme.colorScheme.darkGrey,
                    )
                }
            }

            HorizontalDivider()

            HorizontalPager(
                state = pagerState,
                modifier = Modifier
                    .background(color = MaterialTheme.colorScheme.background)
                    .narrowContent(),
            ) { index ->
                when (index) {
                    DashboardTab.DASHBOARD.ordinal -> {
                        val dashboardGridState = rememberLazyGridState()
                        TrackScrollForFabExpansion(dashboardGridState) { expanded ->
                            fabExpanded.value = expanded
                        }

                        DashboardTab(
                            viewModel = dashboardTabViewModel,
                            viewEvent = viewEvent,
                            onShowMoreWorkoutsClick = {
                                scope.launch {
                                    pagerState.animateScrollToPage(DashboardTab.ACTIVITIES.ordinal)
                                }
                            },
                            onBannerClose = { dashboardTabViewModel.closeBanner(it) },
                            onBannerExposure = { dashboardTabViewModel.onBannerExposure(it) },
                            gridState = dashboardGridState,
                            inlineAppViewState = inlineAppViewState,
                            snackbarHostState = snackbarHostState,
                        )
                    }

                    DashboardTab.ACTIVITIES.ordinal -> {
                        val activitiesListState = rememberLazyListState()
                        TrackScrollForFabExpansion(activitiesListState) { expanded ->
                            fabExpanded.value = expanded
                        }

                        ActivitiesTab(
                            viewModel = activitiesTabViewModel,
                            viewEvent = viewEvent,
                            listState = activitiesListState,
                        )
                    }

                    else -> throw IllegalArgumentException("Unsupported page index: $index")
                }
            }
        }
    }
}

@Composable
private fun <T : ScrollableState> TrackScrollForFabExpansion(
    scrollState: T,
    onFabExpanded: (Boolean) -> Unit,
) {
    val previousOffset = remember { mutableIntStateOf(0) }
    LaunchedEffect(scrollState) {
        snapshotFlow {
            when (scrollState) {
                is LazyGridState -> scrollState.firstVisibleItemScrollOffset
                is LazyListState -> scrollState.firstVisibleItemScrollOffset
                else -> 0
            }
        }.collect { scrollOffset ->
            if (scrollOffset == 0) {
                onFabExpanded(true)
            } else if (scrollOffset > previousOffset.intValue) {
                onFabExpanded(false)
            }
            previousOffset.intValue = scrollOffset
        }
    }
}

private enum class DashboardTab(
    @StringRes val title: Int,
) {
    DASHBOARD(R.string.home_dashboard),
    ACTIVITIES(R.string.home_activities),
}
