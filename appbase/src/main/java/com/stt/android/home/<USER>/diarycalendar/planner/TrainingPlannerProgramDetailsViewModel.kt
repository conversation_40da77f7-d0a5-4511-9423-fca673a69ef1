package com.stt.android.home.diary.diarycalendar.planner

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.diary.diarycalendar.planner.analytics.AiPlannerAnalytics
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramDetails
import com.stt.android.home.diary.diarycalendar.planner.models.DistanceMeasurement
import com.stt.android.home.diary.diarycalendar.planner.models.Option
import com.stt.android.home.diary.diarycalendar.planner.models.PaceMeasurement
import com.stt.android.home.diary.diarycalendar.planner.models.Question
import com.stt.android.home.diary.diarycalendar.planner.models.QuestionId
import com.stt.android.home.diary.diarycalendar.planner.models.SpeedMeasurement
import com.stt.android.home.diary.diarycalendar.planner.usercases.GenerateTrainingPlanUseCase
import com.stt.android.home.diary.diarycalendar.planner.usercases.GenerateTrainingPlanUseCaseResult
import com.stt.android.home.diary.diarycalendar.planner.usercases.GetWorkoutPlannerProgramDetailsByIdUseCase
import com.stt.android.home.diary.diarycalendar.planner.usercases.GetWorkoutPlannerProgramDetailsByIdUseCaseResult
import com.stt.android.utils.getBooleanExtra
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class TrainingPlannerProgramDetailsViewModel
@Inject constructor(
    private val getWorkoutPlannerProgramDetailsByIdUseCase: GetWorkoutPlannerProgramDetailsByIdUseCase,
    private val trainingPlannerUiStateMapper: TrainingPlannerUiStateMapper,
    private val generateTrainingPlanUseCase: GenerateTrainingPlanUseCase,
    private val measurementUnit: MeasurementUnit,
    private val analytics: AiPlannerAnalytics,
    savedStateHandle: SavedStateHandle,
) : ViewModel() {

    private var _questions: List<Question> = emptyList()
    private var _version: String = ""
    private var _trainingPlannerProgramDetails: TrainingPlannerProgramDetails? = null

    private val _uiState = MutableStateFlow<ProgramDetails>(
        ProgramDetails.Loading
    )
    val uiState = _uiState.asStateFlow()

    private val programId = savedStateHandle.get<String?>(TrainingPlannerNavArgs.META_PROGRAM_ID)
        ?: throw IllegalStateException("programId is not provided")

    private val enablePersonalize = savedStateHandle.getBooleanExtra(TrainingPlannerNavArgs.ENABLE_PERSONALIZE, false)

    private val _networkError = MutableStateFlow(false)
    val networkError = _networkError.asStateFlow()

    private val _unknownError = MutableStateFlow(false)
    val unknownError = _unknownError.asStateFlow()

    init {
        loadProgramDetails(programId = programId)
    }

    private fun loadProgramDetails(programId: String) {
        if (programId.isBlank()) {
            _uiState.value = ProgramDetails.InvalidProgramId
        } else {
            _uiState.value = ProgramDetails.Loading
            viewModelScope.launch {
                when (val result = getWorkoutPlannerProgramDetailsByIdUseCase(programId)) {
                    is GetWorkoutPlannerProgramDetailsByIdUseCaseResult.Data -> {
                        analytics.trackProgramDescriptionScreen(
                            programId = programId,
                            programName = result.programDetails.name
                        )
                        _questions = result.programDetails.questionnaire.questions
                        _version = result.programDetails.version
                        _trainingPlannerProgramDetails = result.programDetails
                        _uiState.value = ProgramDetails.Details.Info(
                            programDetailsUiState = trainingPlannerUiStateMapper.toProgramDetailsUiState(
                                details = result.programDetails,
                                enablePersonalize = enablePersonalize,
                            )
                        )
                    }

                    GetWorkoutPlannerProgramDetailsByIdUseCaseResult.NetworkError -> {
                        _uiState.value = ProgramDetails.NetworkError
                    }

                    GetWorkoutPlannerProgramDetailsByIdUseCaseResult.UnknownError -> {
                        _uiState.value = ProgramDetails.UnknownError
                    }
                }
            }
        }
    }

    fun retry() {
        loadProgramDetails(programId = programId)
    }

    private val _questionAnswered = MutableSharedFlow<Unit>()
    val questionAnswered = _questionAnswered.asSharedFlow()

    fun showSurvey() {
        if (programId.isBlank()) {
            _uiState.value = ProgramDetails.InvalidProgramId
        } else {
            viewModelScope.launch {
                if (_questions.isEmpty()) {
                    _uiState.value = ProgramDetails.Details.NoSurveyAvailable
                } else {
                    analytics.trackPlanSurveyStarted(
                        planId = _trainingPlannerProgramDetails?.id ?: "",
                        planName = _trainingPlannerProgramDetails?.name ?: "",
                        planDuration = _trainingPlannerProgramDetails?.durationWeeks ?: 0
                    )
                    _uiState.value = ProgramDetails.Details.Survey.AnswerQuestions(
                        questions = _questions,
                        internalCurrentQuestionId = _questions.first().id,
                        onContinueClick = ::onContinueClick,
                        onBackClick = ::onBackClick,
                        version = _trainingPlannerProgramDetails?.questionnaire?.version ?: ""
                    )
                }
            }
        }
    }

    private fun updateQuestion(questionId: String, block: (question: Question) -> Question) {
        val survey = _uiState.value as? ProgramDetails.Details.Survey ?: return
        val questionIndex = survey.questions.indexOfFirst { questionId == it.id }
        if (questionIndex == -1) return

        val updatedQuestion = block(survey.questions[questionIndex])

        _uiState.value = when (val state = _uiState.value) {
            is ProgramDetails.Details.Survey.AnswerQuestions -> state.copy(
                questions = state.questions.toMutableList().apply {
                    set(questionIndex, updatedQuestion)
                }
            )

            is ProgramDetails.Details.Survey.EditQuestion -> state.copy(
                questions = state.questions.toMutableList().apply {
                    set(questionIndex, updatedQuestion)
                }
            )

            is ProgramDetails.Details.Survey.Summary -> {
                _uiState.value
            }

            ProgramDetails.Details.NoSurveyAvailable -> _uiState.value
            is ProgramDetails.Details.Info -> _uiState.value
            ProgramDetails.InvalidProgramId -> _uiState.value
            ProgramDetails.Loading -> _uiState.value
            ProgramDetails.NetworkError -> _uiState.value
            ProgramDetails.UnknownError -> _uiState.value
        }
        viewModelScope.launch {
            _questionAnswered.emit(Unit)
        }
    }

    fun onOptionsSelected(options: List<Option>, questionId: QuestionId) {
        updateQuestion(questionId) { question ->
            when (question) {
                is Question.SingleChoice -> {
                    if (options.isNotEmpty()) { // Sanity check
                        question.copy(answer = options.first())
                    } else {
                        question
                    }
                }

                is Question.MultiChoice -> {
                    question.copy(answers = options)
                }

                else -> handleUnsupportedOperationForQuestion("onOptionsSelected", question)
            }
        }
    }

    private fun onContinueClick() {
        val newState = when (val state = _uiState.value) {
            is ProgramDetails.Details.Survey.AnswerQuestions -> {
                if (state.internalCurrentQuestionIndex < (state.totalNumberOfQuestions - 1)) {
                    state.copy(
                        internalCurrentQuestionId = state.questions[state.internalCurrentQuestionIndex + 1].id
                    )
                } else {
                    ProgramDetails.Details.Survey.Summary(
                        questions = state.questions,
                        version = state.version
                    )
                }
            }

            is ProgramDetails.Details.Survey.EditQuestion -> {
                ProgramDetails.Details.Survey.Summary(
                    questions = state.questions,
                    version = state.version
                )
            }

            else -> {
                return
            }
        }

        _uiState.value = newState
    }

    private fun onBackClick() {
        when (val state = _uiState.value) {
            is ProgramDetails.Details.Survey.AnswerQuestions -> {
                if (state.internalCurrentQuestionIndex > 0) {
                    _uiState.value = state.copy(
                        internalCurrentQuestionId = state.questions[state.internalCurrentQuestionIndex - 1].id
                    )
                } else {
                    _trainingPlannerProgramDetails?.let {
                        _uiState.value = ProgramDetails.Details.Info(
                            trainingPlannerUiStateMapper.toProgramDetailsUiState(
                                details = it,
                                enablePersonalize = enablePersonalize,
                            )
                        )
                    }
                }
            }

            else -> {}
        }
    }

    fun onCancelSurveyClick() {
        _trainingPlannerProgramDetails?.let {
            _uiState.value = ProgramDetails.Details.Info(
                trainingPlannerUiStateMapper.toProgramDetailsUiState(
                    details = it,
                    enablePersonalize = enablePersonalize,
                )
            )
        }
    }

    fun onNumberChange(value: Int?, questionId: QuestionId) {
        updateQuestion(questionId) { question ->
            when (question) {
                is Question.ValueInput.Number -> question.copy(value = value)
                else -> handleUnsupportedOperationForQuestion("onNumberChange", question)
            }
        }
    }

    fun onDistanceChange(value: Int, questionId: QuestionId) {
        updateQuestion(questionId) { question ->
            when (question) {
                is Question.ValueInput.Distance -> question.copy(
                    value = DistanceMeasurement(
                        meters = measurementUnit.fromDistanceUnit(
                            value.toDouble()
                        ).toInt()
                    )
                )

                else -> handleUnsupportedOperationForQuestion("onDistanceChange", question)
            }
        }
    }

    fun onDurationChange(hours: Int?, minutes: Int?, questionId: QuestionId) {
        updateQuestion(questionId) { question ->
            when (question) {
                is Question.ValueInput.Duration -> question.copy(hours = hours, minutes = minutes)
                else -> handleUnsupportedOperationForQuestion("onDurationChange", question)
            }
        }
    }

    fun onPaceChange(value: PaceMeasurement, questionId: QuestionId) {
        updateQuestion(questionId) { question ->
            when (question) {
                is Question.ValueInput.Pace -> question.copy(value = value)
                else -> handleUnsupportedOperationForQuestion("onPaceChange", question)
            }
        }
    }

    fun onSpeedChange(value: SpeedMeasurement, questionId: QuestionId) {
        updateQuestion(questionId) { question ->
            when (question) {
                is Question.ValueInput.Speed -> question.copy(value = value)
                else -> handleUnsupportedOperationForQuestion("onSpeedChange", question)
            }
        }
    }

    fun onDateChange(valueInMillis: Long?, questionId: QuestionId) {
        updateQuestion(questionId) { question ->
            when (question) {
                is Question.ValueInput.Date -> question.copy(valueInMillis = valueInMillis)
                else -> handleUnsupportedOperationForQuestion("onDateChange", question)
            }
        }
    }

    fun onSportsChange(selectedSports: List<CoreActivityType>, questionId: QuestionId) {
        updateQuestion(questionId) { question ->
            when (question) {
                is Question.Sports -> question.copy(selectedSports = selectedSports)
                else -> handleUnsupportedOperationForQuestion("onSportsChange", question)
            }
        }
    }

    fun onSummaryItemClick(questionId: QuestionId) {
        val survey = _uiState.value as? ProgramDetails.Details.Survey ?: return
        _uiState.value = ProgramDetails.Details.Survey.EditQuestion(
            questions = survey.questions,
            questionIdToEdit = questionId,
            onDoneClick = {
                val newSurvey =
                    _uiState.value as? ProgramDetails.Details.Survey ?: return@EditQuestion
                _uiState.value = ProgramDetails.Details.Survey.Summary(
                    questions = newSurvey.questions,
                    version = newSurvey.version
                )
            },
            version = survey.version
        )
    }

    @Throws(IllegalStateException::class)
    private fun handleUnsupportedOperationForQuestion(
        operation: String,
        question: Question
    ): Nothing {
        val tmp = "$operation is not allowed for ${question::class.simpleName}"
        throw IllegalStateException(tmp)
    }

    fun onConfirmClick(onAnswersSubmitted: () -> Unit) {
        analytics.trackPlanSurveySubmitted(
            planId = _trainingPlannerProgramDetails?.id ?: "",
            planName = _trainingPlannerProgramDetails?.name ?: "",
            planDuration = _trainingPlannerProgramDetails?.durationWeeks ?: 0,
        )
        sendAnswers(onAnswersSubmitted)
    }

    private fun sendAnswers(onAnswersSubmitted: () -> Unit) {
        val survey = _uiState.value as? ProgramDetails.Details.Survey ?: return
        viewModelScope.launch {
            val response = generateTrainingPlanUseCase.invoke(
                planId = programId,
                planVersion = _version,
                questionnaireVersion = survey.version,
                answers = survey.questions
            )

            when (response) {
                is GenerateTrainingPlanUseCaseResult.AnswersSubmitted -> {
                    onAnswersSubmitted()
                }

                GenerateTrainingPlanUseCaseResult.NetworkError -> {
                    _networkError.value = true
                }

                GenerateTrainingPlanUseCaseResult.UnknownError -> {
                    _unknownError.value = true
                }
            }
        }
    }

    fun resetNetworkErrorFlag() {
        _networkError.value = false
    }

    fun resetUnknownErrorFlag() {
        _unknownError.value = false
    }
}
