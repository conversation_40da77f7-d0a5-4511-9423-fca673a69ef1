package com.stt.android.home.diary.diarycalendar.planner.composables

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.home.diary.diarycalendar.planner.ProgramDetails
import com.stt.android.home.diary.diarycalendar.planner.models.Option
import com.stt.android.home.diary.diarycalendar.planner.models.PaceMeasurement
import com.stt.android.home.diary.diarycalendar.planner.models.QuestionId
import com.stt.android.home.diary.diarycalendar.planner.models.SpeedMeasurement
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList

@Composable
fun Survey(
    survey: ProgramDetails.Details.Survey,
    onConfirmClick: () -> Unit,
    onCancelClick: () -> Unit,
    onOptionsSelected: (List<Option>, QuestionId) -> Unit,
    onNumberChange: (Int?, QuestionId) -> Unit,
    onDistanceChange: (Int, QuestionId) -> Unit,
    onDurationChange: (Int?, Int?, QuestionId) -> Unit,
    onPaceChange: (PaceMeasurement, QuestionId) -> Unit,
    onSpeedChange: (SpeedMeasurement, QuestionId) -> Unit,
    onDateChange: (Long?, QuestionId) -> Unit,
    onSummaryItemClick: (questionId: QuestionId) -> Unit,
    onSportsChange: (ImmutableList<CoreActivityType>, QuestionId) -> Unit,
    modifier: Modifier = Modifier
) {
    val confirmedCancelClick = rememberConfirmedCancelClick(onCancelClick)

    Box(modifier = modifier.fillMaxSize()) {
        M3AppTheme {
            when (survey) {
                is ProgramDetails.Details.Survey.AnswerQuestions -> {
                    BackHandler {
                        if (survey.isFirstQuestion) {
                            survey.onBackClick()
                        } else {
                            confirmedCancelClick()
                        }
                    }

                    QuestionContainer(
                        currentQuestion = survey.currentQuestion,
                        currentQuestionIndex = survey.externalQuestionIndex,
                        totalQuestionsCount = survey.totalNumberOfQuestions,
                        onOptionsSelected = onOptionsSelected,
                        onBackClick = if (survey.isFirstQuestion) {
                            survey.onBackClick
                        } else {
                            confirmedCancelClick
                        },
                        onNumberChange = onNumberChange,
                        onDistanceChange = onDistanceChange,
                        onDurationChange = onDurationChange,
                        onPaceChange = onPaceChange,
                        onSpeedChange = onSpeedChange,
                        onDateChange = onDateChange,
                        onSportsChange = onSportsChange,
                        onCancelClick = confirmedCancelClick
                    )
                }

                is ProgramDetails.Details.Survey.EditQuestion -> {
                    EditQuestionContainer(
                        currentQuestion = survey.currentQuestionToEdit,
                        onOptionsSelected = onOptionsSelected,
                        onNumberChange = onNumberChange,
                        onDistanceChange = onDistanceChange,
                        onDurationChange = onDurationChange,
                        onPaceChange = onPaceChange,
                        onSpeedChange = onSpeedChange,
                        onDateChange = onDateChange,
                        onSportsChange = onSportsChange,
                        onCancelClick = confirmedCancelClick
                    )
                }

                is ProgramDetails.Details.Survey.Summary -> {
                    BackHandler {
                        confirmedCancelClick()
                    }
                    TrainingPlannerSummary(
                        questions = survey.questions.toImmutableList(),
                        onSummaryItemClick = onSummaryItemClick,
                        onStartThisProgramClick = onConfirmClick,
                        onCancelClick = confirmedCancelClick
                    )
                }
            }
        }
    }
}

@Composable
fun rememberConfirmedCancelClick(onConfirmed: () -> Unit): () -> Unit {
    var showDialog by rememberSaveable { mutableStateOf(false) }

    if (showDialog) {
        AlertDialog(
            onDismissRequest = { showDialog = false },
            title = {
                Text(
                    text = stringResource(id = R.string.workout_planner_cancel_survey_title),
                    style = MaterialTheme.typography.bodyXLargeBold,
                )
            },
            text = {
                Text(
                    text = stringResource(id = R.string.workout_planner_cancel_survey_message),
                    style = MaterialTheme.typography.bodyLarge,
                )
            },
            confirmButton = {
                TextButton(onClick = {
                    showDialog = false
                    onConfirmed()
                }) {
                    Text(
                        text = stringResource(id = R.string.workout_planner_cancel_survey_cancel),
                        style = MaterialTheme.typography.bodyLargeBold,
                        color = MaterialTheme.colorScheme.primary,
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = { showDialog = false }) {
                    Text(
                        text = stringResource(id = R.string.workout_planner_cancel_survey_go_back),
                        style = MaterialTheme.typography.bodyLargeBold,
                        color = MaterialTheme.colorScheme.secondary,
                    )
                }
            },
            containerColor = MaterialTheme.colorScheme.surface,
        )
    }

    return { showDialog = true }
}
