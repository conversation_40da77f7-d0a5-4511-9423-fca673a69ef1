package com.stt.android.offlinemaps.entity

import androidx.compose.runtime.Immutable
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf

sealed interface OfflineRegionResult : ResultId {
    @Immutable
    data class OfflineRegion(
        override val id: String,
        val name: String,
        val sizes: ImmutableMap<OfflineMapDownloadTarget, Size>,
        val area: Double?,
        val areaUnitRes: Int?,
        val description: String?,
        val bounds: LatLngBounds?,
        val boundaryUrl: String?,
        val maskUrl: String?,
        val downloadOrders: ImmutableList<DownloadOrder>,
        val styleIds: ImmutableList<String>,
        val groupName: String? = null,
        val adjacentRegions: ImmutableList<String> = persistentListOf(),
        val adjacentMaskUrl: String? = null,
        val centerPoint: LatLng? = null,
        val batchDownloadAllowed: Boolean? = null
    ) : OfflineRegionResult {
        data class Size(
            val storageSizeInBytes: Long?,
            val transferSizeInBytes: Long?,
        )

        val sizeForWatch: Size? get() = sizes[OfflineMapDownloadTarget.WATCH]

        val sizeForMobile: Size? get() = sizes[OfflineMapDownloadTarget.MOBILE]

        val downloadRequested: Boolean get() = downloadOrders.any { it.status == OfflineRegionStatus.REQUESTED }

        val downloading: Boolean get() = downloadOrders.any { downloadOrder ->
            downloadOrder.status == OfflineRegionStatus.IN_PROGRESS ||
                downloadOrder.status == OfflineRegionStatus.UPDATE_IN_PROGRESS
        }

        val downloadingUpdate: Boolean get() = downloadOrders.any { it.status == OfflineRegionStatus.UPDATE_IN_PROGRESS }

        val downloaded: Boolean get() = downloadOrders.any { downloadOrder -> downloadOrder.downloaded }

        val downloadedForMobile: Boolean get() = downloadOrders.any { downloadOrder ->
            downloadOrder.downloaded && downloadOrder.sourceTileType?.isForMobile() == true
        }

        val downloadedForWatch: Boolean get() = downloadOrders.any { downloadOrder ->
            downloadOrder.downloaded && downloadOrder.sourceTileType?.isForWatch() == true
        }

        val downloadFailed: Boolean get() = downloadOrders.any { it.status == OfflineRegionStatus.FAILED }

        val deleteRequested: Boolean get() = downloadOrders.any { it.status == OfflineRegionStatus.DELETE_REQUESTED }

        val downloadAvailable: Boolean get() = downloadOrders.isEmpty() ||
            downloadOrders.any { downloadOrder ->
                when (downloadOrder.status) {
                    OfflineRegionStatus.DELETE_FINISHED,
                    OfflineRegionStatus.FAILED -> true

                    OfflineRegionStatus.REQUESTED,
                    OfflineRegionStatus.IN_PROGRESS,
                    OfflineRegionStatus.FINISHED,
                    OfflineRegionStatus.DELETE_REQUESTED,
                    OfflineRegionStatus.UPDATE_AVAILABLE,
                    OfflineRegionStatus.UPDATE_IN_PROGRESS -> false
                }
            }

        val updateAvailable: Boolean get() = downloadOrders.any { it.status == OfflineRegionStatus.UPDATE_AVAILABLE }

        val cancellable: Boolean get() = downloadRequested || (downloading && !downloadingUpdate) || deleteRequested

        val downloadProgress: Float get() {
            // TODO 187306: Support offline maps on mobile
            val downloaded = downloadOrders.firstOrNull()?.downloadedSize
            val transferSize = sizeForWatch?.transferSizeInBytes
            return if (downloaded == null || transferSize == null || transferSize == 0L) {
                0f
            } else {
                (downloaded / transferSize.toFloat()).coerceAtMost(1f)
            }
        }

        companion object {
            val EMPTY = OfflineRegion(
                id = "_empty_region_",
                name = "",
                sizes = persistentMapOf(),
                area = null,
                areaUnitRes = null,
                description = null,
                bounds = null,
                boundaryUrl = null,
                maskUrl = null,
                styleIds = persistentListOf(),
                downloadOrders = persistentListOf(),
            )
        }
    }

    @Immutable
    data class OfflineRegionGroup(
        override val id: String,
        val name: String,
        val size: Long,
        val regions: ImmutableList<OfflineRegion>,
        val batchDownloadAllowed: Boolean? = null
    ) : OfflineRegionResult {
        companion object {
            val EMPTY = OfflineRegionGroup(
                id = "_empty_region_group_",
                name = "",
                size = 0,
                regions = persistentListOf(),
            )
        }
    }
}

interface ResultId {
    val id: String
}
