package com.stt.android.home.futuredayview

import android.content.Context
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.R
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.data.toEpochMilli
import com.stt.android.home.diary.diarycalendar.planner.TrainingPlannerFullscreenActivity
import com.stt.android.home.diary.diarycalendar.planner.domain.models.PlannedWorkout
import com.stt.android.home.diary.diarycalendar.planner.models.PlannedWorkoutUiState
import com.stt.android.home.diary.diarycalendar.planner.models.WorkoutTargetsUiState
import com.stt.android.home.diary.diarycalendar.planner.models.toViewData
import com.stt.android.ui.components.workout.WorkoutCard
import com.stt.android.ui.utils.TextFormatter
import java.time.LocalDate
import java.time.ZoneId

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FutureDayViewScreen(
    onNavigateUp: () -> Unit,
    onViewMyPlanClick: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: FutureDayViewViewModel = hiltViewModel(),
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current

    val onWorkoutTap = { uiState: PlannedWorkoutUiState ->
        val intent = TrainingPlannerFullscreenActivity.showPlannedWorkout(context, uiState)
        context.startActivity(intent)
    }

    FutureDayViewScreenContent(
        uiState = uiState,
        onNavigateUp = onNavigateUp,
        onWorkoutTap = onWorkoutTap,
        onViewMyPlanClick = onViewMyPlanClick,
        modifier = modifier
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FutureDayViewScreenContent(
    uiState: FutureDayViewUiState,
    onNavigateUp: () -> Unit,
    onWorkoutTap: (PlannedWorkoutUiState) -> Unit,
    onViewMyPlanClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current

    Scaffold(
        modifier = modifier,
        topBar = {
            val title = when (val state = uiState) {
                FutureDayViewUiState.Initial -> ""
                is FutureDayViewUiState.Loaded -> formatDateTitle(state.date, context)
            }

            SuuntoTopBar(
                title = title,
                onNavigationClick = onNavigateUp
            )
        },
        contentWindowInsets = WindowInsets.systemBars.only(WindowInsetsSides.Horizontal + WindowInsetsSides.Top)
    ) { padding ->
        Surface(
            modifier = Modifier
                .padding(padding)
                .narrowContentWithBgColors(
                    backgroundColor = MaterialTheme.colorScheme.surface,
                    outerBackgroundColor = MaterialTheme.colorScheme.background
                )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = MaterialTheme.spacing.medium)
                    .verticalScroll(rememberScrollState()),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium)
            ) {
                when (val state = uiState) {
                    FutureDayViewUiState.Initial -> {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    }

                    is FutureDayViewUiState.Loaded -> {
                        Text(
                            text = stringResource(R.string.workout_planner_plan),
                            style = MaterialTheme.typography.bodyXLargeBold,
                        )

                        if (state.plannedWorkouts.isEmpty()) {
                            // Should not get here without having the planned workouts in cache or
                            // due to a programming error.
                        } else {
                            state.plannedWorkouts.forEach { uiState ->
                                WorkoutCard(
                                    viewData = uiState.toViewData(context),
                                    modifier = Modifier.fillMaxWidth(),
                                    onClick = { onWorkoutTap(uiState) },
                                )
                            }
                        }
                        TextButton(
                            onClick = onViewMyPlanClick,
                            modifier = Modifier
                                .align(Alignment.CenterHorizontally)
                                .padding(bottom = MaterialTheme.spacing.medium)
                        ) {
                            Text(
                                text = stringResource(R.string.workout_planner_view_my_plan).uppercase(),
                                style = MaterialTheme.typography.labelLarge
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun formatDateTitle(date: LocalDate, context: Context): String {
    val today = LocalDate.now()
    return when (date) {
        today.plusDays(1L) -> stringResource(R.string.tomorrow)
        else -> TextFormatter.formatDate(
            context,
            date.atStartOfDay(ZoneId.systemDefault()).toEpochMilli(),
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun FutureDayViewScreenPreview() {
    M3AppTheme {
        val workout1 = createSamplePlannedWorkout(
            id = "1",
            activityType = CoreActivityType.RUNNING,
            durationInSeconds = 3600,
            distanceInMeters = 10000,
            name = "Morning Run",
            notes = "Easy run with some intervals"
        )

        val workout2 = createSamplePlannedWorkout(
            id = "2",
            activityType = CoreActivityType.WALKING,
            durationInSeconds = 2700,
            distanceInMeters = 0,
            name = "Strength Session",
            notes = "Focus on upper body"
        )

        FutureDayViewScreenContent(
            uiState = FutureDayViewUiState.Loaded(
                date = LocalDate.now().plusDays(1),
                plannedWorkouts = listOf(
                    createSamplePlannedWorkoutUiState(workout1),
                    createSamplePlannedWorkoutUiState(workout2)
                ),
            ),
            onNavigateUp = {},
            onWorkoutTap = {},
            onViewMyPlanClick = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun FutureDayViewScreenEmptyPreview() {
    M3AppTheme {
        FutureDayViewScreenContent(
            uiState = FutureDayViewUiState.Loaded(
                date = LocalDate.now().plusDays(1),
                plannedWorkouts = emptyList(),
            ),
            onNavigateUp = {},
            onWorkoutTap = {},
            onViewMyPlanClick = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun FutureDayViewScreenLoadingPreview() {
    M3AppTheme {
        FutureDayViewScreenContent(
            uiState = FutureDayViewUiState.Initial,
            onNavigateUp = {},
            onWorkoutTap = {},
            onViewMyPlanClick = {}
        )
    }
}

private fun createSamplePlannedWorkout(
    id: String,
    activityType: CoreActivityType,
    durationInSeconds: Int,
    distanceInMeters: Int,
    name: String,
    notes: String
): PlannedWorkout {
    return PlannedWorkout(
        id = id,
        activityType = activityType,
        durationInSeconds = durationInSeconds,
        estimatedDistanceInMeters = distanceInMeters,
        intensityZone = 3,
        impacts = emptyList(),
        name = name,
        notes = notes,
        trainingDate = LocalDate.now().plusDays(1),
        trainingStressScore = 75,
        targetPace = null,
        targetHeartRate = null,
        targetPower = null,
        avgSpeed = if (distanceInMeters > 0) distanceInMeters / durationInSeconds.toDouble() else null
    )
}

private fun createSamplePlannedWorkoutUiState(workout: PlannedWorkout): PlannedWorkoutUiState {
    val hours = workout.durationInSeconds / 3600
    val minutes = (workout.durationInSeconds % 3600) / 60
    val formattedDuration = if (hours > 0) {
        "${hours}h ${minutes}m"
    } else {
        "${minutes}m"
    }

    val distanceKm = workout.estimatedDistanceInMeters / 1000.0
    val formattedDistance = if (distanceKm > 0) {
        String.format(java.util.Locale.US, "%.1f", distanceKm)
    } else {
        null
    }

    val workoutTargetsUiState = WorkoutTargetsUiState(
        duration = formattedDuration,
        distance = formattedDistance,
        distanceUnit = if (formattedDistance != null) com.stt.android.core.R.string.TXT_KM else null,
        trainingLoad = workout.trainingStressScore.toString(),
        trainingLoadUnit = R.string.workout_values_headline_tss,
        intensityZone = workout.intensityZone,
        impacts = workout.impacts
    )

    return PlannedWorkoutUiState(
        id = workout.id,
        date = "Tomorrow", // Simplified for preview
        activityNameRes = workout.activityType.nameRes,
        activityTypeId = workout.activityType.id,
        note = workout.notes,
        tss = workout.trainingStressScore.toString(),
        workoutTargetsUiState = workoutTargetsUiState
    )
}
