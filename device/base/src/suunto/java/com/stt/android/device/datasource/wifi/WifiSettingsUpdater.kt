package com.stt.android.device.datasource.wifi

import com.stt.android.device.datasource.suuntoplusguide.OnDeviceConnectedInitializer
import com.stt.android.device.domain.watchkey.SetWatchKeyForWatchUseCase
import com.stt.android.offlinemaps.datasource.OfflineRegionDataSource
import com.stt.android.offlinemaps.entity.OfflineMapDownloadTarget
import com.stt.android.offlinemaps.entity.OfflineRegionStatus
import com.stt.android.remote.BaseUrlWithoutApiVersion
import com.stt.android.usecases.location.LastKnownLocationUseCase
import com.stt.android.watch.offlinemaps.domain.GetNumberOfAreasUseCase
import com.stt.android.watch.wifi.datasource.WifiNetworksDataSource
import com.stt.android.watch.wifi.entity.WifiGeneralSettings
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration.Companion.hours

class WifiSettingsUpdater
@Inject constructor(
    private val wifiDataSource: WifiNetworksDataSource,
    private val getNumberOfAreasUseCase: GetNumberOfAreasUseCase,
    private val regionDataSource: OfflineRegionDataSource,
    private val setWatchKeyForWatchUseCase: SetWatchKeyForWatchUseCase,
    private val lastKnownLocationUseCase: LastKnownLocationUseCase,
    @BaseUrlWithoutApiVersion private val baseUrl: String,
) : OnDeviceConnectedInitializer {
    override suspend fun onDeviceConnected(
        serial: String,
        model: String,
        fwVersion: String,
        hwVersion: String,
        capabilities: List<String>
    ) = withContext(IO) {
        val hasWifi = capabilities.any { it.startsWith("hw_wifi") }
        if (!hasWifi) return@withContext // Wifi HW required

        try {
            enableInboxWifi(fwVersion)
            restoreStateOnFactoryReset(
                serial = serial,
                capabilities = capabilities.joinToString(separator = ",")
            )
            setBaseUrl()
            setWatchKeyForWatchUseCase.setToken()
            setCountryCode()
        } catch (e: Exception) {
            Timber.w(e, "Failed to update wifi settings to watch")
        }
    }

    /**
     * Suunto Vertical inbox fw has the wireless network setting unintentionally disabled. Enable
     * the setting if the connected device is Suunto Vertical and the fwVersion matches one of the
     * inbox sw versions.
     */
    private suspend fun enableInboxWifi(fwVersion: String) {
        if (fwVersion == "2.24.42" || fwVersion == "2.24.40") {
            try {
                wifiDataSource.enableInboxWifi()
                Timber.d("Enabled wireless network for Suunto Vertical inbox fw")
            } catch (e: Exception) {
                Timber.w(e, "Failed to enable wireless network for Suunto Vertical inbox fw")
            }
        }
    }

    private suspend fun restoreStateOnFactoryReset(serial: String, capabilities: String) {
        try {
            val count = getNumberOfAreasUseCase.run()
            val excludedStatus = listOf(
                OfflineRegionStatus.REQUESTED,
                OfflineRegionStatus.FAILED,
            )
            if (count > 0) {
                Timber.d("Areas found on watch: $count, skip state reset")
            } else {
                val cloudCount = regionDataSource.getLibrary(
                    downloadTarget = OfflineMapDownloadTarget.WATCH,
                    deviceSerial = serial,
                    capabilities = capabilities,
                ).sumOf { offlineRegion ->
                    offlineRegion.downloadOrders.count { downloadOrder ->
                        !excludedStatus.contains(downloadOrder.status)
                    }
                }
                if (cloudCount > 0) {
                    Timber.d("Watch and cloud area counts do not match. Resetting cloud state.")
                    regionDataSource.resetLibrary(
                        downloadTarget = OfflineMapDownloadTarget.WATCH,
                        deviceSerial = serial,
                        capabilities = capabilities
                    )
                } else {
                    Timber.d("Watch and cloud area counts match. Skip resetting cloud state.")
                }
            }
        } catch (e: Exception) {
            Timber.w(e, "Failed to restore state after factory reset")
        }
    }

    private suspend fun setBaseUrl() {
        wifiDataSource.setOfflineMapsUrl(baseUrl)
        Timber.d("Wifi settings: Base url set=$baseUrl")
    }

    private suspend fun setCountryCode() {
        val countryCode = getLastKnownLocation()
        if (countryCode != null) {
            wifiDataSource.setWifiGeneralSettings(WifiGeneralSettings(countryCode = countryCode))
            Timber.d("Country code set=$countryCode")
        } else {
            Timber.d("Failed to set country code")
        }
    }

    private suspend fun getLastKnownLocation(): String? = try {
        val hourAgo = System.currentTimeMillis() - 1.hours.inWholeMilliseconds
        lastKnownLocationUseCase.getLastKnownLocationAsCountryCode(
            skipPassiveProvider = false,
            timeInMilliSecondsSinceEpoch = hourAgo
        )
    } catch (e: Exception) {
        Timber.w(e, "Failed to update country code to watch")
        null
    }
}
