package com.stt.android.compose.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.LocalContentAlpha
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Typography
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.stt.android.FontRefs

@Composable
fun AppTheme(
    content: @Composable () -> Unit
) {
    // Purely compose based Material 2 theme (works nicely with @Preview)
    SuuntoAppLightTheme(content)
}

fun getDefaultFontFamily() = suuntoFontFamily
fun getCondensedFontFamily() = condensedFontFamily

internal val suuntoFontFamily = FontFamily(
    Font(FontRefs.DEFAULT_FONT_REF),
    Font(FontRefs.DEFAULT_FONT_BOLD_REF, FontWeight.Bold)
)

private val condensedFontFamily = FontFamily(
    Font(FontRefs.DEFAULT_CONDENSED_FONT)
)

private val typography = Typography(
    // Default font
    defaultFontFamily = suuntoFontFamily,

    // body1 is the typical body text size matching @style/Body.Larger with 4sp extra line spacing
    body1 = TextStyle(fontSize = 16.sp, lineHeight = 20.sp),

    // body2 is the smaller body text size matching @style/Body.Medium with 4sp extra line spacing
    body2 = TextStyle(fontSize = 14.sp, lineHeight = 18.sp),

    // h2
    h2 = TextStyle(fontSize = 14.sp, lineHeight = 24.sp, fontWeight = FontWeight.Bold),

    // h6 is used in titles in App bar
    h6 = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.Bold),

    // Button text style
    button = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.Bold),
)

internal object SuuntoColors {
    val nearBlack = Color(0xFF303030)
    val lightGrey = Color(0xFFE9ECEE)
    val darkGrey = Color(0xFF7E8084) // Not an official color in Figma but still widely used
    val suuntoBlue = Color(0xFF16B4EA)
    val brightRed = Color(0xFFff3333)
}

@Composable
private fun SuuntoAppLightTheme(
    content: @Composable () -> Unit
) {
    CompositionLocalProvider(LocalIsMaterial3 provides false) {
        MaterialTheme(
            colors = MaterialTheme.colors.copy(
                primary = SuuntoColors.suuntoBlue,
                primaryVariant = SuuntoColors.darkGrey,
                secondary = SuuntoColors.nearBlack,
                secondaryVariant = SuuntoColors.nearBlack,
                background = SuuntoColors.lightGrey,
                surface = Color.White,
                error = SuuntoColors.brightRed,
                onPrimary = Color.White,
                onSecondary = Color.White,
                onBackground = SuuntoColors.nearBlack,
                onSurface = SuuntoColors.nearBlack,
                onError = Color.White,
                isLight = true,
            ),
            typography = typography,
            content = {
                // MaterialTheme sets local content alpha to 0.87f (e.g.LowContrastContentAlpha.high),
                // which is something we don't want. Set LocalContentAlpha to 1f by default.
                CompositionLocalProvider(LocalContentAlpha provides 1f, content = content)
            },
            shapes = MaterialTheme.shapes.copy(
                medium = RoundedCornerShape(8.dp)
            )
        )
    }
}
